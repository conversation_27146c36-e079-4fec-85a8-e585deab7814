# Anchor Android 项目框架

这是一个基于 Jetpack Compose 的现代化 Android 应用框架，包含了网络请求、图片加载、导航管理和常用工具类等完整功能。

## 🏗️ 项目架构

### 技术栈
- **UI框架**: Jetpack Compose
- **网络请求**: Retrofit + OkHttp + Kotlinx Serialization
- **图片加载**: Coil
- **导航**: Navigation Compose
- **异步处理**: Kotlin Coroutines + Flow
- **架构模式**: MVVM

### 项目结构
```
app/src/main/java/com/github/anchor/
├── config/                 # 配置类
│   └── AppConfig.kt        # 应用配置常量
├── data/                   # 数据层
│   └── network/            # 网络相关
│       ├── ApiService.kt   # API 接口定义
│       ├── ApiResult.kt    # 网络请求结果封装
│       ├── NetworkClient.kt # 网络客户端配置
│       └── NetworkRepository.kt # 网络数据仓库
├── navigation/             # 导航相关
│   ├── NavigationGraph.kt  # 导航图
│   └── Screen.kt          # 页面路由定义
├── ui/                     # UI层
│   ├── components/         # 通用组件
│   │   ├── AsyncImageComponent.kt # 异步图片组件
│   │   └── BottomNavigationBar.kt # 底部导航栏
│   ├── screens/           # 页面
│   │   ├── home/          # 首页
│   │   ├── profile/       # 个人资料页
│   │   └── settings/      # 设置页
│   └── theme/             # 主题相关
└── utils/                 # 工具类
    ├── DateTimeUtils.kt   # 日期时间工具
    ├── LogX.kt        # 日志工具
    ├── NetworkUtils.kt    # 网络状态工具
    ├── PermissionUtils.kt # 权限管理工具
    ├── DataStoreManager.kt # DataStore 数据存储管理器
    └── StringUtils.kt     # 字符串工具
```

## 🚀 主要功能

### 1. 网络请求框架
- 基于 Retrofit + OkHttp 的网络请求
- 支持 JSON 序列化/反序列化
- 统一的错误处理机制
- 请求/响应日志记录
- 网络状态监听

### 2. 图片加载框架
- 基于 Coil 的异步图片加载
- 支持占位图和错误图
- 圆形头像、圆角图片等常用组件
- 加载状态指示器

### 3. 导航框架
- 基于 Navigation Compose
- 底部导航栏
- 类型安全的路由管理
- 页面间参数传递

### 4. 常用工具类
- **LogUtils**: 统一日志管理
- **DataStoreManager**: DataStore 数据存储管理器
- **DateTimeUtils**: 日期时间处理
- **StringUtils**: 字符串验证和处理
- **NetworkUtils**: 网络状态检测
- **PermissionUtils**: 权限管理

## 📱 使用示例

### 网络请求
```kotlin
class HomeViewModel : ViewModel() {
    private val repository = NetworkRepository()
    
    fun loadUsers() {
        viewModelScope.launch {
            repository.getUsers().collect { result ->
                when (result) {
                    is ApiResult.Loading -> {
                        // 显示加载状态
                    }
                    is ApiResult.Success -> {
                        // 处理成功结果
                        val users = result.data.users
                    }
                    is ApiResult.Error -> {
                        // 处理错误
                        val errorMessage = result.message
                    }
                }
            }
        }
    }
}
```

### 图片加载
```kotlin
// 普通图片
AsyncImageComponent(
    imageUrl = "https://example.com/image.jpg",
    modifier = Modifier.size(200.dp),
    contentDescription = "示例图片"
)

// 圆形头像
CircleAvatar(
    imageUrl = user.avatar,
    size = 48.dp
)

// 圆角图片
RoundedImage(
    imageUrl = imageUrl,
    cornerRadius = 12.dp,
    modifier = Modifier.fillMaxWidth()
)
```

### 导航使用
```kotlin
// 在 Composable 中导航
val navController = rememberNavController()

Button(
    onClick = { 
        navController.navigate(Screen.Profile.route)
    }
) {
    Text("跳转到个人资料")
}
```

### 工具类使用
```kotlin
// 日志记录
LogX.d("这是一条调试日志")
LogX.e("这是一条错误日志", exception)

// 数据存储
DataStoreManager.putString("key", "value") // 需要在协程中调用
val value = DataStoreManager.getString("key") // 需要在协程中调用

// 时间格式化
val timeStr = DateTimeUtils.formatTimestamp(timestamp)
val friendlyTime = DateTimeUtils.getFriendlyTime(timestamp)

// 字符串验证
val isValidEmail = StringUtils.isValidEmail("<EMAIL>")
val hiddenPhone = StringUtils.hidePhoneNumber("13800138000")

// 网络状态检测
val networkUtils = NetworkUtils.getInstance(context)
val isConnected = networkUtils.isNetworkAvailable()
val networkType = networkUtils.getCurrentNetworkType()
```

## 🔧 配置说明

### 网络配置
在 `NetworkClient.kt` 中修改 BASE_URL：
```kotlin
private const val BASE_URL = "https://your-api-domain.com/"
```

### 应用配置
在 `AppConfig.kt` 中修改各种配置常量：
```kotlin
object AppConfig {
    const val BASE_URL = "https://your-api.com/"
    const val CONNECT_TIMEOUT = 30L
    // ... 其他配置
}
```

## 📋 依赖说明

主要依赖库：
- `androidx.navigation:navigation-compose` - 导航
- `com.squareup.retrofit2:retrofit` - 网络请求
- `io.coil-kt:coil-compose` - 图片加载
- `org.jetbrains.kotlinx:kotlinx-serialization-json` - JSON 序列化
- `org.jetbrains.kotlinx:kotlinx-coroutines-android` - 协程

## 🎯 开发建议

1. **遵循单一职责原则**: 每个类和方法只负责一个功能
2. **使用 Flow 进行数据流管理**: 响应式编程，便于状态管理
3. **统一错误处理**: 使用 ApiResult 封装网络请求结果
4. **合理使用工具类**: 避免重复代码，提高开发效率
5. **注意内存泄漏**: 正确使用 ViewModel 和 Lifecycle

## 📝 注意事项

- 本框架不使用依赖注入，采用单例模式管理工具类
- 网络请求默认使用 JSON 格式
- 图片加载支持多种格式和缓存策略
- 所有工具类都是线程安全的
- 建议在 Debug 模式下启用详细日志

## 🔄 后续扩展

可以根据项目需求添加：
- 数据库支持 (Room)
- 推送通知
- 文件上传/下载
- 更多 UI 组件
- 国际化支持
- 主题切换
- 数据加密

---

这个框架为 Android 应用开发提供了一个坚实的基础，可以根据具体项目需求进行定制和扩展。
