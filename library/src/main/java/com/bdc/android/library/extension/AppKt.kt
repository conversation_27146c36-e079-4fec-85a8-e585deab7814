package com.bdc.android.library.extension


import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.os.Bundle
import android.os.Parcelable
import android.util.TypedValue
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.angcyo.dsladapter.DslAdapterItem
import com.bdc.android.library.utils.AppManager
import com.bdc.android.library.utils.ToastUtil
import com.bdc.android.library.widget.LoadingDialog
import com.bdc.android.library.widget.createLoadingDialog
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.impl.LoadingPopupView
import com.lxj.xpopup.util.SmartGlideImageLoader
import com.permissionx.guolindev.PermissionX
import java.io.Serializable
import kotlin.math.roundToInt

/**
 * Copyright (C), 2019-2021, 中传互动（湖北）信息技术有限公司
 * Author: HeChao
 * Date: 2021/6/8 14:31
 * Description: 常量组件扩展类
 */
inline fun <reified T : ViewModel> Activity.getViewModel(clazz: Class<T>): T {
    return ViewModelProvider.AndroidViewModelFactory.getInstance(
        AppManager.getApplication()
    ).create(clazz)
}

inline fun <reified T : ViewModel> BasePopupView.getViewModel(clazz: Class<T>): T {
    return ViewModelProvider.AndroidViewModelFactory.getInstance(
        AppManager.getApplication()
    ).create(clazz)
}

fun <T> Activity.jump(activity: Class<T>, intent: Bundle? = null) {
    startActivity(Intent(this, activity).apply {
        intent?.let { this.putExtras(it) }
    })
}

fun <T> Context.jump(activity: Class<T>, intent: Bundle? = null) {
    startActivity(Intent(this, activity).apply {
        intent?.let { this.putExtras(it) }
    })
}

fun Context.finish() {
    this.takeIf { context -> context is Activity }?.let { context -> context as Activity }?.finish()
}

fun <T> Activity.jumpThenFinish(activity: Class<T>, intent: Bundle? = null) {
    jump(activity, intent)
    finish()
}

fun <T> Context.jumpThenFinish(activity: Class<T>, intent: Bundle? = null) {
    jump(activity, intent)
    this.takeIf { context -> context is Activity }?.let { context -> context as Activity }?.finish()
}

fun <T> Fragment.jumpResult(activity: Class<T>, intent: Bundle? = null, requestCode: Int) {
    startActivityForResult(Intent(this.context, activity).apply {
        intent?.let { this.putExtras(it) }
    }, requestCode)
}

fun <T> Activity.jumpResult(activity: Class<T>, intent: Bundle? = null, requestCode: Int) {
    startActivityForResult(Intent(this, activity).apply {
        intent?.let { this.putExtras(it) }
    }, requestCode)
}

fun <T> Fragment.jump(activity: Class<T>, intent: Bundle? = null) {
    startActivity(Intent(this.context, activity).apply {
        intent?.let { this.putExtras(it) }
    })
}

fun <T> Fragment.jumpThenFinish(activity: Class<T>, intent: Bundle? = null) {
    jump(activity, intent)
    requireActivity().finish()
}

fun <T> DslAdapterItem.jump(activity: Class<T>, intent: Bundle? = null) {
    itemDslAdapter?._recyclerView?.context?.let { context ->
        context.startActivity(Intent(context, activity).apply {
            intent?.let { this.putExtras(it) }
        })
    }
}

fun <T> BasePopupView.jump(activity: Class<T>, intent: Bundle? = null) {
    context.startActivity(Intent(context, activity).apply {
        intent?.let { this.putExtras(it) }
    })
}

fun Bundle.putExtra(value: Bundle?): Bundle {
    putBundle("BUNDLE", value)
    return this
}

fun Bundle.getExtra(): Bundle? {
    return getBundle("BUNDLE")
}

fun Intent.putExtra(value: Bundle?): Intent {
    extras?.putExtra(value)
    return this
}

fun Intent.getExtra(): Bundle? {
    return getBundleExtra("BUNDLE")
}

fun Bundle.putId(value: String?): Bundle {
    putString("ID", value)
    return this
}

fun Bundle.getId(): String? {
    return getString("ID")
}

fun Intent.getId(): String? {
    return extras?.getId()
}

fun Bundle.putString(value: String?): Bundle {
    putString("DATA", value)
    return this
}

fun Bundle.getString(): String? {
    return getString("DATA")
}

fun Intent.getString(): String? {
    return extras?.getString()
}

fun Bundle.putTyped(value: String?): Bundle {
    putString("TYPE", value)
    return this
}

fun Bundle.putIntTyped(value: Int?): Bundle {
    putInt("INT_TYPE", value ?: 0)
    return this
}

fun Bundle.getTyped(): String? {
    return getString("TYPE")
}

fun Bundle.getIntTyped(): Int {
    return getInt("INT_TYPE", -1)
}

fun Intent.getTyped(): String? {
    return extras?.getTyped()
}

fun Intent.getIntTyped(): Int {
    return extras?.getIntTyped() ?: -1
}

fun Bundle.putIndex(value: Int): Bundle {
    putInt("INDEX", value)
    return this
}


fun Bundle.getIndex(): Int {
    return getInt("INDEX", -1)
}

fun Intent.getIndex(): Int {
    return extras?.getIndex() ?: -1
}

fun Intent.hasIndex(): Boolean {
    return extras?.containsKey("INDEX") ?: false
}

fun Bundle.putBoolean(value: Boolean): Bundle {
    putBoolean("BOOLEAN", value)
    return this
}

fun Bundle.getBoolean(): Boolean {
    return getBoolean("BOOLEAN", false)
}

fun Intent.getBoolean(): Boolean {
    return extras?.getBoolean() ?: false
}

fun Bundle.putLong(value: Long): Bundle {
    putLong("LONG", value)
    return this
}

fun Bundle.getLong(): Long {
    return getLong("LONG", -1L)
}

fun Intent.getLong(): Long {
    return extras?.getLong() ?: -1L
}

fun Bundle.putBean(bean: Serializable?): Bundle {
    putSerializable("BEAN", bean)
    return this
}

fun Bundle.getSerializable(): Serializable? {
    return getSerializable("BEAN")
}

inline fun <reified T> Bundle.getSerializable(): T? {
    return getSerializable() as T?
}

fun Intent.getSerializable(): Serializable? {
    return extras?.getSerializable()
}

inline fun <reified T> Intent.getSerializable(): T? {
    return getSerializable() as? T
}

fun Intent.hasBean(): Boolean {
    return hasExtra("BEAN")
}

fun Bundle.putArray(beans: Array<out Serializable>): Bundle {
    putSerializable("BEANS", beans)
    return this
}

inline fun <reified T> Bundle.getArray(): Array<T>? {
    return getSerializable("BEANS") as? Array<T>
}

inline fun <reified T> Intent.getArray(): Array<T>? {
    return extras?.getSerializable("BEANS") as? Array<T>
}

fun Bundle.putBean(bean: Parcelable): Bundle {
    putParcelable("BEAN", bean)
    return this
}

fun <T> Bundle.getParcelable(): T? {
    return getParcelable("BEAN") as? T
}

fun <T> Intent.getParcelable(): T? {
    return extras?.getParcelable<T>()
}

fun Float.dp2px(): Int {
    return TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP, this, Resources.getSystem().displayMetrics
    ).toInt()
}

fun Float.px2dp(): Int {
    val scale: Float = Resources.getSystem().displayMetrics.scaledDensity
    return ((this / scale + 0.5f).roundToInt())
}

fun Float.px2sp(): Float {
    val fontScale: Float = Resources.getSystem().displayMetrics.scaledDensity
    return (this / fontScale + 0.5f)
}

fun Context.toast(msg: String) {
    ToastUtil.show(msg)
}

fun Fragment.toast(msg: String) {
    ToastUtil.show(msg)
}

fun Activity.loading(msg: String = "", isOutDismiss: Boolean = true): LoadingDialog {
    return createLoadingDialog(this, title = msg, canceledOnTouchOutside = isOutDismiss)
}

fun Fragment.loading(msg: String? = ""): LoadingPopupView {
    return XPopup.Builder(requireContext()).dismissOnTouchOutside(true).dismissOnBackPressed(false)
        .asLoading().setTitle(msg)
}

fun Context.color(color: Int): Int {
    return ContextCompat.getColor(this, color)
}

@SuppressLint("CheckResult")
fun FragmentActivity.requestPermissions(
    vararg permissions: String, block: (String, Boolean) -> Unit
) {
    PermissionX.init(this).permissions(*permissions)
        .request { allGranted, grantedList, deniedList ->
            grantedList.forEach { block(it, allGranted) }
        }
}

@SuppressLint("CheckResult")
fun Fragment.requestPermissions(
    vararg permissions: String, block: (String, Boolean) -> Unit
) {
    PermissionX.init(this).permissions(*permissions)
        .request { allGranted, grantedList, deniedList ->
            grantedList.forEach { block(it, allGranted) }
        }
}

fun Context.requestPermissions(
    vararg permissions: String, block: (String, Boolean) -> Unit
) {
    PermissionX.init(this as AppCompatActivity).permissions(*permissions)
        .request { allGranted, grantedList, deniedList ->
            grantedList.forEach { block(it, allGranted) }
        }
}

fun Activity.showImage(list: List<String>?, itemPosition: Int, view: ImageView) {
    XPopup.Builder(this).isDestroyOnDismiss(true)
        .asImageViewer(view, itemPosition, list, { _, _ -> }, object : SmartGlideImageLoader() {})
        .show()
}

fun Fragment.showImage(list: List<String>?, itemPosition: Int, view: ImageView) {
    XPopup.Builder(requireContext()).isDestroyOnDismiss(true)
        .asImageViewer(view, itemPosition, list, { _, _ -> }, object : SmartGlideImageLoader() {})
        .show()
}

fun <T> LiveData<T>.observeOnce(lifecycleOwner: LifecycleOwner?, observer: Observer<T>) {
    lifecycleOwner?.let {
        observe(it, object : Observer<T> {
            override fun onChanged(t: T) {
                removeObserver(this)
                observer.onChanged(t)
            }
        })
    }
}

