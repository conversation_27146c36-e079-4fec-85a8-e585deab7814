package com.bdc.android.library.utils

import android.app.Application
import android.os.Build
import android.os.Process
import java.io.BufferedReader
import java.io.FileReader

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/6/18 20:04
 * @description :
 */
object AppUtil {
    fun getProcessName(): String? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            Application.getProcessName()
        } else {
            getProcessNameCompat()
        }
    }

    private fun getProcessNameCompat(): String? {
        val pid = android.os.Process.myPid()
        try {
            BufferedReader(FileReader("/proc/$pid/cmdline")).use { reader ->
                val processName = reader.readLine()
                return processName.split(0.toChar())[0].trim() // 只保留第一个子串并去除多余空白
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    fun killApp() {
        ActivityManager.clearAll()
        Process.killProcess(Process.myPid())
        System.exit(0)
    }


}