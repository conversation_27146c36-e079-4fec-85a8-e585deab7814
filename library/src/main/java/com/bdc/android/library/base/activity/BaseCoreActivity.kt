package com.bdc.android.library.base.activity

import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.bdc.android.library.R
import com.bdc.android.library.event.MessageEvent
import com.bdc.android.library.provider.ContextWrapperProvider
import com.bdc.android.library.utils.ActivityManager
import com.bdc.android.library.utils.ClassUtil
import com.bdc.android.library.utils.Logger
import com.bdc.android.library.widget.XToolbar
import com.gyf.immersionbar.ImmersionBar
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.impl.LoadingPopupView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.lang.reflect.ParameterizedType

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * Author: HeChao
 * Date: 2021/12/15 12:28
 * Description:
 */
abstract class BaseCoreActivity<B : ViewBinding, VM : ViewModel> : AppCompatActivity() {
    protected val TAG = this::class.java.canonicalName ?: "BaseCoreActivity"
    protected lateinit var mBinding: B
    protected val mViewModel: VM by lazy {
//        ViewModelProvider.AndroidViewModelFactory.getInstance(
//            AppManager.getApplication()
//        ).create(ClassUtil.create<VM>(javaClass.genericSuperclass, 1))
        ViewModelProvider(this)[ClassUtil.create<VM>(javaClass.genericSuperclass, 1)]
    }

    private val mLoadingDialog: LoadingPopupView by lazy {
        XPopup.Builder(this).dismissOnTouchOutside(false).dismissOnBackPressed(false).asLoading()
    }

    private var mToolbar: XToolbar? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val canonicalName = this.javaClass.canonicalName
        //release版本 登录及实名认证禁止截屏
        Logger.i("current activity $canonicalName")
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        if (getLayoutId() > 0) {
            initBinding()
        }
//        initActivityManager()
        initImmersionBar()
        initView()
        bindListener()
        initViewStates()
        initViewEvents()
        initData()
    }

    override fun attachBaseContext(newBase: Context) {
        val wrapped = ContextWrapperProvider.wrap?.invoke(newBase) ?: newBase
        super.attachBaseContext(wrapped)
    }

    fun setToolbarTitle(title: String) {
        mToolbar?.setTitle(title)
    }

    fun setNavigationImage(drawable: Int, block: () -> Boolean = { false }) {
        mToolbar?.setNavigationOnClickListener(drawable) {
            if (!block()) {
                finish()
            }
        }
    }

    /**
     * 初始化binding
     * */
    private fun initBinding() {
        mBinding = bindingInflater(layoutInflater)
        setContentView(mBinding.root)

        mToolbar = mBinding.root.findViewWithTag<XToolbar>(XToolbar::class.java.name)?.apply {
            setNavigationImage(
                if (isDarkStatusBar()) ContextCompat.getDrawable(
                    this.context, R.mipmap.ic_back_black
                ) else ContextCompat.getDrawable(this.context, R.mipmap.ic_back_white)
            )
        }
    }

    private fun bindingInflater(layoutInflater: LayoutInflater): B {
        val viewBindingClass = getGenericViewBindingClass()
        val inflateMethod = viewBindingClass.getMethod("inflate", LayoutInflater::class.java)
        @Suppress("UNCHECKED_CAST") return inflateMethod.invoke(null, layoutInflater) as B
    }

    private fun getGenericViewBindingClass(): Class<B> {
        var genericSuperclass = javaClass.genericSuperclass
        while (genericSuperclass !is ParameterizedType) {
            genericSuperclass = (genericSuperclass as Class<*>).genericSuperclass
        }
        return genericSuperclass.actualTypeArguments[0] as Class<B>
    }

    /**
     * 初始化状态栏
     * */
    private fun initImmersionBar() {
        if (!useSystemToolbar()) {
            val immersionBar = ImmersionBar.with(this).statusBarDarkFont(isDarkStatusBar())
                .navigationBarColor(getNavigationBarColor()).keyboardEnable(keyboardEnable())
            if (!isImmerse()) {
                immersionBar.fitsSystemWindows(isFitsSystemWindows())
                    .statusBarColor(getStatusBarColor())
            }
            immersionBar.init()
        } else {
            supportActionBar?.setDisplayHomeAsUpEnabled(true)
        }

    }

    /**
     * 初始化ActivityManager
     * */
    private fun initActivityManager() {
        ActivityManager.addActivity(this)
    }

    /**
     * 初始化
     * */
    open fun initView() {}
    open fun initData() {}
    open fun initViewStates() {} //初始化数据状态监听
    open fun initViewEvents() {}
    open fun bindListener() {}

    open fun showLoading(message: String?) {
        if (!mLoadingDialog.isShown) {
            if (TextUtils.isEmpty(message)) {
                mLoadingDialog.show()
            } else {
                mLoadingDialog.setTitle(message).show()
            }
        }
    }

    open fun stopLoading() {
//        if (mLoadingDialog.isShown) {
        mLoadingDialog.dismiss()
//        }
    }

    override fun onStart() {
        super.onStart()
        if (isRegistered() && !EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    open fun onMessageEvent(event: MessageEvent) {
    }

    abstract fun getLayoutId(): Int
    open fun useSystemToolbar(): Boolean = false
    open fun keyboardEnable(): Boolean = false
    open fun isImmerse(): Boolean = false
    open fun getStatusBarColor(): Int = R.color.color_status_bar
    open fun isDarkStatusBar(): Boolean = false
    open fun isRegistered(): Boolean = false
    open fun getNavigationBarColor(): Int = R.color.color_navigation_bar
    open fun isFitsSystemWindows(): Boolean = true

    override fun onDestroy() {
        super.onDestroy()
//        ActivityManager.removeActivity(this)
        if (isRegistered() && EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}