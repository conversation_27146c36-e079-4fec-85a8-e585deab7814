package com.bdc.android.library.utils

import com.bdc.android.library.BuildConfig
import com.orhanobut.logger.AndroidLogAdapter
import com.orhanobut.logger.Logger

/**
 * Copyright (C), 2020-2021, 中传互动（湖北）信息技术有限公司
 * @Author: pym
 * @Date: 2021/6/7 11:07
 * @Description:
 */
object Logger {
    private var TAG = Logger::class.java.canonicalName
    private var isDebug = BuildConfig.DEBUG

    @JvmStatic
    fun init(tag: String, debug: Boolean = BuildConfig.DEBUG) {
        isDebug = debug
        if (isDebug) {
            TAG = tag
            Logger.addLogAdapter(AndroidLogAdapter())
        }
    }

    @JvmStatic
    fun i(str: String?) {
        if (isDebug) {
//            Log.i(TAG, str!!)
            Logger.i(str ?: "")
        }
    }

    @JvmStatic
    fun i(tag: String?, str: String?) {
        if (isDebug) {
            Logger.i(str ?: "", tag)
        }
    }

    @JvmStatic
    fun v(str: String?) {
        if (isDebug) {
            Logger.v(str ?: "")
        }
    }

    @JvmStatic
    fun v(tag: String?, str: String?) {
        if (isDebug) {
            Logger.v(str ?: "", tag)
        }
    }

    @JvmStatic
    fun d(str: String?) {
        if (isDebug) {
            Logger.d(str ?: "")
        }
    }

    @JvmStatic
    fun d(tag: String?, str: String?) {
        if (isDebug) {
            Logger.d(str ?: "", tag)
        }
    }

    @JvmStatic
    fun w(str: String?) {
        if (isDebug) {
            Logger.w(str ?: "")
        }
    }

    @JvmStatic
    fun w(tag: String?, str: String?) {
        if (isDebug) {
            Logger.w(str ?: "", tag)
        }
    }

    @JvmStatic
    fun e(str: String?) {
        if (isDebug) {
            Logger.e(str ?: "")
        }
    }

    @JvmStatic
    fun e(tag: String?, str: String?) {
        if (isDebug) {
            Logger.e(str ?: "", tag)
        }
    }

    @JvmStatic
    fun json(str: String?) {
        if (isDebug) {
            Logger.json(str)
        }
    }
}
