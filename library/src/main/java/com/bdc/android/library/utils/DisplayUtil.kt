package com.bdc.android.library.utils

import android.content.Context
import android.util.DisplayMetrics
import android.util.Size
import android.view.WindowManager

/**
 * Copyright (C), 2019-2020, 中传互动（湖北）信息技术有限公司
 * Author: <PERSON><PERSON><PERSON>
 * Date: 2020/9/18 18:13
 * Description:
 */

object DisplayUtil {

    fun px2dp(context: Context, dip: Float): Int {
        val density = context.resources.displayMetrics.density
        return (dip / density + 0.5f).toInt()
    }

    fun dp2px(context: Context, dip: Float): Int {
        val density = context.resources.displayMetrics.density
        return (dip * density + 0.5f).toInt()
    }

    fun getScreenWidth(context: Context): Int {
        return getScreenSize(context).width
    }

    fun getScreenHeight(context: Context?): Int {
        return getScreenSize(context).height
    }

    fun getScreenSize(context: Context?): Size {
        val size = IntArray(2)
        val w = context?.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val d = w.defaultDisplay
        val metrics = DisplayMetrics()
        d.getMetrics(metrics)
        return Size(metrics.widthPixels, metrics.heightPixels)
    }

    fun isLongScreen(context: Context): Boolean {
        val displayMetrics = context.resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels.toFloat()
        val screenHeight = displayMetrics.heightPixels.toFloat()
        val aspectRatio = screenHeight / screenWidth // 计算屏幕高宽比
        return aspectRatio > 2.0 // 如果比 16:9（1.77）更长，就认为是长屏
    }
}