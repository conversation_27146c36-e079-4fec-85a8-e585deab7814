package com.mobile.anchor.app.manager

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.mobile.anchor.app.data.model.AgoraConfigBean
import com.mobile.anchor.app.data.model.AuthBean
import com.mobile.anchor.app.data.model.AuthConfigBean
import com.mobile.anchor.app.data.model.UserBean
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

/**
 * DataStoreManager AuthConfig 功能测试
 */
@RunWith(AndroidJUnit4::class)
@Config(sdk = [28])
class DataStoreManagerAuthConfigTest {

    private lateinit var context: Context

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        DataStoreManager.initialize(context)
        
        // 清除所有数据，确保测试环境干净
        runBlocking {
            DataStoreManager.clearAllUserData()
        }
    }

    @Test
    fun testSaveAndGetAuthConfig() = runBlocking {
        // 创建测试配置对象
        val testConfig = AuthConfigBean(
            daily_duration = 480,
            cdn = "https://cdn.example.com",
            agoraConfig = AgoraConfigBean(appID = "test_agora_app_id")
        )

        // 保存配置对象
        DataStoreManager.saveAuthConfig(testConfig)

        // 获取配置对象
        val retrievedConfig = DataStoreManager.getAuthConfig()

        // 验证数据
        assert(retrievedConfig != null)
        assert(retrievedConfig?.daily_duration == testConfig.daily_duration)
        assert(retrievedConfig?.cdn == testConfig.cdn)
        assert(retrievedConfig?.agoraConfig?.appID == testConfig.agoraConfig.appID)

        println("✅ AuthConfig 保存和获取测试通过")
    }

    @Test
    fun testSyncAndAsyncAuthConfigMethods() = runBlocking {
        val testConfig = AuthConfigBean(
            daily_duration = 600,
            cdn = "https://sync-test.com",
            agoraConfig = AgoraConfigBean(appID = "sync_test_app_id")
        )

        // 保存配置对象
        DataStoreManager.saveAuthConfig(testConfig)

        // 测试同步方法
        val syncConfig = DataStoreManager.getAuthConfigSync()
        assert(syncConfig?.cdn == testConfig.cdn)

        // 测试Flow方法
        val flowConfig = DataStoreManager.getAuthConfigFlow().first()
        assert(flowConfig?.cdn == testConfig.cdn)

        println("✅ AuthConfig 同步和异步方法测试通过")
    }

    @Test
    fun testUpdateAuthConfig() = runBlocking {
        // 创建初始配置
        val initialConfig = AuthConfigBean(
            daily_duration = 300,
            cdn = "https://initial.com",
            agoraConfig = AgoraConfigBean(appID = "initial_app_id")
        )

        DataStoreManager.saveAuthConfig(initialConfig)

        // 更新配置
        DataStoreManager.updateAuthConfig { currentConfig ->
            currentConfig?.copy(
                daily_duration = 720,
                cdn = "https://updated.com"
            )
        }

        // 验证更新结果
        val updatedConfig = DataStoreManager.getAuthConfig()
        assert(updatedConfig?.daily_duration == 720)
        assert(updatedConfig?.cdn == "https://updated.com")
        assert(updatedConfig?.agoraConfig?.appID == initialConfig.agoraConfig.appID) // 应该保持不变

        println("✅ AuthConfig 更新测试通过")
    }

    @Test
    fun testSaveLoginData() = runBlocking {
        // 创建测试登录响应
        val testUser = UserBean(
            id = "login_test_user",
            nickname = "登录测试用户",
            email = "<EMAIL>",
            stat = 1, // 注册已完成
            rongcloudToken = "test_rong_token",
            rongcloudAppID = "test_rong_app_id"
        )

        val testConfig = AuthConfigBean(
            daily_duration = 480,
            cdn = "https://login-test.com",
            agoraConfig = AgoraConfigBean(appID = "login_test_app_id")
        )

        val authBean = AuthBean(
            accessToken = "test_access_token",
            accessExpire = System.currentTimeMillis() + 3600000,
            refreshAfter = System.currentTimeMillis() + 1800000,
            config = testConfig,
            anchor = testUser
        )

        // 使用统一登录数据保存方法
        DataStoreManager.saveLoginData(authBean)

        // 验证所有数据都已保存
        val savedUser = DataStoreManager.getUserObject()
        val savedConfig = DataStoreManager.getAuthConfig()
        val savedToken = DataStoreManager.getAccessTokenSync()
        val savedUserId = DataStoreManager.getUserIdSync()

        assert(savedUser?.id == testUser.id)
        assert(savedConfig?.cdn == testConfig.cdn)
        assert(savedToken == authBean.accessToken)
        assert(savedUserId == testUser.id)

        println("✅ 统一登录数据保存测试通过")
    }

    @Test
    fun testCheckUserRegistrationStatus() = runBlocking {
        // 测试场景1: 用户对象不存在
        var status = DataStoreManager.checkUserRegistrationStatus()
        assert(status == "login")

        // 测试场景2: 用户处于注册状态 (stat=0)
        val registrationUser = UserBean(
            id = "registration_user",
            nickname = "注册中用户",
            email = "<EMAIL>",
            stat = 0 // isRegistration = true
        )
        DataStoreManager.saveUserObject(registrationUser)
        
        status = DataStoreManager.checkUserRegistrationStatus()
        assert(status == "build_profile")

        // 测试场景3: 用户注册已完成 (stat=1)
        val completedUser = UserBean(
            id = "completed_user",
            nickname = "已完成用户",
            email = "<EMAIL>",
            stat = 1 // isRegistrationCompleted = true
        )
        DataStoreManager.saveUserObject(completedUser)
        
        status = DataStoreManager.checkUserRegistrationStatus()
        assert(status == "home")

        // 测试场景4: 用户审核通过 (stat=2)
        val approvedUser = UserBean(
            id = "approved_user",
            nickname = "审核通过用户",
            email = "<EMAIL>",
            stat = 2 // 审核通过
        )
        DataStoreManager.saveUserObject(approvedUser)
        
        status = DataStoreManager.checkUserRegistrationStatus()
        assert(status == "home")

        println("✅ 用户注册状态检查测试通过")
    }

    @Test
    fun testClearAllUserDataIncludesAuthConfig() = runBlocking {
        // 保存用户数据和配置
        val testUser = UserBean(id = "clear_test", nickname = "清除测试")
        val testConfig = AuthConfigBean(
            daily_duration = 300,
            cdn = "https://clear-test.com",
            agoraConfig = AgoraConfigBean(appID = "clear_test_app")
        )

        DataStoreManager.saveUserObject(testUser)
        DataStoreManager.saveAuthConfig(testConfig)
        DataStoreManager.saveTokens("test_token")

        // 验证数据已保存
        assert(DataStoreManager.getUserObject() != null)
        assert(DataStoreManager.getAuthConfig() != null)
        assert(DataStoreManager.getAccessTokenSync() != null)

        // 清除所有用户数据
        DataStoreManager.clearAllUserData()

        // 验证所有数据已清除
        assert(DataStoreManager.getUserObject() == null)
        assert(DataStoreManager.getAuthConfig() == null)
        assert(DataStoreManager.getAccessTokenSync() == null)

        println("✅ 清除所有用户数据（包含AuthConfig）测试通过")
    }

    @Test
    fun testNullAuthConfig() = runBlocking {
        // 测试保存null配置对象
        DataStoreManager.saveAuthConfig(null)

        // 获取应该返回null
        val config = DataStoreManager.getAuthConfig()
        assert(config == null)

        println("✅ 空配置对象处理测试通过")
    }
}
