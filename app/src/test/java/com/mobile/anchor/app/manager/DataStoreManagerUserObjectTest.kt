package com.mobile.anchor.app.manager

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.mobile.anchor.app.data.model.UserBean
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

/**
 * DataStoreManager 用户对象功能测试
 */
@RunWith(AndroidJUnit4::class)
@Config(sdk = [28])
class DataStoreManagerUserObjectTest {

    private lateinit var context: Context

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        DataStoreManager.initialize(context)
        
        // 清除所有数据，确保测试环境干净
        runBlocking {
            DataStoreManager.clearAllUserData()
        }
    }

    @Test
    fun testSaveAndGetUserObject() = runBlocking {
        // 创建测试用户对象
        val testUser = UserBean(
            id = "test_user_123",
            nickname = "测试用户",
            email = "<EMAIL>",
            avatar = "https://example.com/avatar.jpg",
            age = 25,
            gender = 1,
            status = "1",
            birthday_at = "1998-01-01",
            country_code = "CN",
            rongcloudToken = "test_rong_token",
            rongcloudAppID = "test_rong_app_id"
        )

        // 保存用户对象
        DataStoreManager.saveUserObject(testUser)

        // 获取用户对象
        val retrievedUser = DataStoreManager.getUserObject()

        // 验证数据
        assert(retrievedUser != null)
        assert(retrievedUser?.id == testUser.id)
        assert(retrievedUser?.nickname == testUser.nickname)
        assert(retrievedUser?.email == testUser.email)
        assert(retrievedUser?.avatar == testUser.avatar)
        assert(retrievedUser?.age == testUser.age)
        assert(retrievedUser?.gender == testUser.gender)

        println("✅ 用户对象保存和获取测试通过")
    }

    @Test
    fun testSyncAndAsyncMethods() = runBlocking {
        val testUser = UserBean(
            id = "sync_test_user",
            nickname = "同步测试用户",
            email = "<EMAIL>"
        )

        // 保存用户对象
        DataStoreManager.saveUserObject(testUser)

        // 测试同步方法
        val syncUser = DataStoreManager.getUserObjectSync()
        assert(syncUser?.id == testUser.id)

        // 测试Flow方法
        val flowUser = DataStoreManager.getUserObjectFlow().first()
        assert(flowUser?.id == testUser.id)

        println("✅ 同步和异步方法测试通过")
    }

    @Test
    fun testUpdateUserObject() = runBlocking {
        // 创建初始用户
        val initialUser = UserBean(
            id = "update_test_user",
            nickname = "初始昵称",
            email = "<EMAIL>",
            age = 20
        )

        DataStoreManager.saveUserObject(initialUser)

        // 更新用户昵称
        DataStoreManager.updateUserObject { currentUser ->
            currentUser?.copy(nickname = "更新后的昵称", age = 25)
        }

        // 验证更新结果
        val updatedUser = DataStoreManager.getUserObject()
        assert(updatedUser?.nickname == "更新后的昵称")
        assert(updatedUser?.age == 25)
        assert(updatedUser?.id == initialUser.id) // ID应该保持不变

        println("✅ 用户对象更新测试通过")
    }

    @Test
    fun testLoginStatusCheck() = runBlocking {
        // 初始状态应该是未登录
        assert(!DataStoreManager.isUserLoggedIn())

        // 只保存用户对象，没有token，应该还是未登录
        val testUser = UserBean(id = "login_test_user", nickname = "登录测试")
        DataStoreManager.saveUserObject(testUser)
        assert(!DataStoreManager.isUserLoggedIn())

        // 保存token
        DataStoreManager.saveTokens("test_access_token")

        // 现在应该是已登录状态
        assert(DataStoreManager.isUserLoggedIn())

        // 测试同步方法
        assert(DataStoreManager.isUserLoggedInSync())

        println("✅ 登录状态检查测试通过")
    }

    @Test
    fun testClearAllUserData() = runBlocking {
        // 保存用户数据
        val testUser = UserBean(
            id = "clear_test_user",
            nickname = "清除测试用户",
            email = "<EMAIL>"
        )
        DataStoreManager.saveUserObject(testUser)
        DataStoreManager.saveTokens("test_token")

        // 验证数据已保存
        assert(DataStoreManager.getUserObject() != null)
        assert(DataStoreManager.getAccessTokenSync() != null)

        // 清除所有用户数据
        DataStoreManager.clearAllUserData()

        // 验证数据已清除
        assert(DataStoreManager.getUserObject() == null)
        assert(DataStoreManager.getAccessTokenSync() == null)
        assert(!DataStoreManager.isUserLoggedIn())

        println("✅ 清除用户数据测试通过")
    }

    @Test
    fun testNullUserObject() = runBlocking {
        // 测试保存null用户对象
        DataStoreManager.saveUserObject(null)

        // 获取应该返回null
        val user = DataStoreManager.getUserObject()
        assert(user == null)

        // 登录状态应该是false
        assert(!DataStoreManager.isUserLoggedIn())

        println("✅ 空用户对象处理测试通过")
    }

    @Test
    fun testDataConsistency() = runBlocking {
        // 测试保存用户对象时，单字段也会被正确保存
        val testUser = UserBean(
            id = "consistency_test",
            nickname = "一致性测试",
            email = "<EMAIL>",
            avatar = "https://example.com/avatar.jpg"
        )

        DataStoreManager.saveUserObject(testUser)

        // 验证单字段方法也能获取到正确的值
        assert(DataStoreManager.getUserIdSync() == testUser.id)
        assert(DataStoreManager.getUserNameSync() == testUser.nickname)
        assert(DataStoreManager.getUserEmailSync() == testUser.email)

        println("✅ 数据一致性测试通过")
    }
}
