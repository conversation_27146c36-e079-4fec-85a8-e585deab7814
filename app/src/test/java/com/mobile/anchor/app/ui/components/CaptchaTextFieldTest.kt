package com.mobile.anchor.app.ui.components

import org.junit.Test
import org.junit.Assert.*

/**
 * 验证码输入组件的单元测试
 */
class CaptchaTextFieldTest {

    @Test
    fun testCaptchaType_Number() {
        val type = CaptchaType.NUMBER
        assertEquals("NUMBER", type.name)
    }

    @Test
    fun testCaptchaType_Letter() {
        val type = CaptchaType.LETTER
        assertEquals("LETTER", type.name)
    }

    @Test
    fun testCaptchaType_Mixed() {
        val type = CaptchaType.MIXED
        assertEquals("MIXED", type.name)
    }

    @Test
    fun testNumberValidation() {
        // 测试数字验证逻辑
        val validNumbers = listOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9')
        val invalidChars = listOf('a', 'A', '!', '@', '#', '$', '%', '^', '&', '*')
        
        validNumbers.forEach { char ->
            assertTrue("数字 $char 应该是有效的", char.isDigit())
        }
        
        invalidChars.forEach { char ->
            assertFalse("字符 $char 不应该是有效数字", char.isDigit())
        }
    }

    @Test
    fun testLetterValidation() {
        // 测试字母验证逻辑
        val validLetters = listOf('a', 'b', 'c', 'A', 'B', 'C', 'z', 'Z')
        val invalidChars = listOf('0', '1', '2', '!', '@', '#', '$', '%')
        
        validLetters.forEach { char ->
            assertTrue("字母 $char 应该是有效的", char.isLetter())
        }
        
        invalidChars.forEach { char ->
            assertFalse("字符 $char 不应该是有效字母", char.isLetter())
        }
    }

    @Test
    fun testMixedValidation() {
        // 测试混合验证逻辑
        val validChars = listOf('a', 'A', '0', '1', 'z', 'Z', '9')
        val invalidChars = listOf('!', '@', '#', '$', '%', '^', '&', '*', '(', ')')
        
        validChars.forEach { char ->
            assertTrue("字符 $char 应该是有效的字母或数字", char.isLetterOrDigit())
        }
        
        invalidChars.forEach { char ->
            assertFalse("字符 $char 不应该是有效的字母或数字", char.isLetterOrDigit())
        }
    }

    @Test
    fun testStringFiltering() {
        // 测试字符串过滤逻辑
        val input = "a1b2c3!@#"
        
        // 只保留数字
        val numbersOnly = input.filter { it.isDigit() }
        assertEquals("123", numbersOnly)
        
        // 只保留字母
        val lettersOnly = input.filter { it.isLetter() }
        assertEquals("abc", lettersOnly)
        
        // 保留字母和数字
        val alphanumericOnly = input.filter { it.isLetterOrDigit() }
        assertEquals("a1b2c3", alphanumericOnly)
    }

    @Test
    fun testLengthLimiting() {
        // 测试长度限制逻辑
        val input = "1234567890"
        val maxLength = 6
        
        val limitedInput = input.take(maxLength)
        assertEquals("123456", limitedInput)
        assertEquals(maxLength, limitedInput.length)
    }

    @Test
    fun testCodeCompletion() {
        // 测试验证码完成检测
        val targetLength = 6
        
        val incompleteCode = "12345"
        assertFalse("未完成的验证码不应该触发完成", incompleteCode.length == targetLength)
        
        val completeCode = "123456"
        assertTrue("完成的验证码应该触发完成", completeCode.length == targetLength)
        
        val overflowCode = "1234567"
        assertFalse("超长的验证码不应该触发完成", overflowCode.length == targetLength)
    }
}
