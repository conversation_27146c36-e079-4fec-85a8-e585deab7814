<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 悬浮窗权限 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SYSTEM_ALERT_WINDOW" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <!-- 摄像头权限 -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- 录音权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- 存储权限 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <!-- Android 13+ 媒体权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- 安装APK权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.front" />

    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <application
        android:name="com.mobile.anchor.app.App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:enableOnBackInvokedCallback="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Anchor">

        <!-- FileProvider for camera image capture -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 悬浮窗服务 -->
        <service
            android:name=".service.OverlayWindowService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <activity
            android:name=".ui.welcome.WelcomeActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.App.Starting">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.activities.LoginActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.call.CallVideoChatActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.ProfileBuildActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.ProfileEditActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.FaceRecordActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.BankBindActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.AlbumManageActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.SettingActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.WalletActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.LevelActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.RewardTaskActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.NotificationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.conversation.MyRongConversationActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.UserDetailActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activities.ReferralProfitActivity"
            android:screenOrientation="portrait" />
    </application>

</manifest>