<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="ResourceName">


    <declare-styleable name="SlidingTabLayoutExtend">
        <!-- indicator -->
        <attr name="tl_indicator_start_color" format="color"/>
        <attr name="tl_indicator_end_color" format="color"/>

    </declare-styleable>

    <!-- MarqueeView的自定义属性 -->
    <declare-styleable name="MarqueeView">
        <!-- 文本颜色 -->
        <attr name="marqueeTextColor" format="color" />
        <!-- 文本大小 -->
        <attr name="marqueeTextSize" format="dimension" />
        <!-- 滚动间隔时间(毫秒) -->
        <attr name="marqueeInterval" format="integer" />
        <!-- 动画持续时间(毫秒) -->
        <attr name="marqueeDuration" format="integer" />
        <!-- 最大行数 -->
        <attr name="marqueeMaxLines" format="integer" />
        <!-- 是否自动滚动 -->
        <attr name="marqueeAutoScroll" format="boolean" />
        <!-- 文本对齐方式 -->
        <attr name="marqueeTextGravity">
            <flag name="top" value="0x30" />
            <flag name="bottom" value="0x50" />
            <flag name="left" value="0x03" />
            <flag name="right" value="0x05" />
            <flag name="center_vertical" value="0x10" />
            <flag name="center_horizontal" value="0x01" />
            <flag name="center" value="0x11" />
        </attr>
    </declare-styleable>
</resources>