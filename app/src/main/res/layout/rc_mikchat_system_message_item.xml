<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/CL"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_16"
    android:background="@drawable/shape_system_notice_item_bg"
    android:orientation="vertical"
    android:padding="@dimen/dp_16">

    <TextView
        android:id="@+id/notice_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_18"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="Update" />

    <TextView
        android:id="@+id/notice_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp_12"
        android:textColor="#40ffffff"
        android:textSize="@dimen/sp_14"
        tools:text="Update" />

    <View
        android:id="@+id/space_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_1"
        android:background="#40ffffff" />

    <TextView
        android:id="@+id/tv_check_now"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:drawablePadding="@dimen/dp_8"
        android:gravity="end"
        android:text="@string/label_check_now"
        android:textColor="#40ffffff"
        android:textSize="@dimen/sp_14"
        app:drawableEndCompat="@mipmap/ic_right_gray"
        app:drawableTint="#40ffffff" />


</LinearLayout>