<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <FrameLayout
        android:id="@+id/local_video_view_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/LLUser"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_40"
        android:background="@drawable/discover_video_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/dp_4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/head"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/dp_44"
            android:layout_gravity="center_vertical"
            android:src="@mipmap/ic_default_avatar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:orientation="vertical">

            <TextView
                android:id="@+id/anchorName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:ellipsize="end"
                android:maxWidth="@dimen/dp_100"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_16"
                android:textStyle="bold"
                tools:text="LiLiLiLiLiLiLiLi..." />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_5"
                android:layout_marginEnd="@dimen/dp_14"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_age1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_age_bg1"
                    android:paddingHorizontal="@dimen/dp_10"
                    android:paddingVertical="@dimen/dp_3"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_12"
                    tools:text="24" />

                <TextView
                    android:id="@+id/tv_country1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_8"
                    android:background="@drawable/shape_country_bg1"
                    android:paddingHorizontal="@dimen/dp_10"
                    android:paddingVertical="@dimen/dp_3"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp_12"
                    tools:text="24" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_follow_top"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_28"
            android:layout_gravity="center_vertical|end"
            android:layout_marginStart="@dimen/dp_4"
            android:background="@drawable/shape_follow_btn_bg"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_6"
            android:paddingVertical="@dimen/dp_4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/iv_follow_top"
                android:layout_width="@dimen/dp_7"
                android:layout_height="@dimen/dp_7"
                android:src="@mipmap/ic_follow" />

            <TextView
                android:id="@+id/tv_follow_top"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_2"
                android:includeFontPadding="false"
                android:text="@string/btn_follow"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_10" />

        </LinearLayout>
    </LinearLayout>

    <com.mobile.anchor.app.ui.view.GiftEffectView
        android:id="@+id/giftEffectView"
        android:layout_width="wrap_content"
        android:background="@color/white"
        android:layout_marginStart="@dimen/dp_4"
        android:layout_marginTop="@dimen/dp_10"
        app:layout_constraintStart_toStartOf="@id/LLUser"
        app:layout_constraintTop_toBottomOf="@id/LLUser"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_14"
        android:layout_marginBottom="@dimen/dp_140"
        android:background="@drawable/discover_video_bg_02"
        android:maxWidth="@dimen/dp_170"
        android:paddingLeft="@dimen/dp_8"
        android:paddingTop="@dimen/dp_6"
        android:paddingRight="@dimen/dp_8"
        android:paddingBottom="@dimen/dp_6"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="visible" />

    <com.bdc.android.library.refreshlayout.XRecyclerView
        android:id="@+id/message_list"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_200"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_9"
        app:layout_constraintBottom_toTopOf="@+id/LLNa"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tip" />

    <RelativeLayout
        android:id="@+id/RLRemoteVideo"
        android:layout_width="@dimen/dp_100"
        android:layout_height="@dimen/dp_120"
        android:layout_marginStart="@dimen/dp_240"
        android:layout_marginTop="@dimen/dp_146"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/remote_video_view_container"
            android:layout_width="@dimen/dp_100"
            android:layout_height="@dimen/dp_120" />

        <View
            android:id="@+id/view_hide_remote"
            android:layout_width="@dimen/dp_100"
            android:layout_height="@dimen/dp_120"
            android:background="@color/black"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_remote_close_video_header"
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_50"
            android:layout_centerInParent="true"
            android:visibility="gone" />

        <TextView
            android:id="@+id/timer"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_22"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_video_call_time"
            android:gravity="center"
            android:paddingLeft="@dimen/dp_6"
            android:paddingTop="@dimen/dp_2"
            android:paddingRight="@dimen/dp_6"
            android:paddingBottom="@dimen/dp_2"
            android:text="00:00"
            android:textColor="#ffffffff"
            android:textSize="12sp" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/LLNa"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingBottom="@dimen/dp_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent">

        <LinearLayout
            android:id="@+id/ll_input_text"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_36"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_8"
            android:layout_weight="1"
            android:background="@drawable/discover_video_bg_03"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_marginStart="@dimen/dp_8"
                android:src="@mipmap/ic_video_emoji" />

            <View
                android:layout_width="@dimen/dp_1"
                android:layout_height="@dimen/dp_24"
                android:layout_marginStart="@dimen/dp_8"
                android:background="@color/white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_8"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_weight="1"
                android:text="@string/say_something"
                android:textColor="#60ffffff"
                android:textSize="@dimen/sp_12" />

            <ImageView
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:layout_marginStart="@dimen/dp_8"
                android:src="@mipmap/ic_video_enter" />

        </LinearLayout>

        <ImageView
            android:id="@+id/hung_up"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"
            android:layout_marginEnd="@dimen/dp_8"
            android:src="@mipmap/ic_video_close"
            android:textColor="#fff"
            app:layout_constraintBottom_toBottomOf="@+id/LLNa"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/LLNa" />

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_video_focus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:src="@mipmap/ic_video_focus"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/face"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_370"
        android:layout_marginStart="@dimen/dp_25"
        android:layout_marginTop="@dimen/dp_180"
        android:layout_marginEnd="@dimen/dp_25"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <!--                android:background="#50000000"-->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottom_send_msg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        android:paddingVertical="@dimen/dp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <io.rong.imkit.widget.RongEditText
            android:id="@+id/edit_btn"
            style="@style/EditTextStyle.Alignment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_12"
            android:background="@drawable/shape_message_input_edit"
            android:gravity="center_vertical"
            android:hint="@string/message_input_hint"
            android:maxLines="4"
            android:minHeight="@dimen/dp_36"
            android:paddingHorizontal="@dimen/dp_20"
            android:textColor="@color/white"
            android:textColorHint="#6D7096"
            android:textSize="@dimen/sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/input_panel_send_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/input_panel_send_btn"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:background="@color/transparent"
            android:text="@string/rc_send"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/send_gift"
        android:layout_width="@dimen/dp_54"
        android:layout_height="@dimen/dp_54"
        android:layout_marginEnd="@dimen/dp_12"
        android:layout_marginBottom="@dimen/dp_198"
        android:src="@mipmap/ic_video_gift"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/CLLeave"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/bg_video_call"
        android:clickable="true"
        tools:visibility="gone">

        <ImageView
            android:id="@+id/head02"
            android:layout_width="@dimen/dp_320"
            android:layout_height="@dimen/dp_320"
            android:layout_marginTop="@dimen/dp_180"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@mipmap/ic_default_image"
            tools:src="@mipmap/ic_default_image" />

        <View
            android:layout_width="@dimen/dp_320"
            android:layout_height="@dimen/dp_102"
            android:background="@drawable/shape_call_info_bottom_lock"
            app:layout_constraintBottom_toBottomOf="@id/head02"
            app:layout_constraintEnd_toEndOf="@id/head02"
            app:layout_constraintStart_toStartOf="@id/head02" />

        <ImageView
            android:id="@+id/iv_vip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_16"
            android:src="@mipmap/ic_vip_person"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/anchorName02"
            app:layout_constraintStart_toStartOf="@id/head02"
            app:layout_constraintTop_toTopOf="@id/anchorName02"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/anchorName02"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_4"
            android:layout_marginBottom="@dimen/dp_47"
            android:ellipsize="end"
            android:maxWidth="@dimen/dp_140"
            android:maxLines="1"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/head02"
            app:layout_constraintStart_toEndOf="@id/iv_vip"
            tools:text="LiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLi..." />


        <com.mobile.anchor.app.ui.view.LevelLabelView
            android:id="@+id/levelLabelView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            app:layout_constraintBottom_toBottomOf="@id/anchorName02"
            app:layout_constraintStart_toEndOf="@id/anchorName02"
            app:layout_constraintTop_toTopOf="@id/anchorName02" />


        <TextView
            android:id="@+id/tv_age2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@drawable/shape_age_bg"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_3"
            android:textColor="@color/white"
            app:layout_constraintStart_toStartOf="@id/anchorName02"
            app:layout_constraintTop_toBottomOf="@id/anchorName02"
            tools:text="23" />

        <LinearLayout
            android:id="@+id/ll_follow"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_28"
            android:layout_gravity="center_vertical|end"
            android:layout_marginEnd="@dimen/dp_16"
            android:background="@drawable/shape_follow_btn_bg"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp_6"
            android:paddingVertical="@dimen/dp_4"
            app:layout_constraintEnd_toEndOf="@id/head02"
            app:layout_constraintTop_toTopOf="@id/anchorName02"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/iv_follow"
                android:layout_width="@dimen/dp_7"
                android:layout_height="@dimen/dp_7"
                android:src="@mipmap/ic_follow" />

            <TextView
                android:id="@+id/tv_follow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_2"
                android:includeFontPadding="false"
                android:text="@string/btn_follow"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_10" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_country2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@drawable/shape_country_bg"
            android:paddingHorizontal="@dimen/dp_8"
            android:paddingVertical="@dimen/dp_3"
            android:textColor="@color/white"
            app:layout_constraintStart_toEndOf="@id/tv_age2"
            app:layout_constraintTop_toBottomOf="@id/anchorName02"
            tools:text="23" />

        <LinearLayout
            android:id="@+id/video_call_action"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="@id/head02"
            app:layout_constraintTop_toBottomOf="@id/head02">

            <ImageView
                android:id="@+id/leave"
                android:layout_width="@dimen/dp_72"
                android:layout_height="@dimen/dp_72"
                android:src="@mipmap/ic_cancel_call" />

            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/anchor_listen"
                android:layout_width="@dimen/dp_88"
                android:layout_height="@dimen/dp_88"
                android:layout_marginStart="@dimen/dp_103"
                app:autoPlay="true"
                app:source="ic_video.svga" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_join_count_down_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/shape_join_count_down_bg"
        android:paddingHorizontal="@dimen/dp_22"
        android:paddingVertical="@dimen/dp_10"
        android:textColor="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout><!--    </androidx.drawerlayout.widget.DrawerLayout>-->
