<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">
    <!-- Fragment容器 -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/bottom_navigation"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 底部导航栏 -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        app:itemHorizontalTranslationEnabled="true"
        app:itemIconSize="30dp"
        app:itemIconTint="@null"
        app:itemTextColor="@color/bottom_nav_text_color"
        app:labelVisibilityMode="auto"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:menu="@menu/bottom_navigation_menu" />

<!--    <com.flyco.tablayout.CommonTabLayout-->
<!--        android:id="@+id/bottomBar"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="@dimen/dp_65"-->
<!--        android:background="@color/background"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:tl_indicator_height="0dp"-->
<!--        app:tl_textsize="12sp"-->
<!--        app:tl_textSelectColor="@android:color/black"-->
<!--        app:tl_textUnselectColor="@android:color/darker_gray"-->
<!--        app:tl_underline_height="0dp" />-->

</androidx.constraintlayout.widget.ConstraintLayout>