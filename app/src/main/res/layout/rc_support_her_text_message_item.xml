<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/CL"
    android:padding="@dimen/dp_16">


    <LinearLayout
        android:id="@+id/ll_avatar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_anchor_avatar"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:src="@mipmap/ic_pic_default_oval"/>

        <ImageView
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:src="@mipmap/ic_message_support_her" />

        <ImageView
            android:id="@+id/iv_user_avatar"
            android:layout_width="@dimen/dp_48"
            android:layout_height="@dimen/dp_48"
            android:src="@mipmap/ic_default_avatar"/>
    </LinearLayout>


    <TextView
        android:id="@+id/tv_support_her_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxWidth="@dimen/dp_227"
        app:layout_constraintTop_toBottomOf="@id/ll_avatar"
        android:layout_marginTop="@dimen/dp_10"
        android:textColor="@color/white"
        tools:text="vvvvvvvvvvvvvv"/>


</androidx.constraintlayout.widget.ConstraintLayout>