<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/color_background"
    android:layout_height="@dimen/rc_title_bar_height"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tool_bar_left"
        style="@style/TextStyle.Alignment"
        android:layout_width="110dp"
        android:layout_height="match_parent"
        android:drawablePadding="3dp"
        android:gravity="center_vertical"
        android:paddingStart="11dp"
        android:paddingEnd="10dp"
        android:paddingRight="10dp"
        android:scaleType="center"
        android:textColor="@android:color/white"
        android:textSize="17sp"
        android:textStyle="bold"
        android:drawableTint="@color/colorWhite"
        tools:ignore="RtlHardcoded"
        android:layout_gravity="start"
        app:drawableStartCompat="@drawable/rc_title_bar_back" />

    <FrameLayout
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="115dp"
        android:layout_marginEnd="115dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp">

        <TextView
            android:id="@+id/tool_bar_middle"
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="1"
            android:maxEms="12"
            android:scaleType="center"
            android:textColor="@android:color/white"
            android:textSize="17sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tool_bar_middle_typing"
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="1"
            android:maxEms="12"
            android:scaleType="center"
            android:textColor="@android:color/white"
            android:textSize="17sp"
            android:textStyle="bold"
            android:visibility="gone" />
    </FrameLayout>

    <LinearLayout
        android:layout_width="110dp"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:orientation="horizontal">
        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1"/>
        <ImageView
            android:id="@+id/rc_search"
            android:layout_width="19dp"
            android:layout_height="19dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="10dp"
            android:scaleType="centerCrop"
            android:visibility="gone"
            android:src="@drawable/rc_search_icon" />

        <TextView
            android:id="@+id/tool_bar_right"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:scaleType="center"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:visibility="gone" />
    </LinearLayout>
</FrameLayout>
