<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/CL"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="6dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="horizontal"
        android:padding="@dimen/dp_5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/gift"
            android:layout_width="@dimen/dp_71"
            android:layout_height="@dimen/dp_67"

            />

        <LinearLayout
            android:id="@+id/llSend"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_8"
            android:orientation="vertical">

            <TextView
                android:id="@+id/giftName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxWidth="@dimen/dp_120"
                android:maxLines="1"
                android:textColor="#ffffffff"
                android:textSize="@dimen/sp_16"
                tools:text="Ask for kisssssis...*99" />


        </LinearLayout>

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>