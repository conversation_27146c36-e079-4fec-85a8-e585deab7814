<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center_vertical"
    android:layout_marginBottom="@dimen/dp_8"
    android:background="@drawable/shape_video_message_item"
    android:minHeight="@dimen/dp_24"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp_12"
    android:paddingVertical="@dimen/dp_2">

    <TextView
        android:id="@+id/tv_label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_14"
        tools:text="vvvvvvvv"/>

    <ImageView
        android:id="@+id/gift_img"
        android:layout_width="@dimen/dp_20"
        android:layout_height="@dimen/dp_20"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginEnd="@dimen/dp_4"
        android:adjustViewBounds="true"
        app:layout_constraintStart_toEndOf="@id/tv_label"
        app:layout_constraintTop_toTopOf="@id/tv_label"
        android:visibility="gone" />

    <TextView
        android:id="@+id/gift_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#BBA7FF"
        app:layout_constraintStart_toEndOf="@id/gift_img"
        app:layout_constraintTop_toTopOf="@id/tv_label"
        android:textSize="@dimen/sp_14"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>