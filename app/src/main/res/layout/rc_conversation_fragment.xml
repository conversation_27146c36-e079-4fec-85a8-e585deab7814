<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <io.rong.imkit.widget.refresh.SmartRefreshLayout
        android:id="@+id/rc_refresh"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/rc_extension"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rc_message_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never" />
    </io.rong.imkit.widget.refresh.SmartRefreshLayout>

    <TextView
        android:id="@+id/rc_new_message_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="4dp"
        android:background="@drawable/rc_conversation_newmsg"
        android:gravity="center"
        android:paddingBottom="5dp"
        android:textColor="#ffffff"
        android:textSize="12dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/rc_refresh"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/rc_unread_message_count"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/rc_unread_height"
        android:layout_marginStart="3dp"
        android:layout_marginTop="@dimen/rc_margin_size_30"
        android:layout_marginEnd="5dp"
        android:background="@drawable/rc_unread_msg_bg_style"
        android:drawableStart="@drawable/rc_unread_msg_arrow"
        android:drawablePadding="3dp"
        android:gravity="center"
        android:maxLines="1"
        android:minWidth="120dp"
        android:paddingStart="7dp"
        android:paddingEnd="7dp"
        android:textColor="@color/rc_text_main_color"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/rc_mention_message_count"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/rc_unread_height"
        android:layout_marginStart="3dp"
        android:layout_marginTop="@dimen/rc_margin_size_80"
        android:layout_marginEnd="5dp"
        android:background="@drawable/rc_unread_msg_bg_style"
        android:drawableStart="@drawable/rc_unread_msg_arrow"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:minWidth="120dp"
        android:paddingStart="7dp"
        android:paddingEnd="7dp"
        android:text="@string/rc_mention_messages"
        android:textColor="@color/rc_text_main_color"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/rl_support_me"
        android:layout_width="@dimen/dp_54"
        android:layout_height="@dimen/dp_54"
        android:layout_margin="@dimen/dp_15"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/rc_extension"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible">

        <!--        <ImageView-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="match_parent"-->
        <!--            android:src="@mipmap/ic_support_me" />-->
        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/svga"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:autoPlay="true"
            app:loopCount="1"
            app:source="support_me.svga" />

        <com.mobile.anchor.app.i18n.I18nTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_8"
            android:text="@string/support_me"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_8" />
    </RelativeLayout>

    <io.rong.imkit.conversation.extension.MyRongExtension
        android:id="@+id/rc_extension"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rc_refresh" />

    <LinearLayout
        android:id="@+id/rc_notification_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#660F0F0F"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
