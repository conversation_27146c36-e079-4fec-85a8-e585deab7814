<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_width="@dimen/dp_200"
        android:layout_height="@dimen/dp_115"
        android:src="@mipmap/rc_conversation_list_empty"/>

    <TextView
        android:id="@+id/rc_empty_tv"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/rc_conversation_list_empty_prompt"
        android:textColor="#40ffffff"
        android:textSize="@dimen/rc_font_text_third_size" />
</LinearLayout>