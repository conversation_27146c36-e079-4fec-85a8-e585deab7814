<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/CL"
    android:padding="@dimen/dp_16">

    <TextView
        android:id="@+id/tv_recharge_for_me_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxWidth="@dimen/dp_227"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="vvvvvvvvvvvvvv" />

    <com.mobile.anchor.app.i18n.I18nTextView
        android:id="@+id/btn_recharge_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:drawablePadding="@dimen/dp_8"
        android:paddingHorizontal="@dimen/dp_10"
        android:paddingVertical="@dimen/dp_4"
        android:gravity="end"
        android:text="@string/btn_recharge_detail"
        android:textColor="@color/color_9F2AF8"
        android:textSize="@dimen/sp_10"
        android:background="@drawable/shape_bg_white_12"
        app:drawableEndCompat="@mipmap/ic_right_gray"
        app:drawableTint="@color/color_9F2AF8"
        app:layout_constraintEnd_toEndOf="@id/tv_recharge_for_me_content"
        app:layout_constraintTop_toBottomOf="@id/tv_recharge_for_me_content" />


</androidx.constraintlayout.widget.ConstraintLayout>