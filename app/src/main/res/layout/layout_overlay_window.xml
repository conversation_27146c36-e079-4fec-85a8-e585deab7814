<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_floating_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@mipmap/ic_launcher"
        android:contentDescription="@string/app_name" />

    <TextView
        android:id="@+id/tv_message_count"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_gravity="end"
        android:background="@drawable/shape_red_dot"
        android:gravity="center"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        android:visibility="gone" />

</FrameLayout>