<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_badge"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_18"
        android:minWidth="@dimen/dp_18"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_2"
        android:background="@drawable/base_shape_oval_primary"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp_3"
        android:includeFontPadding="false"
        android:text="100"
        android:textColor="@color/colorWhite" />

</LinearLayout>