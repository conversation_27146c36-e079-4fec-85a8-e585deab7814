<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rc_conversation_item"
    android:layout_width="match_parent"
    android:layout_height="@dimen/rc_conversation_item_height">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/rc_conversation_portrait_rl"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/rc_conversation_portrait"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:scaleType="centerCrop" />


        </RelativeLayout>

        <View
            android:id="@+id/top_label_view"
            android:layout_width="3dp"
            android:layout_height="44dp"
            android:background="#9F2AF8"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/rc_conversation_portrait_rl"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/rc_conversation_portrait_rl" />
        <TextView
            android:id="@+id/rc_conversation_title"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/dp_150"
            android:layout_marginStart="@dimen/dp_8"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_16"
            app:layout_constraintStart_toEndOf="@+id/rc_conversation_portrait_rl"
            app:layout_constraintTop_toTopOf="@id/rc_conversation_portrait_rl"  />

        <com.mobile.anchor.app.ui.view.LevelLabelView
            android:id="@+id/level_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            app:layout_constraintStart_toEndOf="@id/rc_conversation_title"
            app:layout_constraintTop_toTopOf="@id/rc_conversation_title" />

        <TextView
            android:id="@+id/rc_conversation_content"
            style="@style/TextStyle.Alignment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="60dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="#60ffffff"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/rc_conversation_portrait_rl"
            app:layout_constraintTop_toBottomOf="@+id/rc_conversation_title" />

        <TextView
            android:id="@+id/rc_conversation_date"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/rc_margin_size_12"
            android:textColor="#40ffffff"
            android:textSize="10sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/rc_conversation_title" />

        <ImageView
            android:id="@+id/rc_conversation_no_disturb"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/rc_margin_size_12"
            android:layout_marginBottom="@dimen/rc_margin_size_12"
            android:src="@drawable/rc_no_disturb"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <RelativeLayout
            android:id="@+id/rc_conversation_unread"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/rc_margin_size_12"
            android:layout_marginBottom="@dimen/rc_margin_size_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:id="@+id/rc_conversation_unread_bg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:src="@drawable/rc_unread_count_bg_normal" />

            <TextView
                android:id="@+id/rc_conversation_unread_count"
                style="@style/TextStyle.Alignment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/rc_white_color"
                android:textSize="9sp" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/rc_conversation_read_receipt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/rc_margin_size_2"
            android:layout_marginBottom="@dimen/rc_margin_size_12"
            android:src="@drawable/rc_read_receipt"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/rc_conversation_no_disturb"
            app:layout_goneMarginEnd="@dimen/rc_margin_size_12" />

        <View
            android:id="@+id/divider"
            android:layout_width="wrap_content"
            android:layout_height="0.5dp"
            android:layout_marginTop="71.5dp"
            android:background="@color/rc_divider_color"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/rc_conversation_title"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>