<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp"
    android:elevation="8dp"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp_16">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/dp_12"
        android:background="@drawable/shape_global_notification_bg"
        android:paddingHorizontal="@dimen/dp_16">

        <!-- 用户头像 -->
        <androidx.cardview.widget.CardView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="12dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="0dp">

            <ImageView
                android:id="@+id/iv_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@mipmap/ic_default_avatar" />

        </androidx.cardview.widget.CardView>

        <!-- 消息内容区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 标题和发送者名称 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="新消息" />

                <TextView
                    android:id="@+id/tv_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="#CCFFFFFF"
                    android:textSize="12sp"
                    tools:text="刚刚" />

            </LinearLayout>

            <!-- 消息内容 -->
            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/white"
                android:textSize="13sp" />

        </LinearLayout>

        <!-- 关闭按钮 -->
        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:src="@drawable/ic_close_white"
            android:visibility="gone" />
    </LinearLayout>


</LinearLayout>
