<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/CL"
    android:padding="6dp"
    android:background="#201831"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <TextView
        android:id="@+id/nickName"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:maxWidth="@dimen/dp_120"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_height="wrap_content"
        tools:text="LiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLiLi"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_16"
        />

    <TextView
        android:paddingLeft="@dimen/dp_9"
        android:paddingRight="@dimen/dp_9"
        android:layout_marginStart="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_5"
        app:layout_constraintTop_toTopOf="@+id/nickName"
        app:layout_constraintBottom_toBottomOf="@+id/nickName"
        app:layout_constraintLeft_toRightOf="@+id/nickName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/send_to_you"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_11"
        android:id="@+id/direction"
        />

    <ImageView
        android:layout_marginStart="@dimen/dp_5"
        android:layout_marginEnd="@dimen/dp_5"
        app:layout_constraintTop_toTopOf="@+id/direction"
        app:layout_constraintBottom_toBottomOf="@+id/direction"
        app:layout_constraintLeft_toRightOf="@+id/direction"
        android:layout_width="@dimen/dp_42"
        android:layout_height="@dimen/dp_31"
        android:id="@+id/gift"
        />

</androidx.constraintlayout.widget.ConstraintLayout>