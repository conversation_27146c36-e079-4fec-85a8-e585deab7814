package io.rong.imkit

import android.view.View
import androidx.core.content.ContextCompat
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.view.LevelLabelView
import com.mobile.anchor.app.utils.fromJson
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversationlist.model.BaseUiConversation
import io.rong.imkit.conversationlist.provider.BaseConversationProvider
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.userinfo.db.model.User
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message.SentStatus
import io.rong.message.RecallNotificationMessage

/**
 * Author:Lxf
 * Create on:2024/8/5
 * Description:
 */
class MyCustomConversationProvider : BaseConversationProvider() {
    override fun isItemViewType(item: BaseUiConversation): Boolean {
        return Conversation.ConversationType.PRIVATE == item.mCore.conversationType
    }

    override fun bindViewHolder(
        holder: ViewHolder,
        uiConversation: BaseUiConversation,
        position: Int,
        list: List<BaseUiConversation?>?,
        listener: IViewProviderListener<BaseUiConversation?>?
    ) {
        super.bindViewHolder(holder, uiConversation, position, list, listener)
        if (RongConfigCenter.featureConfig()
                .isReadReceiptConversationType(Conversation.ConversationType.PRIVATE) && RongConfigCenter.conversationConfig()
                .isShowReadReceipt(Conversation.ConversationType.PRIVATE) && uiConversation.mCore.senderUserId == RongIMClient.getInstance().currentUserId && uiConversation.mCore.sentStatus.value == SentStatus.READ.value && uiConversation.mCore.latestMessage !is RecallNotificationMessage
        ) {
            holder.setVisible(R.id.rc_conversation_read_receipt, true)
        } else {
            holder.setVisible(R.id.rc_conversation_read_receipt, false)
        }
        holder.convertView.setBackgroundColor(ContextCompat.getColor(holder.context, R.color.picture_color_transparent))
        if (uiConversation.mCore.isTop) {
            holder.getView<View>(com.mobile.anchor.app.R.id.top_label_view).visibility = View.VISIBLE
        } else {
            holder.getView<View>(com.mobile.anchor.app.R.id.top_label_view).visibility = View.GONE
//            holder.convertView.setBackgroundColor(ContextCompat.getColor(holder.context, R.color.picture_color_transparent))
        }
        holder.setVisible(R.id.divider, false)

        val levelView = holder.getView<LevelLabelView>(com.mobile.anchor.app.R.id.level_label)
        val userInfo = RongUserInfoManager.getInstance().getUserInfo(uiConversation.mCore.targetId)
        userInfo?.extra?.let {
            if (it.isEmpty()) return@let
            val tmpUserBean  = it.fromJson<UserBean>()
            if (tmpUserBean != null) {
                levelView.current = tmpUserBean.userLevelConfig?.level ?: 0
            }
        }
    }
}