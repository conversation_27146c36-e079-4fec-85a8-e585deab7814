package io.rong.imkit.conversation.extension

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import com.lxj.xpopup.core.BasePopupView
import com.mobile.anchor.app.R
import com.mobile.anchor.app.popup.showGiftPopup
import com.mobile.anchor.app.ui.conversation.MyRongConversationActivity
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
class MyGiftPlugin : IPluginModule {
    private var giftPopup: BasePopupView? = null
    override fun obtainDrawable(context: Context): Drawable? =
        ResourcesCompat.getDrawable(context.resources, R.mipmap.ic_message_gift, null)

    override fun obtainTitle(context: Context): String = context.getString(R.string.label_gift)

    override fun onClick(currentFragment: Fragment, extension: RongExtension, index: Int) {
        currentFragment.activity?.let {
            giftPopup = giftPopup?.show() ?: showGiftPopup(
                it,
                (it as? MyRongConversationActivity)?.getTargetId() ?: "0"
            ) { giftBean ->
                val myRongConversationActivity = it as MyRongConversationActivity
                myRongConversationActivity.sendGift(giftBean)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent) {
    }
}