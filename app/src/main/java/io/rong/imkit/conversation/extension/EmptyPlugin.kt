package io.rong.imkit.conversation.extension

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import com.lxj.xpopup.core.BasePopupView
import com.mobile.anchor.app.R
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
class EmptyPlugin : IPluginModule {
    override fun obtainDrawable(context: Context): Drawable? =
        ResourcesCompat.getDrawable(context.resources, com.bdc.android.library.R.color.transparent, null)

    override fun obtainTitle(context: Context): String = ""

    override fun onClick(currentFragment: Fragment, extension: RongExtension, index: Int) {
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent) {
    }
}