package io.rong.imkit.conversation.extension;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.Intent;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import java.util.Iterator;

import io.rong.common.rlog.RLog;
import io.rong.imkit.R;
import io.rong.imkit.conversation.extension.component.emoticon.EmoticonBoard;
import io.rong.imkit.conversation.extension.component.moreaction.MoreInputPanel;
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule;
import io.rong.imkit.conversation.extension.component.plugin.IPluginRequestPermissionResultCallback;
import io.rong.imkit.conversation.messgelist.viewmodel.MessageViewModel;
import io.rong.imkit.event.uievent.InputBarEvent;
import io.rong.imkit.event.uievent.PageEvent;
import io.rong.imkit.feature.destruct.DestructManager;
import io.rong.imkit.feature.mention.IExtensionEventWatcher;
import io.rong.imkit.feature.mention.RongMentionManager;
import io.rong.imkit.utils.PermissionCheckUtil;
import io.rong.imkit.utils.RongUtils;
import io.rong.imkit.utils.RongViewUtils;
import io.rong.imkit.utils.keyboard.KeyboardHeightObserver;
import io.rong.imkit.utils.keyboard.KeyboardHeightProvider;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.ConversationIdentifier;

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
public class MyRongExtension extends RongExtension {
    private String TAG = RongExtension.class.getSimpleName();
    private Fragment mFragment;
    private ConversationIdentifier mConversationIdentifier;
    private ViewGroup mRoot;
    private RongExtensionViewModel mExtensionViewModel;
    private MessageViewModel mMessageViewModel;
    private RelativeLayout mAttachedInfoContainer;
    private RelativeLayout mBoardContainer;
    private RelativeLayout mInputPanelContainer;
    private MyInputPanel mInputPanel;
    private EmoticonBoard mEmoticonBoard;
    private MyPluginBoard mPluginBoard;
    private MyInputPanel.InputStyle mInputStyle;
    private MoreInputPanel mMoreInputPanel;
    private InputMode mPreInputMode;
    private KeyboardHeightProvider keyboardHeightProvider = null;
    private boolean editTextIsFocused = false;
    private final KeyboardHeightObserver mKeyboardHeightObserver;

    public MyRongExtension(Context context) {
        super(context);
        this.mKeyboardHeightObserver = new NamelessClass_1();
        this.initView(context);
    }

    class NamelessClass_1 implements KeyboardHeightObserver {
        NamelessClass_1() {
        }

        public void onKeyboardHeightChanged(int orientation, boolean isOpen, int keyboardHeight) {
            if (getActivityFromView() != null) {
                if (isOpen) {
                    updateBoardContainerHeight(true, false);
                    int saveKeyBoardHeight = RongUtils.getSaveKeyBoardHeight(getContext(), orientation);
                    if (saveKeyBoardHeight != keyboardHeight) {
                        RongUtils.saveKeyboardHeight(getContext(), orientation, keyboardHeight);

                    }

                    mBoardContainer.setVisibility(VISIBLE);
                    mExtensionViewModel.getExtensionBoardState().setValue(true);
                } else if (mExtensionViewModel != null) {
                    if (mExtensionViewModel.isSoftInputShow()) {
                        mExtensionViewModel.setSoftInputKeyBoard(false, false);
                    }

                    if ((mPreInputMode == InputMode.TextInput || mPreInputMode == InputMode.VoiceInput || mPreInputMode == InputMode.NormalMode)) {
//                        mBoardContainer.setVisibility(View.GONE);
                        updateBoardContainerHeight(false, false);
                        mExtensionViewModel.getExtensionBoardState().setValue(false);
                    }
                }
            }

        }
    }

    public MyRongExtension(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mKeyboardHeightObserver = new NamelessClass_1();
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.RongExtension);
        int attr = a.getInt(R.styleable.RongExtension_RCStyle, 291);
        a.recycle();
        this.mInputStyle = MyInputPanel.InputStyle.getStyle(attr);
        this.initView(context);
    }

    private void initView(Context context) {
        this.mRoot = (LinearLayout) LayoutInflater.from(context).inflate(R.layout.rc_extension_board, this, true);
        this.mAttachedInfoContainer = this.mRoot.findViewById(R.id.rc_ext_attached_info_container);
        this.mInputPanelContainer = this.mRoot.findViewById(R.id.rc_ext_input_container);
        this.mBoardContainer = this.mRoot.findViewById(R.id.rc_ext_board_container);
    }

    public void bindToConversation(Fragment fragment, ConversationIdentifier conversationIdentifier, boolean disableSystemEmoji) {
        this.mFragment = fragment;
        this.mConversationIdentifier = conversationIdentifier;
        this.mExtensionViewModel = (RongExtensionViewModel) (new ViewModelProvider(this.mFragment)).get(RongExtensionViewModel.class);
        this.mExtensionViewModel.getAttachedInfoState().observe(this.mFragment, new Observer<Boolean>() {
            public void onChanged(Boolean isVisible) {
                mAttachedInfoContainer.setVisibility(isVisible ? View.VISIBLE : View.GONE);
            }
        });
        this.mExtensionViewModel.getExtensionBoardState().observe(this.mFragment, new Observer<Boolean>() {
            public void onChanged(Boolean value) {
                RLog.d(TAG, "getExtensionBoardState tonChanged:" + value);
                if (!value) {
//                    mBoardContainer.setVisibility(GONE);
                }

            }
        });
        this.mMessageViewModel = (MessageViewModel) (new ViewModelProvider(this.mFragment)).get(MessageViewModel.class);
        this.mMessageViewModel.getPageEventLiveData().observe(this.mFragment, new Observer<PageEvent>() {
            public void onChanged(PageEvent pageEvent) {
                if (pageEvent instanceof InputBarEvent) {
                    if (((InputBarEvent) pageEvent).mType.equals(InputBarEvent.Type.ReEdit)) {
                        insertToEditText(((InputBarEvent) pageEvent).mExtra);
                        mExtensionViewModel.getInputModeLiveData().postValue(InputMode.TextInput);
                    } else if (((InputBarEvent) pageEvent).mType.equals(InputBarEvent.Type.ShowMoreMenu)) {
                        mExtensionViewModel.getInputModeLiveData().postValue(InputMode.MoreInputMode);
                    } else if (((InputBarEvent) pageEvent).mType.equals(InputBarEvent.Type.HideMoreMenu)) {
                        if (DestructManager.isActive()) {
                            DestructManager.getInstance().activeDestructMode(getContext());
                            mAttachedInfoContainer.removeAllViews();
                            mAttachedInfoContainer.setVisibility(GONE);
                        } else {
                            resetToDefaultView(((InputBarEvent) pageEvent).mExtra);
                        }
                    } else if (((InputBarEvent) pageEvent).mType.equals(InputBarEvent.Type.ActiveMoreMenu) && mMoreInputPanel != null) {
                        mMoreInputPanel.refreshView(true);
                    } else if (((InputBarEvent) pageEvent).mType.equals(InputBarEvent.Type.InactiveMoreMenu) && mMoreInputPanel != null) {
                        mMoreInputPanel.refreshView(false);
                    }
                }

            }
        });
        this.mEmoticonBoard = new EmoticonBoard(fragment, this.mBoardContainer, this.getConversationType(), this.getTargetId(), disableSystemEmoji);
        this.mPluginBoard = new MyPluginBoard(fragment, this.mBoardContainer, this.getConversationType(), this.getTargetId());
        this.mInputPanel = new MyInputPanel(fragment, this.mInputPanelContainer, this.mInputStyle, this.mConversationIdentifier);
        if (this.mInputPanelContainer.getChildCount() <= 0) {
            RongViewUtils.addView(this.mInputPanelContainer, this.mInputPanel.getRootView());
        }

        this.mExtensionViewModel.setAttachedConversation(conversationIdentifier, this.mInputPanel.getEditText());
        this.mExtensionViewModel.getInputModeLiveData().observe(this.mFragment, new Observer<InputMode>() {
            public void onChanged(InputMode inputMode) {
                if (mPreInputMode != inputMode){
                    mPreInputMode = inputMode;
                    updateInputMode(inputMode);
                }
            }
        });

        for (IExtensionModule module : RongExtensionManager.getInstance().getExtensionModules()) {
            module.onAttachedToExtension(fragment, this);
        }

    }

    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    public void onResume() {
        if (this.mExtensionViewModel != null) {
            if (this.useKeyboardHeightProvider()) {
                this.keyboardHeightProvider = new KeyboardHeightProvider(this.getActivityFromView());
                this.keyboardHeightProvider.setKeyboardHeightObserver(this.mKeyboardHeightObserver);
            }

            this.post(new Runnable() {
                public void run() {
                    KeyboardHeightProvider keyboardHeightProvider = MyRongExtension.this.keyboardHeightProvider;
                    if (keyboardHeightProvider != null) {
                        keyboardHeightProvider.start();
                    }

                }
            });
            final EditText editText = this.mExtensionViewModel.getEditTextWidget();
            if (editText != null) {
                if (this.editTextIsFocused) {
                    this.postDelayed(() -> {
                        editText.setSelection(editText.getText().toString().length());
                        editText.requestFocus();
                        mExtensionViewModel.forceSetSoftInputKeyBoard(true);
                    }, 200L);
                }

                if (editText.getText().length() > 0 || editText.isFocused()) {
                    editText.setOnKeyListener(new OnKeyListener() {
                        public boolean onKey(View v, int keyCode, KeyEvent event) {
                            if (keyCode == 67 && event.getAction() == 0) {
                                int cursorPos = editText.getSelectionStart();
                                RongMentionManager.getInstance().onDeleteClick(getConversationType(), getTargetId(), editText, cursorPos);
                            }

                            return false;
                        }
                    });
                }
            }

        }
    }

    public void onPause() {
        if (this.keyboardHeightProvider != null) {
            this.keyboardHeightProvider.stop();
            this.keyboardHeightProvider.setKeyboardHeightObserver((KeyboardHeightObserver) null);
            this.keyboardHeightProvider = null;
        }

        if (this.mExtensionViewModel != null) {
            if (this.mExtensionViewModel.getEditTextWidget() != null) {
                this.editTextIsFocused = this.mExtensionViewModel.getEditTextWidget().isFocused();
            }

            if (this.mPreInputMode != null && this.mPreInputMode == InputMode.TextInput && this.mBoardContainer != null) {
                this.mExtensionViewModel.collapseExtensionBoard();
            }
        }

        if (this.mInputPanel != null) {
            this.mInputPanel.onPause();
        }

    }

    public void setAttachedInfo(View view) {
        this.mAttachedInfoContainer.removeAllViews();
        if (view != null) {
            this.mAttachedInfoContainer.addView(view);
        }

        this.mAttachedInfoContainer.setVisibility(VISIBLE);
    }

    public RelativeLayout getContainer(RongExtension.ContainerType type) {
        if (type == null) {
            return null;
        } else if (type.equals(RongExtension.ContainerType.ATTACH)) {
            return this.mAttachedInfoContainer;
        } else {
            return type.equals(RongExtension.ContainerType.INPUT) ? this.mInputPanelContainer : this.mBoardContainer;
        }
    }

    public EmoticonBoard getEmoticonBoard() {
        return this.mEmoticonBoard;
    }

    public void resetToDefaultView() {
        this.resetToDefaultView((String) null);
        this.getInputPanel().getDraft();
    }

    public void resetToDefaultView(String conversationType) {
        if (!TextUtils.equals(conversationType, Conversation.ConversationType.PUBLIC_SERVICE.getName()) && !TextUtils.equals(conversationType, Conversation.ConversationType.APP_PUBLIC_SERVICE.getName())) {
            this.mInputPanelContainer.removeAllViews();
            if (this.mInputPanel == null) {
                this.mInputPanel = new MyInputPanel(this.mFragment, this.mInputPanelContainer, this.mInputStyle, this.mConversationIdentifier);
            }

            this.mExtensionViewModel.setEditTextWidget(this.mInputPanel.getEditText());
            RongViewUtils.addView(this.mInputPanelContainer, this.mInputPanel.getRootView());
            if (this.mFragment.getContext() != null) {
                this.mAttachedInfoContainer.removeAllViews();
                this.mAttachedInfoContainer.setVisibility(GONE);
                this.updateInputMode(InputMode.NormalMode);
            }

        } else {
            this.mInputPanelContainer.setVisibility(VISIBLE);
            Fragment fragment = this.mFragment;
            if (fragment != null && fragment.getContext() != null) {
                this.mAttachedInfoContainer.removeAllViews();
                this.mAttachedInfoContainer.setVisibility(GONE);
                this.mExtensionViewModel.getInputModeLiveData().postValue(InputMode.TextInput);
            }

        }
    }

    public void updateInputMode(InputMode inputMode) {
        if (inputMode != null) {
            RLog.d(this.TAG, "update to inputMode:" + inputMode);
            if (inputMode.equals(InputMode.TextInput)) {
                EditText editText = this.mExtensionViewModel.getEditTextWidget();
                if (editText == null || editText.getText() == null) {
                    return;
                }

                if (this.isEditTextSameProperty(editText)) {
                    return;
                }

                RLog.d(this.TAG, "update for TextInput mode");
                this.mInputPanelContainer.setVisibility(VISIBLE);
                this.updateBoardContainerHeight(false, false);
                this.mBoardContainer.removeAllViews();
                this.mBoardContainer.setVisibility(VISIBLE);
                RongViewUtils.addView(this.mBoardContainer, this.mPluginBoard.getView());
                if (!this.useKeyboardHeightProvider()) {
                    this.mExtensionViewModel.getExtensionBoardState().setValue(false);
                } else {
                    this.mExtensionViewModel.getExtensionBoardState().setValue(true);
                }

                if (!editText.isFocused() && editText.getText().length() <= 0) {
                    this.mExtensionViewModel.setSoftInputKeyBoard(false);
                    this.mExtensionViewModel.getExtensionBoardState().setValue(false);
                } else {
                    this.postDelayed(new Runnable() {
                        public void run() {
                            if (mFragment != null && mFragment.getActivity() != null && !mFragment.getActivity().isFinishing()) {
                                mExtensionViewModel.setSoftInputKeyBoard(true);
                            }

                        }
                    }, 100L);
                }
            } else if (inputMode.equals(InputMode.VoiceInput)) {
                this.postDelayed(new Runnable() {
                    public void run() {
                        updateBoardContainerHeight(false, false);
                    }
                }, 100L);
                this.mInputPanelContainer.setVisibility(VISIBLE);
//                this.mBoardContainer.setVisibility(GONE);
                this.mBoardContainer.removeAllViews();
                this.mBoardContainer.setVisibility(VISIBLE);
                RongViewUtils.addView(this.mBoardContainer, this.mPluginBoard.getView());
                this.mExtensionViewModel.forceSetSoftInputKeyBoard(false);
                this.mExtensionViewModel.getExtensionBoardState().setValue(false);
            } else if (inputMode.equals(InputMode.EmoticonMode)) {
                this.mExtensionViewModel.setSoftInputKeyBoard(false);
                this.postDelayed(new Runnable() {
                    public void run() {
                        updateBoardContainerHeight(false, true);
                        mBoardContainer.removeAllViews();
                        RongViewUtils.addView(mBoardContainer, mEmoticonBoard.getView());
                        mBoardContainer.setVisibility(VISIBLE);
                        mExtensionViewModel.getExtensionBoardState().setValue(true);
                    }
                }, 100L);
            } else if (inputMode.equals(InputMode.PluginMode)) {
                this.mExtensionViewModel.setSoftInputKeyBoard(false);
                this.postDelayed(new Runnable() {
                    public void run() {
                        updateBoardContainerHeight(false, false);
                        mBoardContainer.removeAllViews();
                        RongViewUtils.addView(mBoardContainer, mPluginBoard.getView());
                        mBoardContainer.setVisibility(VISIBLE);
                        mExtensionViewModel.forceSetSoftInputKeyBoard(false);
                        mExtensionViewModel.getExtensionBoardState().setValue(true);
                    }
                }, 100L);
            } else if (inputMode.equals(InputMode.MoreInputMode)) {
                this.mInputPanelContainer.setVisibility(VISIBLE);
//                this.mBoardContainer.setVisibility(GONE);
                if (this.mMoreInputPanel == null) {
                    this.mMoreInputPanel = new MoreInputPanel(this.mFragment, this.mAttachedInfoContainer);
                }

                this.mAttachedInfoContainer.removeAllViews();
                RongViewUtils.addView(this.mAttachedInfoContainer, this.mMoreInputPanel.getRootView());
                this.mAttachedInfoContainer.setVisibility(VISIBLE);
                this.mExtensionViewModel.setSoftInputKeyBoard(false);
                this.mExtensionViewModel.getExtensionBoardState().setValue(false);
            } else if (inputMode.equals(InputMode.QuickReplyMode)) {
                this.mInputPanelContainer.setVisibility(VISIBLE);
                this.mBoardContainer.setVisibility(VISIBLE);
                this.mExtensionViewModel.forceSetSoftInputKeyBoard(false);
                this.mExtensionViewModel.getExtensionBoardState().setValue(true);
            } else if (inputMode.equals(InputMode.NormalMode)) {
                this.mInputPanelContainer.setVisibility(VISIBLE);
                this.mBoardContainer.setVisibility(VISIBLE);
                this.mExtensionViewModel.forceSetSoftInputKeyBoard(false);
                this.mExtensionViewModel.getExtensionBoardState().setValue(false);
            }

        }
    }

    private void updateBoardContainerHeight(boolean isKeySoftShow, boolean isShowEmoji) {
        if (this.useKeyboardHeightProvider()) {
            int saveKeyboardHeight = RongUtils.getSaveKeyBoardHeight(this.getContext(), this.getContext().getResources().getConfiguration().orientation);
            if (saveKeyboardHeight <= 0) {
                saveKeyboardHeight = getResources().getDimensionPixelSize(R.dimen.rc_extension_board_height);
            }
            ViewGroup.LayoutParams layoutParams = mBoardContainer.getLayoutParams();
            if (isKeySoftShow && layoutParams.height != saveKeyboardHeight) {
                layoutParams.height = saveKeyboardHeight;
                mBoardContainer.setLayoutParams(layoutParams);
            } else if (isShowEmoji) {
                layoutParams.height = getResources().getDimensionPixelSize(R.dimen.rc_extension_board_height);
                mBoardContainer.setLayoutParams(layoutParams);
            } else if (layoutParams.height != getResources().getDimensionPixelSize(com.mobile.anchor.app.R.dimen.message_extension_board_height)) {
                layoutParams.height = getResources().getDimensionPixelSize(com.mobile.anchor.app.R.dimen.message_extension_board_height);
                mBoardContainer.setLayoutParams(layoutParams);
            }
//            if (saveKeyboardHeight <= 0 && layoutParams.height != this.getResources().getDimensionPixelSize(com.bdc.android.library.R.dimen.dp_110)) {
//            layoutParams.height = this.getResources().getDimensionPixelSize(com.bdc.android.library.R.dimen.dp_110);
//            this.mBoardContainer.setLayoutParams(layoutParams);
//            } else if (layoutParams.height != saveKeyboardHeight) {
//                layoutParams.height = saveKeyboardHeight;
//                this.mBoardContainer.setLayoutParams(layoutParams);
//            }

        }
    }

    public void collapseExtension() {
        RLog.d(this.TAG, "collapseExtension");
        this.mExtensionViewModel.collapseExtensionBoard();
    }

    public void addPluginPager(View v) {
        if (null != this.mPluginBoard) {
            this.mPluginBoard.addPager(v);
        }

    }

    public MyInputPanel getMyInputPanel() {
        return mInputPanel;
    }

    public EditText getInputEditText() {
        return this.mInputPanel != null ? this.mInputPanel.getEditText() : null;
    }

    public Conversation.ConversationType getConversationType() {
        if (this.mConversationIdentifier == null) {
            RLog.e(this.TAG, "getConversationType mConversationIdentifier is null");
            return Conversation.ConversationType.NONE;
        } else {
            return this.mConversationIdentifier.getType();
        }
    }

    public String getTargetId() {
        if (this.mConversationIdentifier == null) {
            RLog.e(this.TAG, "getTargetId mConversationIdentifier is null");
            return "";
        } else {
            return this.mConversationIdentifier.getTargetId();
        }
    }

    public ConversationIdentifier getConversationIdentifier() {
        return this.mConversationIdentifier;
    }

    public void requestPermissionForPluginResult(String[] permissions, int requestCode, IPluginModule pluginModule) {
        if ((requestCode & -256) != 0) {
            throw new IllegalArgumentException("requestCode must less than 256");
        } else if (null != this.mPluginBoard) {
            int position = this.mPluginBoard.getPluginPosition(pluginModule);
            int req = (position + 1 << 8) + (requestCode & 255);
            PermissionCheckUtil.requestPermissions(this.mFragment, permissions, req);
        }
    }

    public boolean onRequestPermissionResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        int position = (requestCode >> 8) - 1;
        int reqCode = requestCode & 255;
        if (null == this.mPluginBoard) {
            return false;
        } else {
            IPluginModule pluginModule = this.mPluginBoard.getPluginModule(position);
            return pluginModule instanceof IPluginRequestPermissionResultCallback ? ((IPluginRequestPermissionResultCallback) pluginModule).onRequestPermissionResult(this.mFragment, this, reqCode, permissions, grantResults) : false;
        }
    }

    public void startActivityForPluginResult(Intent intent, int requestCode, IPluginModule pluginModule) {
        if ((requestCode & -256) != 0) {
            throw new IllegalArgumentException("requestCode must less than 256.");
        } else if (null != this.mPluginBoard) {
            int position = this.mPluginBoard.getPluginPosition(pluginModule);
            this.mFragment.startActivityForResult(intent, (position + 1 << 8) + (requestCode & 255));
        }
    }

    public void onActivityPluginResult(int requestCode, int resultCode, Intent data) {
        int position = (requestCode >> 8) - 1;
        int reqCode = requestCode & 255;
        if (null != this.mPluginBoard) {
            IPluginModule pluginModule = this.mPluginBoard.getPluginModule(position);
            if (pluginModule != null) {
                pluginModule.onActivityResult(reqCode, resultCode, data);
            }

        }
    }

    public void onDestroy() {
        if (this.mInputPanel != null) {
            this.mInputPanel.onDestroy();
            RongMentionManager.getInstance().destroyInstance(this.getConversationType(), this.getTargetId(), this.getInputEditText());
        }

        Iterator var1 = RongExtensionManager.getInstance().getExtensionEventWatcher().iterator();

        while (var1.hasNext()) {
            IExtensionEventWatcher watcher = (IExtensionEventWatcher) var1.next();
            watcher.onDestroy(this.getConversationType(), this.getTargetId());
        }

        var1 = RongExtensionManager.getInstance().getExtensionModules().iterator();

        while (var1.hasNext()) {
            IExtensionModule extensionModule = (IExtensionModule) var1.next();
            extensionModule.onDetachedFromExtension();
        }

    }

    private void insertToEditText(String content) {
        EditText editText = this.mExtensionViewModel.getEditTextWidget();
        int len = content.length();
        int cursorPos = editText.getSelectionStart();
        editText.getEditableText().insert(cursorPos, content);
        editText.setSelection(cursorPos + len);
    }

    private boolean isEditTextSameProperty(EditText editText) {
        if (this.mPreInputMode == null) {
            return false;
        } else {
            return this.mPreInputMode.equals(InputMode.TextInput) && (editText.isFocused() || editText.getText().length() > 0) && this.mExtensionViewModel.isSoftInputShow();
        }
    }

    private Activity getActivityFromView() {
        for (Context context = this.getContext(); context instanceof ContextWrapper; context = ((ContextWrapper) context).getBaseContext()) {
            if (context instanceof Activity) {
                return (Activity) context;
            }
        }

        return null;
    }

    public boolean useKeyboardHeightProvider() {
        Activity activity = this.getActivityFromView();
        return activity != null && !activity.isInMultiWindowMode();
    }

    public static enum ContainerType {
        ATTACH,
        INPUT,
        BOARD;

        private ContainerType() {
        }
    }
}
