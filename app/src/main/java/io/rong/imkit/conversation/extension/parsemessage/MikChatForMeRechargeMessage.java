package io.rong.imkit.conversation.extension.parsemessage;

import android.os.Parcel;
import android.text.TextUtils;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;

import io.rong.common.ParcelUtils;
import io.rong.imlib.MessageTag;
import io.rong.imlib.model.MessageContent;

@MessageTag(value = "VC:ForMeRecharge", flag = MessageTag.ISCOUNTED)
public class MikChatForMeRechargeMessage extends MessageContent {
    private static final String TAG = "MikChatSupportHerMessage";
    // 自定义消息变量，可以有多个
    public String anchor_id;  //主播id
    public String anchor_content; //给主播看的内容
    public String user_content;//给用户看的内容

    private MikChatForMeRechargeMessage() {
    }

    /**
     * 构造函数。
     *
     * @param in 初始化传入的 Parcel。
     */
    public MikChatForMeRechargeMessage(Parcel in) {
        setExtra(ParcelUtils.readFromParcel(in));
//        setContent(ParcelUtils.readFromParcel(in));
        anchor_id = ParcelUtils.readFromParcel(in);
        anchor_content = ParcelUtils.readFromParcel(in);
        user_content = ParcelUtils.readFromParcel(in);
    }

    public static MikChatForMeRechargeMessage obtain(String anchorId, String anchorContent, String userContent) {
        MikChatForMeRechargeMessage msg = new MikChatForMeRechargeMessage();
        msg.anchor_id = anchorId;
        msg.anchor_content = anchorContent;
        msg.user_content = userContent;
        return msg;
    }

    /**
     * 创建 MikChatMessage(byte[] data) 带有 byte[] 的构造方法用于解析消息内容.
     */
    public MikChatForMeRechargeMessage(byte[] data) {
        if (data == null) {
            return;
        }
        String jsonStr = null;
        try {
            jsonStr = new String(data, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException ", e);
        }
        if (jsonStr == null) {
            Log.e(TAG, "jsonStr is null ");
            return;
        }

        try {
            JSONObject jsonObj = new JSONObject(jsonStr);
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (jsonObj.has("user")) {
                setUserInfo(parseJsonToUserInfo(jsonObj.getJSONObject("user")));
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (jsonObj.has("mentionedInfo")) {
                setMentionedInfo(parseJsonToMentionInfo(jsonObj.getJSONObject("mentionedInfo")));
            }
            // 将所有自定义变量从收到的 json 解析并赋值
            if (jsonObj.has("anchor_id")) {
                anchor_id = jsonObj.optString("anchor_id");
            }
            if (jsonObj.has("anchor_content")) {
                anchor_content = jsonObj.optString("anchor_content");
            }
            if (jsonObj.has("user_content")) {
                user_content = jsonObj.optString("user_content");
            }

        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }
    }

    /**
     * 将本地消息对象序列化为消息数据。
     *
     * @return 消息数据。
     * <p>
     * {"type":"1","channelId":"1585bd70f4de488fac95741485803dce","userId":"1663916137398743042","userCategory":"B"}
     */
    @Override
    public byte[] encode() {
        JSONObject jsonObj = new JSONObject();
        try {
            // 消息携带用户信息时, 自定义消息需添加下面代码
            if (getJSONUserInfo() != null) {
                jsonObj.putOpt("user", getJSONUserInfo());
            }
            // 用于群组聊天, 消息携带 @ 人信息时, 自定义消息需添加下面代码
            if (getJsonMentionInfo() != null) {
                jsonObj.putOpt("mentionedInfo", getJsonMentionInfo());
            }

            if (!TextUtils.isEmpty(this.getExtra())) {
                jsonObj.put("extra", this.getExtra());
            }

            //  将所有自定义消息的内容，都序列化至 json 对象中
            jsonObj.put("anchor_id", this.anchor_id);
            jsonObj.put("anchor_content", this.anchor_content);
            jsonObj.put("user_content", this.user_content);
        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }

        try {
            return jsonObj.toString().getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "UnsupportedEncodingException ", e);
        }
        return null;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int i) {
        // 对消息属性进行序列化，将类的数据写入外部提供的 Parcel 中
        ParcelUtils.writeToParcel(dest, getExtra());
        ParcelUtils.writeToParcel(dest, anchor_id);
        ParcelUtils.writeToParcel(dest, anchor_content);
        ParcelUtils.writeToParcel(dest, user_content);
    }

    public static final Creator<MikChatForMeRechargeMessage> CREATOR =
            new Creator<MikChatForMeRechargeMessage>() {
                public MikChatForMeRechargeMessage createFromParcel(Parcel source) {
                    return new MikChatForMeRechargeMessage(source);
                }

                public MikChatForMeRechargeMessage[] newArray(int size) {
                    return new MikChatForMeRechargeMessage[size];
                }
            };
}