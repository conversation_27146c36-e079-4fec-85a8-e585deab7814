package io.rong.imkit.conversation.extension.provider

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.jump
import com.mobile.anchor.app.R
import com.mobile.anchor.app.ui.activities.ReferralProfitActivity
import com.mobile.anchor.app.ui.conversation.MyRongConversationActivity
import io.rong.imkit.conversation.extension.parsemessage.MikChatForMeRechargeMessage
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent

/**
 * Author:Lxf
 * Create on:2024/8/19
 * Description:
 */
class ForMeRechargeMessageItemProvider : BaseMessageItemProvider<MikChatForMeRechargeMessage>() {
    override fun getSummarySpannable(context: Context, t: MikChatForMeRechargeMessage?): Spannable {
        return SpannableString(context.getString(R.string.title_message_support_me))
    }

    override fun onCreateMessageContentViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.rc_for_me_recharge_message_item, parent, false)
        return ViewHolder(parent.context, view)
    }

    override fun isMessageViewType(messageContent: MessageContent): Boolean {
        return messageContent is MikChatForMeRechargeMessage && !messageContent.isDestruct
    }

    override fun onItemClick(
        holder: ViewHolder?,
        t: MikChatForMeRechargeMessage?,
        uiMessage: UiMessage?,
        position: Int,
        list: MutableList<UiMessage>?,
        listener: IViewProviderListener<UiMessage>?
    ): Boolean {
        return false
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder,
        message: MikChatForMeRechargeMessage,
        uiMessage: UiMessage,
        position: Int,
        list: MutableList<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        holder.apply {
            getView<TextView>(R.id.tv_recharge_for_me_content).text = message.anchor_content
            val isSender = uiMessage.message.messageDirection == Message.MessageDirection.SEND
            setBackgroundRes(R.id.CL, if (isSender) R.drawable.ic_gift_right_bg else R.drawable.ic_gift_left_bg)

            getView<View>(R.id.btn_recharge_detail).click {
//                if (holder.context is MyRongConversationActivity) {
//                    val conversationActivity = holder.context as MyRongConversationActivity
//                    conversationActivity.supportHer()
//                }
                holder.context.jump(ReferralProfitActivity::class.java)
            }
        }
    }

    override fun showBubble(): Boolean {
        return false
    }
}