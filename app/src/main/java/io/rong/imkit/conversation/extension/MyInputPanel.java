package io.rong.imkit.conversation.extension;


import android.annotation.SuppressLint;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.bdc.android.library.utils.ToastUtil;

import java.lang.ref.WeakReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.rong.common.rlog.RLog;
import io.rong.imkit.IMCenter;
import io.rong.imkit.R;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.feature.destruct.DestructManager;
import io.rong.imkit.feature.mention.IExtensionEventWatcher;
import io.rong.imkit.feature.mention.RongMentionManager;
import io.rong.imkit.widget.RongEditText;
import io.rong.imlib.ChannelClient;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.IRongCoreCallback;
import io.rong.imlib.IRongCoreEnum;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.ConversationIdentifier;
import io.rong.imlib.model.Message;
import io.rong.message.TextMessage;

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
public class MyInputPanel {
    private final String TAG = this.getClass().getSimpleName();
    private Context mContext;
    private ConversationIdentifier mConversationIdentifier;
    private InputStyle mInputStyle;
    private Fragment mFragment;
    private View mInputPanel;
    private boolean mIsVoiceInputMode;
    private ImageView mVoiceToggleBtn;
    private EditText mEditText;
    private TextView mVoiceInputBtn;
    private ImageView mEmojiToggleBtn;
    private Button mSendBtn;
    //    private ImageView mAddBtn;
    private ViewGroup mAddOrSendBtn;
    private RongExtensionViewModel mExtensionViewModel;
    private String mInitialDraft = "";
    private float mLastTouchY;
    private boolean mUpDirection;
    private final View.OnClickListener mOnSendBtnClick = v -> {
        if (MyInputPanel.this.mExtensionViewModel != null) {
                onSendClick();
        }
    };


    //自定义发送文本 加入过滤词和屏蔽词逻辑
    private void onSendClick() {
        if (!TextUtils.isEmpty(this.mEditText.getText()) && !TextUtils.isEmpty(this.mEditText.getText().toString().trim())) {
            String text = this.mEditText.getText().toString();
            if (text.length() > 5500) {
                ToastUtil.show(mContext.getString(R.string.rc_message_too_long));
                RLog.d(this.TAG, "The text you entered is too long to send.");
            } else {
                this.mEditText.setText("");

                if (AndroidEmoji.isEmoji(text)) {
                    String replacedText = AndroidEmoji.replaceEmojiWithText(text, true);
                    text = AndroidEmoji.ensure(replacedText).toString();
                }

                TextMessage textMessage = TextMessage.obtain(text);
                if (DestructManager.isActive()) {
                    int length = text.length();
                    long time;
                    if (length <= 20) {
                        time = 10L;
                    } else {
                        time = Math.round((double) (length - 20) * 0.5 + 10.0);
                    }

                    textMessage.setDestruct(true);
                    textMessage.setDestructTime(time);
                }

                Message message = Message.obtain(this.mConversationIdentifier, textMessage);
                RongMentionManager.getInstance().onSendToggleClick(message, this.mEditText);
                if (!RongExtensionManager.getInstance().getExtensionEventWatcher().isEmpty()) {

                    for (IExtensionEventWatcher watcher : RongExtensionManager.getInstance().getExtensionEventWatcher()) {
                        watcher.onSendToggleClick(message);
                    }
                }

                message.setCanIncludeExpansion(true);
                IMCenter.getInstance().sendMessage(message, DestructManager.isActive() ? mContext.getResources().getString(R.string.rc_conversation_summary_content_burn) : null, (String) null, (IRongCallback.ISendMessageCallback) null);
            }
        } else {
            RLog.d(this.TAG, "can't send empty content.");
            this.mEditText.setText("");
        }
    }

    private final View.OnFocusChangeListener mOnEditTextFocusChangeListener = (v, hasFocus) -> {
        if (hasFocus) {
            if (MyInputPanel.this.mExtensionViewModel != null && MyInputPanel.this.mExtensionViewModel.getInputModeLiveData() != null) {
                MyInputPanel.this.mExtensionViewModel.getInputModeLiveData().postValue(InputMode.TextInput);
            }

            if (!TextUtils.isEmpty(MyInputPanel.this.mEditText.getText())) {
                MyInputPanel.this.mSendBtn.setVisibility(View.VISIBLE);
//                MyInputPanel.this.mAddBtn.setVisibility(View.GONE);
            }
        } else if (MyInputPanel.this.mExtensionViewModel != null) {
//            EditText editText = MyInputPanel.this.mExtensionViewModel.getEditTextWidget();
//            if (editText.getText() != null && editText.getText().length() == 0) {
//                MyInputPanel.this.mSendBtn.setVisibility(View.GONE);
//                MyInputPanel.this.mAddBtn.setVisibility(View.VISIBLE);
//            }
        }

    };
    private final TextWatcher mEditTextWatcher = new TextWatcher() {
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if (s != null && s.length() != 0) {
                MyInputPanel.this.mAddOrSendBtn.setVisibility(View.VISIBLE);
                MyInputPanel.this.mSendBtn.setVisibility(View.VISIBLE);
                MyInputPanel.this.mSendBtn.setEnabled(true);
//                MyInputPanel.this.mSendBtn.setTextColor(ContextCompat.getColor(mContext, com.heart.heartmerge.R.color.color_white));
//                MyInputPanel.this.mAddBtn.setVisibility(View.GONE);
            } else {
                MyInputPanel.this.mSendBtn.setEnabled(false);
//                MyInputPanel.this.mSendBtn.setTextColor(ContextCompat.getColor(mContext, com.heart.heartmerge.R.color.color_94969C));
                MyInputPanel.this.saveTextMessageDraft(MyInputPanel.this.mEditText.getText().toString());
                if (!MyInputPanel.this.mInputStyle.equals(InputStyle.STYLE_CONTAINER_EXTENSION) && !MyInputPanel.this.mInputStyle.equals(InputStyle.STYLE_SWITCH_CONTAINER_EXTENSION)) {
                    MyInputPanel.this.mAddOrSendBtn.setVisibility(View.VISIBLE);
                } else {
                    MyInputPanel.this.mAddOrSendBtn.setVisibility(View.VISIBLE);
//                    MyInputPanel.this.mAddBtn.setVisibility(View.VISIBLE);
//                    MyInputPanel.this.mSendBtn.setVisibility(View.GONE);
                }
            }

            int offset;
            if (count == 0) {
                int var10000 = start + before;
                offset = -before;
            } else {
                offset = count;
            }

            if (Conversation.ConversationType.PRIVATE.equals(MyInputPanel.this.mConversationIdentifier.getType()) && offset != 0) {
                RongIMClient.getInstance().sendTypingStatus(MyInputPanel.this.mConversationIdentifier.getType(), MyInputPanel.this.mConversationIdentifier.getTargetId(), "RC:TxtMsg");
            }

        }

        public void afterTextChanged(Editable s) {
        }
    };

    public MyInputPanel(Fragment fragment, ViewGroup parent, InputStyle inputStyle, ConversationIdentifier conversationIdentifier) {
        this.mFragment = fragment;
        this.mInputStyle = inputStyle;
        this.mConversationIdentifier = conversationIdentifier;
        this.initView(fragment.getContext(), parent);
        this.mExtensionViewModel = (new ViewModelProvider(fragment)).get(RongExtensionViewModel.class);
        this.mExtensionViewModel.getInputModeLiveData().observe(fragment.getViewLifecycleOwner(), new Observer<InputMode>() {
            public void onChanged(InputMode inputMode) {
                MyInputPanel.this.updateViewByInputMode(inputMode);
            }
        });
        if (fragment.getContext() != null) {
            this.mIsVoiceInputMode = RongExtensionCacheHelper.isVoiceInputMode(fragment.getContext(), conversationIdentifier.getType(), conversationIdentifier.getTargetId());
        }

        if (this.mIsVoiceInputMode) {
            this.mExtensionViewModel.getInputModeLiveData().setValue(InputMode.VoiceInput);
        } else {
            this.mExtensionViewModel.getInputModeLiveData().setValue(InputMode.TextInput);
        }

    }

    @SuppressLint({"ClickableViewAccessibility"})
    private void initView(final Context context, ViewGroup parent) {
        this.mContext = context;
        this.mInputPanel = LayoutInflater.from(context).inflate(R.layout.rc_extension_input_panel, parent, false);
        this.mVoiceToggleBtn = this.mInputPanel.findViewById(R.id.input_panel_voice_toggle);
        this.mEditText = this.mInputPanel.findViewById(R.id.edit_btn);
        this.mVoiceInputBtn = this.mInputPanel.findViewById(R.id.press_to_speech_btn);
        this.mEmojiToggleBtn = this.mInputPanel.findViewById(R.id.input_panel_emoji_btn);
        this.mAddOrSendBtn = this.mInputPanel.findViewById(R.id.input_panel_add_or_send);
        this.mSendBtn = this.mInputPanel.findViewById(R.id.input_panel_send_btn);
//        this.mAddBtn = this.mInputPanel.findViewById(R.id.input_panel_add_btn);
        this.mSendBtn.setOnClickListener(this.mOnSendBtnClick);
        this.mEditText.setOnFocusChangeListener(this.mOnEditTextFocusChangeListener);
        this.mEditText.addTextChangedListener(this.mEditTextWatcher);
        this.mVoiceToggleBtn.setOnClickListener(v -> {
                if (MyInputPanel.this.mExtensionViewModel != null) {
                    if (MyInputPanel.this.mIsVoiceInputMode) {
                        MyInputPanel.this.mIsVoiceInputMode = false;
                        MyInputPanel.this.mExtensionViewModel.getInputModeLiveData().setValue(InputMode.TextInput);
                        MyInputPanel.this.mEditText.requestFocus();
                    } else {
                        MyInputPanel.this.mIsVoiceInputMode = true;
                        MyInputPanel.this.mExtensionViewModel.getInputModeLiveData().postValue(InputMode.VoiceInput);
                    }

//                RongExtensionCacheHelper.saveVoiceInputMode(context, MyInputPanel.this.mConversationIdentifier.getType(), MyInputPanel.this.mConversationIdentifier.getTargetId(), MyInputPanel.this.mIsVoiceInputMode);
                }
        });

        this.mEmojiToggleBtn.setOnClickListener(v -> {
            if (mExtensionViewModel != null) {
                InputMode inputMode = mExtensionViewModel.getInputModeLiveData().getValue();
                if (inputMode != null && inputMode.equals(InputMode.EmoticonMode)) {
                    mEditText.requestFocus();
                    mExtensionViewModel.getInputModeLiveData().postValue(InputMode.TextInput);
                } else {
                    mExtensionViewModel.getInputModeLiveData().postValue(InputMode.EmoticonMode);
                }

            }
        });
//        this.mAddBtn.setOnClickListener(v -> {
//            if (MyInputPanel.this.mExtensionViewModel != null) {
//                if (MyInputPanel.this.mExtensionViewModel.getInputModeLiveData().getValue() != null && ((InputMode) MyInputPanel.this.mExtensionViewModel.getInputModeLiveData().getValue()).equals(InputMode.PluginMode)) {
//                    MyInputPanel.this.mEditText.requestFocus();
//                    MyInputPanel.this.mExtensionViewModel.getInputModeLiveData().setValue(InputMode.TextInput);
//                } else {
//                    MyInputPanel.this.mExtensionViewModel.getInputModeLiveData().setValue(InputMode.PluginMode);
//                    ReferenceManager.getInstance().hideReferenceView();
//                }
//
//            }
//        });
        if (TextUtils.isEmpty(this.mInitialDraft)) {
            this.getDraft();
        }

//        this.mVoiceInputBtn.setOnTouchListener(this.mOnVoiceBtnTouchListener);
        this.setInputPanelStyle(this.mInputStyle);
    }

    private void updateViewByInputMode(InputMode inputMode) {
        if (!inputMode.equals(InputMode.TextInput) && !inputMode.equals(InputMode.PluginMode)) {
            if (inputMode.equals(InputMode.VoiceInput)) {
                this.mVoiceToggleBtn.setImageDrawable(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.rc_ext_toggle_keyboard_btn, null));
                this.mVoiceInputBtn.setVisibility(View.VISIBLE);
                this.mEditText.setVisibility(View.GONE);
                mAddOrSendBtn.setVisibility(View.GONE);
                this.mEmojiToggleBtn.setImageDrawable(ResourcesCompat.getDrawable(mContext.getResources(), com.mobile.anchor.app.R.mipmap.rc_ext_input_panel_emoji, null));
//                if (!this.mInputStyle.equals(MyInputPanel.InputStyle.STYLE_CONTAINER_EXTENSION) && !this.mInputStyle.equals(MyInputPanel.InputStyle.STYLE_SWITCH_CONTAINER_EXTENSION)) {
//                    this.mAddOrSendBtn.setVisibility(View.GONE);
//                } else {
//                    this.mAddOrSendBtn.setVisibility(View.VISIBLE);
//                    this.mAddBtn.setVisibility(View.VISIBLE);
//                    this.mSendBtn.setVisibility(View.GONE);
//                }
            } else if (inputMode.equals(InputMode.EmoticonMode)) {
                this.mVoiceToggleBtn.setImageDrawable(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.rc_ext_toggle_voice_btn, null));
                this.mEmojiToggleBtn.setImageDrawable(ResourcesCompat.getDrawable(mContext.getResources(), com.mobile.anchor.app.R.mipmap.rc_ext_input_panel_emoji, null));
                this.mEditText.setVisibility(View.VISIBLE);
                this.mVoiceInputBtn.setVisibility(View.GONE);
                this.resetInputView();
            } else if (inputMode.equals(InputMode.QuickReplyMode)) {
                this.mIsVoiceInputMode = false;
                this.mVoiceToggleBtn.setImageDrawable(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.rc_ext_toggle_voice_btn, null));
                this.mEmojiToggleBtn.setImageDrawable(ResourcesCompat.getDrawable(mContext.getResources(), com.mobile.anchor.app.R.mipmap.rc_ext_input_panel_emoji, null));
                this.mEditText.setVisibility(View.VISIBLE);
                this.mVoiceInputBtn.setVisibility(View.GONE);
            } else if (inputMode.equals(InputMode.NormalMode)) {
                this.mIsVoiceInputMode = false;
                this.mVoiceToggleBtn.setImageDrawable(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.rc_ext_toggle_voice_btn, null));
                this.mEmojiToggleBtn.setImageDrawable(ResourcesCompat.getDrawable(mContext.getResources(), com.mobile.anchor.app.R.mipmap.rc_ext_input_panel_emoji, null));
                this.mEditText.setVisibility(View.VISIBLE);
                this.mVoiceInputBtn.setVisibility(View.GONE);
                this.resetInputView();
            }
        } else {
            if (inputMode.equals(InputMode.TextInput)) {
                this.mIsVoiceInputMode = false;
            }

            this.mVoiceToggleBtn.setImageDrawable(ResourcesCompat.getDrawable(mContext.getResources(), R.drawable.rc_ext_toggle_voice_btn, null));
            this.mEmojiToggleBtn.setImageDrawable(ResourcesCompat.getDrawable(mContext.getResources(), com.mobile.anchor.app.R.mipmap.rc_ext_input_panel_emoji, null));
            this.mEditText.setVisibility(View.VISIBLE);
            this.mVoiceInputBtn.setVisibility(View.GONE);
            this.resetInputView();
        }

    }

    private void resetInputView() {
        Editable text = this.mEditText.getText();
        if (text != null && text.length() != 0) {
            this.mAddOrSendBtn.setVisibility(View.VISIBLE);
            this.mSendBtn.setVisibility(View.VISIBLE);
//            this.mAddBtn.setVisibility(View.GONE);
        } else if (!this.mInputStyle.equals(InputStyle.STYLE_CONTAINER_EXTENSION) && !this.mInputStyle.equals(InputStyle.STYLE_SWITCH_CONTAINER_EXTENSION)) {
            this.mAddOrSendBtn.setVisibility(View.GONE);
        } else {
            this.mAddOrSendBtn.setVisibility(View.VISIBLE);
//            this.mAddBtn.setVisibility(View.VISIBLE);
//            this.mSendBtn.setVisibility(View.GONE);
        }

    }

    public void changeToVoiceStyle() {
        if (MyInputPanel.this.mExtensionViewModel != null) {
            if (MyInputPanel.this.mIsVoiceInputMode) {
                MyInputPanel.this.mIsVoiceInputMode = false;
                MyInputPanel.this.mExtensionViewModel.getInputModeLiveData().setValue(InputMode.TextInput);
                MyInputPanel.this.mEditText.requestFocus();
            } else {
                MyInputPanel.this.mExtensionViewModel.getInputModeLiveData().postValue(InputMode.VoiceInput);
                MyInputPanel.this.mIsVoiceInputMode = true;
            }

//            RongExtensionCacheHelper.saveVoiceInputMode(mContext, MyInputPanel.this.mConversationIdentifier.getType(), MyInputPanel.this.mConversationIdentifier.getTargetId(), MyInputPanel.this.mIsVoiceInputMode);
        }

    }

    public void changeToEmoji() {
        if (mExtensionViewModel != null) {
            InputMode inputMode = mExtensionViewModel.getInputModeLiveData().getValue();
            if (inputMode != null && inputMode.equals(InputMode.EmoticonMode)) {
                mEditText.requestFocus();
                mExtensionViewModel.getInputModeLiveData().postValue(InputMode.TextInput);
            } else {
                mExtensionViewModel.getInputModeLiveData().postValue(InputMode.EmoticonMode);
            }

        }
    }

    public EditText getEditText() {
        return this.mEditText;
    }

    public View getRootView() {
        return this.mInputPanel;
    }

    public void setVisible(int viewId, boolean visible) {
        this.mInputPanel.findViewById(viewId).setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    public void setInputPanelStyle(InputStyle style) {
        switch (style) {
            case STYLE_CONTAINER:
                this.setC();
                break;
            case STYLE_CONTAINER_EXTENSION:
                this.setCE();
                break;
            case STYLE_SWITCH_CONTAINER:
                this.setSC();
                break;
            default:
                this.setSCE();
        }

        this.mInputStyle = style;
    }

    private void setSCE() {
        if (this.mInputPanel != null) {
//            this.mVoiceToggleBtn.setVisibility(View.VISIBLE);
            this.mEmojiToggleBtn.setVisibility(this.shouldShowEmojiButton() ? View.VISIBLE : View.GONE);
//            this.mAddBtn.setVisibility(View.VISIBLE);
        }

    }

    private void setC() {
        if (this.mInputPanel != null) {
            this.mVoiceToggleBtn.setVisibility(View.GONE);
            this.mAddOrSendBtn.setVisibility(View.GONE);
            this.mEmojiToggleBtn.setVisibility(View.GONE);
//            this.mAddBtn.setVisibility(View.GONE);
//            this.mSendBtn.setVisibility(View.GONE);
        }

    }

    private void setCE() {
        if (this.mInputPanel != null) {
            this.mVoiceToggleBtn.setVisibility(View.GONE);
            this.mAddOrSendBtn.setVisibility(View.VISIBLE);
            this.mEmojiToggleBtn.setVisibility(this.shouldShowEmojiButton() ? View.VISIBLE : View.GONE);
//            this.mAddBtn.setVisibility(View.VISIBLE);
        }

    }

    private void setSC() {
        if (this.mInputPanel != null) {
            this.mVoiceToggleBtn.setVisibility(View.VISIBLE);
            this.mAddOrSendBtn.setVisibility(View.GONE);
//            this.mAddBtn.setVisibility(View.GONE);
        }

    }

    private boolean shouldShowEmojiButton() {
        return !RongConfigCenter.featureConfig().isHideEmojiButton();
//        return false;
    }

    public void getDraft() {
        WeakReference<MyInputPanel> weakThis = new WeakReference<>(this);
        ChannelClient.getInstance().getTextMessageDraft(this.mConversationIdentifier.getType(), this.mConversationIdentifier.getTargetId(), this.mConversationIdentifier.getChannelId(), new GetDraftCallback(weakThis));
    }

    public void onPause() {
        if (this.mEditText != null && this.mEditText.getText() != null && !this.mInitialDraft.equals(this.mEditText.getText().toString())) {
            this.saveTextMessageDraft(this.mEditText.getText().toString());
        }

    }

    public void onDestroy() {
//        RongExtensionCacheHelper.saveVoiceInputMode(this.mContext, this.mConversationIdentifier.getType(), this.mConversationIdentifier.getTargetId(), this.mIsVoiceInputMode);
        this.mFragment = null;
        this.mContext = null;
        this.mExtensionViewModel = null;
        if (this.mEditText != null && this.mEditText.getText() != null && !this.mInitialDraft.equals(this.mEditText.getText().toString())) {
            this.saveTextMessageDraft(this.mEditText.getText().toString());
        }

    }

    private void saveTextMessageDraft(final String draft) {
        IMCenter.getInstance().saveTextMessageDraft(this.mConversationIdentifier, draft, new RongIMClient.ResultCallback<Boolean>() {
            public void onSuccess(Boolean success) {
                if (success) {
                    MyInputPanel.this.mInitialDraft = draft;
                }

            }

            public void onError(RongIMClient.ErrorCode e) {
            }
        });
    }

    private void updateMessageDraft(final String draft) {
        if (!TextUtils.isEmpty(draft)) {
            this.mEditText.postDelayed(new Runnable() {
                public void run() {
                    MyInputPanel.this.mInitialDraft = draft;
                    if (MyInputPanel.this.mEditText instanceof RongEditText) {
                        ((RongEditText) MyInputPanel.this.mEditText).setText(draft, false);
                    } else {
                        MyInputPanel.this.mEditText.setText(draft);
                    }

                    MyInputPanel.this.mEditText.setSelection(MyInputPanel.this.mEditText.length());
                    MyInputPanel.this.mEditText.requestFocus();
                    MyInputPanel.this.resetInputView();
                }
            }, 50L);
        }
    }

    public static enum InputStyle {
        STYLE_SWITCH_CONTAINER_EXTENSION(291),
        STYLE_SWITCH_CONTAINER(288),
        STYLE_CONTAINER_EXTENSION(35),
        STYLE_CONTAINER(32);

        int v;

        private InputStyle(int v) {
            this.v = v;
        }

        public static InputStyle getStyle(int v) {
            InputStyle result = null;
            InputStyle[] var2 = values();
            int var3 = var2.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                InputStyle style = var2[var4];
                if (style.v == v) {
                    result = style;
                    break;
                }
            }

            return result;
        }
    }

    private static class GetDraftCallback extends IRongCoreCallback.ResultCallback<String> {
        private WeakReference<MyInputPanel> mWeakInputPanel;

        GetDraftCallback(WeakReference<MyInputPanel> weakInputPanel) {
            this.mWeakInputPanel = weakInputPanel;
        }

        public void onSuccess(String s) {
            if (this.mWeakInputPanel != null) {
                if (this.mWeakInputPanel.get() != null) {
                    this.mWeakInputPanel.get().updateMessageDraft(s);
                }

            }
        }

        public void onError(IRongCoreEnum.CoreErrorCode e) {
        }
    }
}
