package io.rong.imkit.conversation.extension;

import android.content.Context;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.BaseAdapter;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import io.rong.common.rlog.RLog;
import io.rong.imkit.R.drawable;
import io.rong.imkit.R.id;
import io.rong.imkit.R.layout;
import io.rong.imkit.conversation.ConversationFragment;
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule;
import io.rong.imlib.model.Conversation;

/**
 * Author:Lxf
 * Create on:2024/8/7
 * Description:
 */
public class MyPluginBoard {
    private final String TAG = this.getClass().getSimpleName();
    private ViewGroup mViewContainer;
    private final ViewGroup mRoot;
    private List<IPluginModule> mPluginModules = new ArrayList<>();
    private final Fragment mFragment;
    private final Conversation.ConversationType mConversationType;
    private final String mTargetId;
    private int mPluginCountPerPage;
    private int currentPage = 0;
    private ViewPager2 mViewPager;
    private View mCustomPager;
    private PluginPagerAdapter mPagerAdapter;
    private LinearLayout mIndicator;
    private static final int DEFAULT_SHOW_COLUMN = 2;
    private static final int DEFAULT_SHOW_ROW = 1;

    public MyPluginBoard(Fragment fragment, ViewGroup parent, Conversation.ConversationType type, String targetId) {
        this.mFragment = fragment;
        this.mRoot = parent;
        this.mConversationType = type;
        this.mTargetId = targetId;
        this.initView(fragment.getContext(), this.mRoot);
    }

    public ViewGroup getView() {
        if (this.mCustomPager != null) {
            this.mCustomPager.setVisibility(View.GONE);
        }

        return this.mViewContainer;
    }

    private void initView(Context context, ViewGroup viewGroup) {
        this.mViewContainer = (ViewGroup) LayoutInflater.from(context).inflate(layout.rc_ext_plugin_pager, viewGroup, false);
        this.mViewContainer.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            public void onViewAttachedToWindow(View v) {
                mRoot.post(new Runnable() {
                    public void run() {
                        ViewGroup.LayoutParams indicatorLayoutParams = mIndicator.getLayoutParams();
                        if (indicatorLayoutParams instanceof ViewGroup.MarginLayoutParams layoutParams) {
                            Pair<Integer, Integer> cellSize = calculateCellSize(mRoot, DEFAULT_SHOW_COLUMN, DEFAULT_SHOW_ROW, 0, mIndicator.getHeight() + layoutParams.topMargin + layoutParams.bottomMargin, 0, 0);

                            for (GridView gridView : mPagerAdapter.pageSet) {
                                ((PluginItemAdapter) gridView.getAdapter()).updateLayoutByCellSize(cellSize);
                            }
                        } else {
                            RLog.d(TAG, "mViewContainer LayoutParams is not MarginLayoutParams");
                        }

                    }
                });
            }

            public void onViewDetachedFromWindow(View v) {
            }
        });

        try {
            this.mPluginCountPerPage = context.getResources().getInteger(context.getResources().getIdentifier("rc_extension_plugin_count_per_page", "integer", context.getPackageName()));
        } catch (Exception var4) {
            this.mPluginCountPerPage = 8;
        }

        this.mViewPager = this.mViewContainer.findViewById(id.rc_view_pager);
        this.mIndicator = this.mViewContainer.findViewById(id.rc_indicator);
        this.mViewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            public void onPageSelected(int position) {
                onIndicatorChanged(currentPage, position);
                currentPage = position;
            }
        });
        this.initPlugins(this.mConversationType);
    }

    private Pair<Integer, Integer> calculateCellSize(ViewGroup parent, int numColumns, int numRows, int paddingTop, int paddingBottom, int paddingLeft, int paddingRight) {
        int parentHeight = parent.getHeight();
        int parentWidth = parent.getWidth();
        int cellWidth = (parentWidth - paddingLeft - paddingRight) / numColumns;
        int cellHeight = (parentHeight - paddingBottom - paddingTop) / numRows;
        return Pair.create(cellWidth, Math.min(cellHeight, (int)((double)cellWidth * 1.2)));
    }

    private void initIndicator(Context context, int pages, LinearLayout indicator) {
        for(int i = 0; i < pages; ++i) {
            ImageView imageView = (ImageView)LayoutInflater.from(context).inflate(layout.rc_ext_indicator, (ViewGroup)null);
            imageView.setImageResource(drawable.rc_ext_indicator);
            indicator.addView(imageView);
            if (pages == 1) {
                indicator.setVisibility(View.GONE);
            } else {
                indicator.setVisibility(View.VISIBLE);
            }
        }

    }

    private void initPlugins(Conversation.ConversationType conversationType) {
        if (this.mPluginModules == null || this.mPluginModules.isEmpty()) {
            this.mPluginModules = RongExtensionManager.getInstance().getExtensionConfig().getPluginModules(this.mConversationType, this.mTargetId);
            int pages = 0;
            int count = this.mPluginModules.size();
            if (count > 0) {
                int rem = count % this.mPluginCountPerPage;
                if (rem > 0) {
                    rem = 1;
                }

                pages = count / this.mPluginCountPerPage + rem;
            }

            mPagerAdapter = new PluginPagerAdapter(pages, count);
            mViewPager.setAdapter(mPagerAdapter);
            this.mViewPager.setOffscreenPageLimit(1);
            this.initIndicator(this.mFragment.getContext(), pages, this.mIndicator);
            this.onIndicatorChanged(-1, 0);
            this.mPagerAdapter.notifyDataSetChanged();
        }
    }

    public void removePlugin(IPluginModule pluginModule) {
        this.mPluginModules.remove(pluginModule);
        if (this.mPagerAdapter != null && this.mViewPager != null) {
            int count = this.mPluginModules.size();
            if (count > 0) {
                int rem = count % this.mPluginCountPerPage;
                if (rem > 0) {
                    rem = 1;
                }

                int pages = count / this.mPluginCountPerPage + rem;
                this.mPagerAdapter.setPages(pages);
                this.mPagerAdapter.setItems(count);
                this.mPagerAdapter.notifyDataSetChanged();
                this.removeIndicator(pages, this.mIndicator);
            }
        }

    }

    public void addPlugin(IPluginModule pluginModule) {
        if (pluginModule == null) {
            RLog.d(this.TAG, "addPlugin pluginModule is null");
        } else {
            this.mPluginModules.add(pluginModule);
            int count = this.mPluginModules.size();
            if (this.mPagerAdapter != null && count > 0 && this.mIndicator != null) {
                int rem = count % this.mPluginCountPerPage;
                if (rem > 0) {
                    rem = 1;
                }

                int pages = count / this.mPluginCountPerPage + rem;
                this.mPagerAdapter.setPages(pages);
                this.mPagerAdapter.setItems(count);
                this.mPagerAdapter.notifyDataSetChanged();
                this.mIndicator.removeAllViews();
                this.initIndicator(this.mFragment.getContext(), pages, this.mIndicator);
            }

        }
    }

    private void removeIndicator(int totalPages, LinearLayout indicator) {
        int index = indicator.getChildCount();
        if (index > totalPages && index - 1 >= 0) {
            indicator.removeViewAt(index - 1);
            this.onIndicatorChanged(index, index - 1);
            if (totalPages <= 1) {
                indicator.setVisibility(View.INVISIBLE);
            }
        }

    }

    private void onIndicatorChanged(int pre, int cur) {
        int count = this.mIndicator.getChildCount();
        if (count > 0 && pre < count && cur < count) {
            ImageView curView;
            if (pre >= 0) {
                curView = (ImageView)this.mIndicator.getChildAt(pre);
                curView.setImageResource(drawable.rc_ext_indicator);
            }

            if (cur >= 0) {
                curView = (ImageView)this.mIndicator.getChildAt(cur);
                curView.setImageResource(drawable.rc_ext_indicator_hover);
            }
        }

    }

    public void addPager(View v) {
        this.mCustomPager = v;
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(-1, -1);
        params.addRule(13, -1);
        this.mViewContainer.addView(v, params);
    }

    public View getPager() {
        return this.mCustomPager;
    }

    public void removePager(View view) {
        if (this.mCustomPager != null && this.mCustomPager == view) {
            this.mViewContainer.removeView(view);
            this.mCustomPager = null;
        }

    }

    public int getPluginPosition(IPluginModule pluginModule) {
        return this.mPluginModules.indexOf(pluginModule);
    }

    public IPluginModule getPluginModule(int position) {
        return position >= 0 && position < this.mPluginModules.size() ? (IPluginModule)this.mPluginModules.get(position) : null;
    }

    public List<IPluginModule> getPluginModules() {
        return this.mPluginModules;
    }

    private class PluginItemAdapter extends BaseAdapter {
        int count;
        int index;
        Pair<Integer, Integer> cellSize;

        public PluginItemAdapter(int index, int count) {
            this.count = Math.min(mPluginCountPerPage, count - index);
            this.index = index;
            this.cellSize = new Pair<>(-1, -1);
        }

        public int getCount() {
            return this.count;
        }

        public Object getItem(int position) {
            return null;
        }

        public long getItemId(int position) {
            return 0L;
        }

        public View getView(final int position, View convertView, ViewGroup parent) {
            Context context = parent.getContext();
            ViewHolder holder;
            if (convertView == null) {
                holder = new ViewHolder();
                convertView = LayoutInflater.from(parent.getContext()).inflate(layout.rc_ext_plugin_item, (ViewGroup)null);
                holder.imageView = (ImageView)convertView.findViewById(id.rc_ext_plugin_icon);
                holder.textView = (TextView)convertView.findViewById(id.rc_ext_plugin_title);
                convertView.setTag(holder);
            }

            AbsListView.LayoutParams layoutParams = (AbsListView.LayoutParams)convertView.getLayoutParams();
            if (layoutParams == null) {
                layoutParams = new AbsListView.LayoutParams((Integer)this.cellSize.first, (Integer)this.cellSize.second);
            } else {
                layoutParams.width = (Integer)this.cellSize.first;
                layoutParams.height = (Integer)this.cellSize.second;
            }

            convertView.setLayoutParams(layoutParams);
            convertView.setOnClickListener(new View.OnClickListener() {
                public void onClick(View v) {
                    IPluginModule plugin = (IPluginModule)mPluginModules.get(currentPage * mPluginCountPerPage + position);
                    if (mFragment instanceof ConversationFragment) {
                        plugin.onClick(mFragment, ((ConversationFragment)mFragment).getRongExtension(), currentPage * mPluginCountPerPage + position);
                    }

                }
            });
            holder = (ViewHolder)convertView.getTag();
            IPluginModule plugin = (IPluginModule)mPluginModules.get(position + this.index);
            if (plugin == null) {
                return convertView;
            } else {
                holder.imageView.setImageDrawable(plugin.obtainDrawable(context));
                holder.textView.setText(plugin.obtainTitle(context));
                return convertView;
            }
        }

        public void updateLayoutByCellSize(Pair<Integer, Integer> cellSize) {
            if (this.cellSize == null || !this.cellSize.equals(cellSize)) {
                this.cellSize = cellSize;
                this.notifyDataSetChanged();
            }

        }

        static class ViewHolder {
            ImageView imageView;
            TextView textView;

            ViewHolder() {
            }
        }
    }

    private static class PluginPagerViewHolder extends RecyclerView.ViewHolder {
        GridView gridView;

        public PluginPagerViewHolder(@NonNull View itemView) {
            super(itemView);
            this.gridView = (GridView)itemView;
        }
    }

    private class PluginPagerAdapter extends RecyclerView.Adapter<PluginPagerViewHolder> {
        int pages;
        int items;
        Set<GridView> pageSet;

        public PluginPagerAdapter(int pages, int items) {
            this.pages = pages;
            this.items = items;
            this.pageSet = new HashSet<>();
        }

        @NonNull
        public PluginPagerViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            GridView gridView = (GridView)LayoutInflater.from(parent.getContext()).inflate(layout.rc_ext_plugin_grid_view, parent, false);
            this.pageSet.add(gridView);
            return new PluginPagerViewHolder(gridView);
        }

        public void onBindViewHolder(@NonNull PluginPagerViewHolder holder, int position) {
            GridView gridView = holder.gridView;
            gridView.setNumColumns(4);
            gridView.setAdapter(new PluginItemAdapter(position * mPluginCountPerPage, this.items));
        }

        public int getItemCount() {
            return this.pages;
        }

        public void setPages(int value) {
            this.pages = value;
        }

        public void setItems(int value) {
            this.items = value;
        }
    }
}
