package io.rong.imkit.conversation.extension.provider

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.mobile.anchor.app.R
import com.mobile.anchor.app.extension.loadAnchorAvatar
import com.mobile.anchor.app.extension.loadAvatar
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import io.rong.imkit.conversation.extension.parsemessage.MikChatSupportHerMessage
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent

/**
 * Author:Lxf
 * Create on:2024/8/19
 * Description:
 */
class SupportHerMessageItemProvider : BaseMessageItemProvider<MikChatSupportHerMessage>() {
    override fun getSummarySpannable(context: Context, t: MikChatSupportHerMessage?): Spannable {
        return SpannableString(context.getString(R.string.title_message_support_me))
    }

    override fun onCreateMessageContentViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.rc_support_her_text_message_item, parent, false)
        return ViewHolder(parent.context, view)
    }

    override fun isMessageViewType(messageContent: MessageContent): Boolean {
        return messageContent is MikChatSupportHerMessage && !messageContent.isDestruct
    }

    override fun onItemClick(
        holder: ViewHolder?,
        t: MikChatSupportHerMessage?,
        uiMessage: UiMessage?,
        position: Int,
        list: MutableList<UiMessage>?,
        listener: IViewProviderListener<UiMessage>?
    ): Boolean {
        return false
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder,
        message: MikChatSupportHerMessage,
        uiMessage: UiMessage,
        position: Int,
        list: MutableList<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        holder.apply {
            val anchorUserInfo = RongUserInfoManager.getInstance().getUserInfo(message.user_id)
            anchorUserInfo?.portraitUri?.let {
                getView<ImageView>(R.id.iv_anchor_avatar).loadAnchorAvatar(it.toString())
            }
            DataStoreManager.getUserObject()?.avatar?.let {
                getView<ImageView>(R.id.iv_user_avatar).loadAvatar(it)
            }
            getView<TextView>(R.id.tv_support_her_content).text = message.anchor_content
            val isSender = uiMessage.message.messageDirection == Message.MessageDirection.SEND
            setBackgroundRes(R.id.CL, if (isSender) R.drawable.ic_gift_right_bg else R.drawable.ic_gift_left_bg)
        }
    }

    override fun showBubble(): Boolean {
        return false
    }
}