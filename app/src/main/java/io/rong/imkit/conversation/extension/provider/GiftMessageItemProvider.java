package io.rong.imkit.conversation.extension.provider;

import android.content.Context;
import android.text.Spannable;
import android.text.SpannableString;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.bumptech.glide.Glide;
import com.mobile.anchor.app.R;

import java.util.List;

import io.rong.imkit.conversation.extension.parsemessage.MikChatGiftMessage;
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider;
import io.rong.imkit.model.UiMessage;
import io.rong.imkit.widget.adapter.IViewProviderListener;
import io.rong.imkit.widget.adapter.ViewHolder;
import io.rong.imlib.model.Message;
import io.rong.imlib.model.MessageContent;

public class GiftMessageItemProvider extends BaseMessageItemProvider<MikChatGiftMessage> {

    public GiftMessageItemProvider() {
        mConfig.showReadState = true;
    }

    @Override
    protected ViewHolder onCreateMessageContentViewHolder(ViewGroup parent, int viewType) {
        View view =
                LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.rc_gift_text_message_item, parent, false);
        return new ViewHolder(parent.getContext(), view);
    }

    @Override
    protected void bindMessageContentViewHolder(
            final ViewHolder holder,
            ViewHolder parentHolder,
            MikChatGiftMessage message,
            final UiMessage uiMessage,
            int position,
            List<UiMessage> list,
            IViewProviderListener<UiMessage> listener) {
        final TextView nickName = holder.getView(R.id.nickName);
        final ImageView gift = holder.getView(R.id.gift);
        nickName.setText(message.showName);
//        if (message.giftDirection.equals("0")) {
//            nickName.setText("Sent");
//        }else {
//            nickName.setText("Receive");
//        }

        Glide.with(holder.getContext()).load(message.icon).into(gift);


        boolean isSender =
                uiMessage.getMessage().getMessageDirection().equals(Message.MessageDirection.SEND);
            holder.setBackgroundRes(R.id.CL, isSender ? R.drawable.rc_ic_bubble_right : R.drawable.rc_ic_bubble_left);
    }

    private void setDirection(View view, boolean isSender) {
        ConstraintLayout.LayoutParams lp = ((ConstraintLayout.LayoutParams) view.getLayoutParams());
        if (isSender) {
            lp.startToStart = ConstraintLayout.LayoutParams.UNSET;
            lp.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
        } else {
            lp.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
            lp.endToEnd = ConstraintLayout.LayoutParams.UNSET;
        }
        view.setLayoutParams(lp);
    }

    @Override
    protected boolean onItemClick(
            ViewHolder holder,
            MikChatGiftMessage message,
            UiMessage uiMessage,
            int position,
            List<UiMessage> list,
            IViewProviderListener<UiMessage> listener) {
        return false;
    }

    @Override
    protected boolean isMessageViewType(MessageContent messageContent) {
        return messageContent instanceof MikChatGiftMessage && !messageContent.isDestruct();
    }


    @Override
    public Spannable getSummarySpannable(Context context, MikChatGiftMessage message) {
        return new SpannableString("[Gift]");
    }
    @Override
    public boolean showBubble() {
        return false;
    }
}
