package io.rong.imkit.conversation.extension.provider;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.util.LayoutDirection;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.text.TextUtilsCompat;

import com.bdc.android.library.utils.ToastUtil;
import com.mobile.anchor.app.R;
import com.mobile.anchor.app.utils.Constants;

import java.util.List;
import java.util.Locale;
import java.util.Objects;

import io.rong.common.rlog.RLog;
import io.rong.imkit.conversation.extension.AndroidEmoji;
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider;
import io.rong.imkit.model.State;
import io.rong.imkit.model.UiMessage;
import io.rong.imkit.utils.TextViewUtils;
import io.rong.imkit.widget.adapter.IViewProviderListener;
import io.rong.imkit.widget.adapter.ViewHolder;
import io.rong.imlib.model.Message;
import io.rong.imlib.model.MessageContent;
import io.rong.message.TextMessage;

public class MyTextMessageItemProvider extends BaseMessageItemProvider<TextMessage> {
    public MyTextMessageItemProvider() {
        this.mConfig.showReadState = true;
    }

    protected ViewHolder onCreateMessageContentViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.rc_translate_text_message_item, parent, false);
        return new ViewHolder(parent.getContext(), view);
    }

    protected void bindMessageContentViewHolder(final ViewHolder holder, ViewHolder parentHolder, TextMessage message, final UiMessage uiMessage, int position, List<UiMessage> list, IViewProviderListener<UiMessage> listener) {
        final TextView textView = (TextView) holder.getView(R.id.rc_text);
        TextView translatedView = (TextView) holder.getView(R.id.rc_translated_text);
        ProgressBar progressBar = (ProgressBar) holder.getView(R.id.rc_pb_translating);
        //设置违规拦截提示
        final ImageView block = holder.getView(R.id.rc_block);
        RLog.e("MyTextMessageItemProvider", "block" + uiMessage.getExpansion());
        if (uiMessage.getExpansion() != null) {
            if (uiMessage.getExpansion().containsKey(Constants.MESSAGE_EXPEND_KEY_BLOCK) && Objects.equals(uiMessage.getExpansion().get(Constants.MESSAGE_EXPEND_KEY_BLOCK), "1")) {
                block.setVisibility(View.VISIBLE);
            } else {
                block.setVisibility(View.GONE);
            }
        }
        block.setOnClickListener(v -> ToastUtil.show(v.getContext().getString(R.string.tip_contains_forbidden_words)));

        if (!this.checkViewsValid(new View[]{textView, translatedView, progressBar})) {
            RLog.e("BaseMessageItemProvider", "checkViewsValid error," + uiMessage.getObjectName());
        } else {
            if (TextUtilsCompat.getLayoutDirectionFromLocale(Locale.getDefault()) == LayoutDirection.RTL) {
                textView.setTextAlignment(View.TEXT_ALIGNMENT_VIEW_END);
            }

            textView.setTag(uiMessage.getMessageId());
            if (uiMessage.getContentSpannable() == null) {
                SpannableStringBuilder spannable = TextViewUtils.getSpannable(message.getContent(), new TextViewUtils.RegularCallBack() {
                    public void finish(SpannableStringBuilder spannable) {
                        uiMessage.setContentSpannable(spannable);
                        textView.post(new Runnable() {
                            public void run() {
                                if (TextUtils.equals(textView.getTag() == null ? "" : textView.getTag().toString(), String.valueOf(uiMessage.getMessageId()))) {
                                    textView.setText(uiMessage.getContentSpannable());
                                }

                            }
                        });
                    }
                });
                uiMessage.setContentSpannable(spannable);
            }

            textView.setText(uiMessage.getContentSpannable());
//            textView.setMovementMethod(new LinkTextViewMovementMethod(new ILinkClickListener() {
//                public boolean onLinkClick(String link) {
//                    boolean result = false;
//                    if (RongConfigCenter.conversationConfig().getConversationClickListener() != null) {
//                        result = RongConfigCenter.conversationConfig().getConversationClickListener().onMessageLinkClick(holder.getContext(), link, uiMessage.getMessage());
//                    }
//
//                    if (result) {
//                        return true;
//                    } else {
//                        String str = link.toLowerCase();
//                        if (str.startsWith("http") || str.startsWith("https")) {
//                            RouteUtils.routeToWebActivity(textView.getContext(), link);
//                            result = true;
//                        }
//
//                        return result;
//                    }
//                }
//            }));
            textView.setOnClickListener(new View.OnClickListener() {
                public void onClick(View view) {
                    ViewParent parent = view.getParent();
                    if (parent instanceof View) {
                        ((View) parent).performClick();
                    }

                }
            });
            textView.setOnLongClickListener(new View.OnLongClickListener() {
                public boolean onLongClick(View view) {
                    ViewParent parent = view.getParent();
                    return parent instanceof View ? ((View) parent).performLongClick() : false;
                }
            });
            boolean isSender = uiMessage.getMessage().getMessageDirection().equals(Message.MessageDirection.SEND);
            if (this.mConfig.showContentBubble) {
                holder.setBackgroundRes(R.id.rc_text, isSender ? R.drawable.rc_ic_bubble_right : R.drawable.rc_ic_bubble_left);
            }

            if (uiMessage.getTranslateStatus() == State.SUCCESS && !TextUtils.isEmpty(uiMessage.getTranslatedContent())) {
                translatedView.setVisibility(View.VISIBLE);
                progressBar.setVisibility(View.GONE);
                translatedView.setText(uiMessage.getTranslatedContent());
                holder.setBackgroundRes(R.id.rc_translated_text, isSender ? R.drawable.rc_ic_translation_bubble_right : R.drawable.rc_ic_translation_bubble_left);
            } else if (uiMessage.getTranslateStatus() == State.PROGRESS) {
                translatedView.setVisibility(View.GONE);
                progressBar.setVisibility(View.VISIBLE);
                translatedView.setVisibility(View.GONE);
                holder.setBackgroundRes(R.id.rc_pb_translating, isSender ? R.drawable.rc_ic_translation_bubble_right : R.drawable.rc_ic_translation_bubble_left);
            } else {
                translatedView.setText((CharSequence) null);
                translatedView.setVisibility(View.GONE);
                progressBar.setVisibility(View.GONE);
                translatedView.setBackground((Drawable) null);
            }

            this.setDirection(textView, isSender);
            this.setDirection(translatedView, isSender);
            this.setDirection(progressBar, isSender);
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                public void onClick(View v) {
                    ViewParent parent = v.getParent();
                    if (parent instanceof View) {
                        ((View) parent).performClick();
                    }

                }
            });
            holder.itemView.setOnLongClickListener(new View.OnLongClickListener() {
                public boolean onLongClick(View v) {
                    ViewParent parent = v.getParent();
                    return parent instanceof View ? ((View) parent).performLongClick() : false;
                }
            });
        }
    }

    private void setDirection(View view, boolean isSender) {
        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) view.getLayoutParams();
        if (isSender) {
            lp.startToStart = -1;
            lp.endToEnd = 0;
        } else {
            lp.startToStart = 0;
            lp.endToEnd = -1;
        }

        view.setLayoutParams(lp);
    }

    protected boolean onItemClick(ViewHolder holder, TextMessage message, UiMessage uiMessage, int position, List<UiMessage> list, IViewProviderListener<UiMessage> listener) {
        return false;
    }

    protected boolean isMessageViewType(MessageContent messageContent) {
        return messageContent instanceof TextMessage && !messageContent.isDestruct();
    }

    public Spannable getSummarySpannable(Context context, TextMessage message) {
        if (message != null && !TextUtils.isEmpty(message.getContent())) {
            String content = message.getContent();
            content = content.replace("\n", " ");
            if (content.length() > 100) {
                content = content.substring(0, 100);
            }

            return new SpannableString(AndroidEmoji.ensure(content));
        } else {
            return new SpannableString("");
        }
    }

    public boolean showBubble() {
        return false;
    }
}
