package com.mobile.anchor.app.config

/**
 * 应用配置类
 * 统一管理应用的配置常量
 */
object AppConfig {
    
    // 应用信息
    const val APP_NAME = "Anchor"
    const val VERSION_NAME = "1.0.0"
    const val VERSION_CODE = 1
    
    // 网络配置
    const val BASE_URL = "https://api.example.com/"
    const val CONNECT_TIMEOUT = 30L // 秒
    const val READ_TIMEOUT = 30L // 秒
    const val WRITE_TIMEOUT = 30L // 秒
    
    // 缓存配置
    const val CACHE_SIZE = 10 * 1024 * 1024L // 10MB
    const val CACHE_MAX_AGE = 60 * 60 * 24 // 1天
    const val CACHE_MAX_STALE = 60 * 60 * 24 * 7 // 7天
    
    // 图片加载配置
    const val IMAGE_CACHE_SIZE = 50 * 1024 * 1024L // 50MB
    const val IMAGE_PLACEHOLDER_COLOR = 0xFFE0E0E0
    const val IMAGE_ERROR_COLOR = 0xFFFFCDD2
    
    // 分页配置
    const val DEFAULT_PAGE_SIZE = 20
    const val MAX_PAGE_SIZE = 100
    
    // 数据库配置
    const val DATABASE_NAME = "anchor_database"
    const val DATABASE_VERSION = 1
    
    // SharedPreferences 配置
    const val PREF_NAME = "anchor_preferences"
    
    // 日志配置
    const val LOG_TAG = "Anchor"
    const val ENABLE_LOG = true
    
    // 主题配置
    const val DEFAULT_THEME = "system" // system, light, dark
    
    // 语言配置
    const val DEFAULT_LANGUAGE = "zh" // zh, en
    
    // 文件路径配置
    const val CACHE_DIR_NAME = "cache"
    const val IMAGE_CACHE_DIR_NAME = "images"
    const val LOG_DIR_NAME = "logs"
    
    // 网络状态检查间隔
    const val NETWORK_CHECK_INTERVAL = 5000L // 5秒
    
    // 重试配置
    const val MAX_RETRY_COUNT = 3
    const val RETRY_DELAY = 1000L // 1秒
    
    // 动画配置
    const val ANIMATION_DURATION_SHORT = 200L
    const val ANIMATION_DURATION_MEDIUM = 300L
    const val ANIMATION_DURATION_LONG = 500L
    
    // 触摸反馈配置
    const val TOUCH_FEEDBACK_ENABLED = true
    
    // 调试配置
    const val DEBUG_MODE = true // BuildConfig.DEBUG
    const val ENABLE_STRICT_MODE = DEBUG_MODE
    const val ENABLE_LEAK_CANARY = DEBUG_MODE
    
    // API 配置
    object Api {
        const val TIMEOUT_CONNECT = 30
        const val TIMEOUT_READ = 30
        const val TIMEOUT_WRITE = 30
        
        // API 版本
        const val API_VERSION = "v1"
        
        // 请求头
        const val HEADER_CONTENT_TYPE = "application/json"
        const val HEADER_ACCEPT = "application/json"
        const val HEADER_USER_AGENT = "$APP_NAME/$VERSION_NAME"
        
        // 状态码
        const val HTTP_OK = 200
        const val HTTP_CREATED = 201
        const val HTTP_NO_CONTENT = 204
        const val HTTP_BAD_REQUEST = 400
        const val HTTP_UNAUTHORIZED = 401
        const val HTTP_FORBIDDEN = 403
        const val HTTP_NOT_FOUND = 404
        const val HTTP_INTERNAL_SERVER_ERROR = 500
    }
    
    // 缓存策略
    object Cache {
        const val CACHE_CONTROL_MAX_AGE = "max-age=$CACHE_MAX_AGE"
        const val CACHE_CONTROL_MAX_STALE = "max-stale=$CACHE_MAX_STALE"
        const val CACHE_CONTROL_NO_CACHE = "no-cache"
        const val CACHE_CONTROL_NO_STORE = "no-store"
    }
    
    // 错误码
    object ErrorCode {
        const val NETWORK_ERROR = 1001
        const val PARSE_ERROR = 1002
        const val UNKNOWN_ERROR = 1003
        const val TIMEOUT_ERROR = 1004
        const val SERVER_ERROR = 1005
    }
    
    // 事件类型
    object EventType {
        const val USER_LOGIN = "user_login"
        const val USER_LOGOUT = "user_logout"
        const val PAGE_VIEW = "page_view"
        const val BUTTON_CLICK = "button_click"
        const val API_CALL = "api_call"
    }
}
