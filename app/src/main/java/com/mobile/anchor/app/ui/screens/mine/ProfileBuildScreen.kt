package com.mobile.anchor.app.ui.screens.mine

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bdc.android.library.extension.jump
import com.bdc.android.library.utils.ToastUtil
import com.mobile.anchor.app.MainActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.network.ApiResult
import com.mobile.anchor.app.extension.buildImageUrl
import com.mobile.anchor.app.extension.formatDate
import com.mobile.anchor.app.ui.components.AnchorButton
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTextField
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.AsyncImageComponent
import com.mobile.anchor.app.ui.components.BottomSelectDialog
import com.mobile.anchor.app.ui.components.DatePickerDialog
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.theme.Surface
import com.mobile.anchor.app.ui.viewmodels.UserViewModel
import com.mobile.anchor.app.utils.rememberAvatarPickerState
import com.mobile.anchor.app.utils.rememberImagePickerConfig

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/7 15:43
 * @description : 完善资料页面
 */
@Composable
fun ProfileBuildScreen(
) {
    BuildProfileContent()
}

@Composable
private fun BuildProfileContent(
    viewModel: UserViewModel = viewModel()
) {
    val context = LocalContext.current

    // 表单状态
    var nickname by remember { mutableStateOf("") }
    // 默认生日为当前日期往前推18年
    var birthday by remember {
        mutableStateOf(
            java.util.Calendar.getInstance().apply {
                add(java.util.Calendar.YEAR, -18)
            }.timeInMillis
        )
    }
    var agencyCode by remember { mutableStateOf("") }
    var avatarUrl by remember { mutableStateOf<String?>(null) }

    // 头像上传状态
    var isUploadingAvatar by remember { mutableStateOf(false) }
    var avatarUploadError by remember { mutableStateOf<String?>(null) }

    // 观察ViewModel状态
    val updateProfileState by viewModel.updateProfileState.observeAsState()

    // 网络请求状态
    val isLoading = updateProfileState is ApiResult.Loading
    val errorMessage = (updateProfileState as? ApiResult.Error)?.message

    // 监听更新成功状态
    LaunchedEffect(updateProfileState) {
        if (updateProfileState is ApiResult.Success) {
            context.jump(MainActivity::class.java)

            // 清除状态
            viewModel.clearUpdateProfileState()
        }
    }

    // 弹窗状态
    var showAvatarDialog by remember { mutableStateOf(false) }
    var showDatePickerDialog by remember { mutableStateOf(false) }

    AnchorScaffold(
        topBar = {
            AnchorTopBar(
                title = context.getString(R.string.edit_profile)
            )
        }) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(24.dp))

            // 头像上传区域
            AvatarUploadSection(
                avatarUrl = avatarUrl, isUploading = isUploadingAvatar, onAvatarClick = {
                    if (!isUploadingAvatar) {
                        showAvatarDialog = true
                    }
                })

            // 头像上传错误信息
            avatarUploadError?.let { error ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = error,
                    color = Color.Red,
                    fontSize = 12.sp,
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            // 昵称输入
            ProfileTextField(
                value = nickname,
                onValueChange = { nickname = it },
                label = stringResource(R.string.nickname),
                placeholder = stringResource(R.string.enter_your_nickname)
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 生日选择
            ProfileClickableField(
                value = birthday.formatDate(),
                label = stringResource(R.string.date_of_birth),
                placeholder = stringResource(R.string.select_your_birthday),
                onClick = {
                    showDatePickerDialog = true
                })

            Spacer(modifier = Modifier.height(16.dp))

            //相册
            AlbumSection()

            // 公会代码
            ProfileTextField(
                value = agencyCode,
                onValueChange = { agencyCode = it },
                label = stringResource(R.string.agency_code),
                placeholder = stringResource(R.string.enter_your_agency_code)
            )

            Spacer(modifier = Modifier.weight(1F))

            // 保存按钮
            AnchorButton(
                text = stringResource(R.string.next),
                onClick = {
                    viewModel.checkInfo(nickname, agencyCode) {
                        if (it.list.all { it.pass }) {
                            viewModel.updateProfile(
                                avatar = avatarUrl ?: "",
                                birthdayAt = birthday,
                                nickname = nickname,
                                inviteCode = agencyCode,
                                updateType = 0, // 提交资料审核 更新基本信息
                                onSuccess = {
                                    // 资料更新成功，调用完成回调跳转到首页
//                                    onProfileCompleted?.invoke()
                                    context.jump(MainActivity::class.java)
                                })
                        } else {
                            ToastUtil.show(it.list.find { it.pass.not() }?.reason)
                        }
                    }
                    // 调用ViewModel的更新资料方法，传递成功回调
                },
                enabled = nickname.isNotBlank() && agencyCode.isNotBlank() && avatarUrl != null,
                isLoading = isLoading
            )

            // 错误信息显示
            errorMessage?.let { message ->
                Spacer(
                    modifier = Modifier
                        .height(8.dp)
                        .align(Alignment.CenterHorizontally)
                )
                Text(
                    text = message,
                    color = Color.Red,
                    fontSize = 14.sp,
                )
            }

            Spacer(modifier = Modifier.height(24.dp))
        }
    }

    // 头像选择器状态
    val avatarPickerState = rememberAvatarPickerState(
        onAvatarSelected = { objectKey ->
            viewModel.checkImageStatus(objectKey.buildImageUrl()) {
                if (it.pass) {
                    // 上传成功
                    avatarUrl = objectKey
                } else {
                    // 上传失败
                    avatarUploadError = it.reason
                }
            }
            showAvatarDialog = false
        }, onUploadStart = {
            showAvatarDialog = false
            isUploadingAvatar = true
            avatarUploadError = null
        }, onUploadEnd = {
            isUploadingAvatar = false
        }, onError = { error ->
            avatarUploadError = error
            showAvatarDialog = false
        }, config = rememberImagePickerConfig(
            showCamera = true, showGallery = true, showGooglePhotos = false
        )
    )

    // 图片选择弹窗
    BottomSelectDialog(
        visible = showAvatarDialog,
        title = stringResource(R.string.select_avatar),
        options = avatarPickerState.getOptions(),
        onDismiss = { showAvatarDialog = false },
        onOptionSelected = { index, _ ->
            avatarPickerState.handleOptionSelected(index)
        })

    // 日期选择弹窗
    DatePickerDialog(
        visible = showDatePickerDialog,
        title = stringResource(R.string.select_birthday),
        initialDate = java.util.Calendar.getInstance().apply {
            timeInMillis = birthday
        },
        onDismiss = { showDatePickerDialog = false },
        onDateSelected = { calendar ->
            birthday = calendar.time.time
            showDatePickerDialog = false
        })
}

/**
 * 头像上传区域
 */
@Composable
private fun AvatarUploadSection(
    avatarUrl: String?, isUploading: Boolean = false, onAvatarClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(100.dp)
            .clip(RoundedCornerShape(50.dp))
            .background(Surface)
            .clickable { onAvatarClick() }, contentAlignment = Alignment.Center
    ) {
        if (avatarUrl != null) {
            AsyncImageComponent(
                imageUrl = avatarUrl.buildImageUrl(),
                modifier = Modifier.fillMaxSize(),
                shape = RoundedCornerShape(50.dp)
            )
        } else {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (isUploading) {
                    CircularProgressIndicator(
                        color = Primary, modifier = Modifier.size(32.dp)
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = stringResource(R.string.uploading),
                        color = Color.White,
                        fontSize = 12.sp
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = stringResource(R.string.add_avatar),
                        tint = Color.White,
                        modifier = Modifier.size(32.dp)
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = stringResource(R.string.add_photo),
                        color = Color.White,
                        fontSize = 12.sp
                    )
                }
            }
        }
    }
}

@Composable
private fun AlbumSection(modifier: Modifier = Modifier) {
    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "label",
            color = Color(0xFF9FA1A6),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )


    }
}

/**
 * 资料输入框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ProfileTextField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    placeholder: String,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = label,
            color = Color(0xFF9FA1A6),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        AnchorTextField(value, placeholder, onValueChange = onValueChange)
    }
}

/**
 * 可点击的资料字段（用于日期选择等）
 */
@Composable
private fun ProfileClickableField(
    value: String,
    label: String,
    placeholder: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = label,
            color = Color(0xFF9FA1A6),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .clip(RoundedCornerShape(50.dp))
                .background(Color(0xFF1A1D2E))
                .clickable { onClick() }
                .padding(horizontal = 16.dp),
            contentAlignment = Alignment.CenterStart) {
            Text(
                text = if (value.isNotEmpty()) value else placeholder,
                color = if (value.isNotEmpty()) Color.White else Color(0xFF999999),
                fontSize = 16.sp
            )
        }
    }
}

@Preview
@Composable
fun BuildProfileScreenPreview() {
    AnchorTheme {
        BuildProfileContent()
    }
}
