package com.mobile.anchor.app.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.mobile.anchor.app.R
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.DisabledColor
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.theme.Surface
import com.mobile.anchor.app.update.DownloadState

/**
 * 更新进度弹框组件
 * 显示下载进度和状态
 */
@Composable
fun UpdateProgressDialog(
    visible: Boolean,
    downloadState: DownloadState,
    dismissOnBackPress: Boolean = false,
    dismissOnClickOutside: Boolean = false,
    autoDismiss: Boolean = false,
    onClose: () -> Unit = {},
    onCancel: () -> Unit = {},
    onRetry: () -> Unit = {},
    onInstall: () -> Unit = {},
    onDismiss: () -> Unit = {}
) {
    AnimatedVisibility(visible, enter = fadeIn(), exit = fadeOut()) {
        Box {
            // 背景遮罩
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable { onDismiss() })
            Dialog(
                onDismissRequest = {
                    // 只有在下载完成或出错时才能关闭
//                    when (downloadState) {
//                        is DownloadState.Downloaded, is DownloadState.DownloadError, is DownloadState.DownloadCancelled -> onDismiss()
//
//                        else -> {}
//                    }
                }, properties = DialogProperties(
                    dismissOnBackPress = dismissOnBackPress,
                    dismissOnClickOutside = dismissOnClickOutside,
                    usePlatformDefaultWidth = false,
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
                ) {
                    // 弹框内容
                    Card(
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                            .padding(horizontal = 16.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Surface)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 24.dp, start = 24.dp, end = 24.dp, bottom = 10.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            when (downloadState) {
                                is DownloadState.Preparing -> {
                                    // 准备下载
                                    CircularProgressIndicator(
                                        color = Primary,
                                        modifier = Modifier.size(40.dp),
                                        strokeWidth = 3.dp
                                    )

                                    Spacer(modifier = Modifier.height(16.dp))

                                    Text(
                                        text = stringResource(R.string.download_preparing),
                                        color = Color.White,
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium
                                    )

                                    Text(
                                        text = stringResource(R.string.download_preparing_message),
                                        color = Color.White.copy(alpha = 0.7f),
                                        fontSize = 14.sp,
                                        textAlign = TextAlign.Center,
                                        modifier = Modifier.padding(top = 8.dp)
                                    )
                                    Spacer(modifier = Modifier.height(10.dp))
                                }

                                is DownloadState.Downloading -> {
                                    // 下载中
                                    Text(
                                        text = stringResource(R.string.download_in_progress),
                                        color = Color.White,
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium
                                    )

                                    Spacer(modifier = Modifier.height(16.dp))

                                    // 进度条
                                    LinearProgressIndicator(
                                        progress = downloadState.progress / 100f,
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .height(8.dp),
                                        color = Primary,
                                        trackColor = Color.White.copy(alpha = 0.2f)
                                    )

                                    Spacer(modifier = Modifier.height(12.dp))

                                    // 进度信息
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween
                                    ) {
                                        Text(
                                            text = "${downloadState.progress}%",
                                            color = Color.White,
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight.Medium
                                        )

                                        Text(
                                            text = formatBytes(downloadState.downloadedBytes) + "/" + formatBytes(
                                                downloadState.totalBytes
                                            ),
                                            color = Color.White.copy(alpha = 0.7f),
                                            fontSize = 14.sp
                                        )
                                    }

                                    if (downloadState.speed.isNotEmpty()) {
                                        Spacer(modifier = Modifier.height(8.dp))
                                        Text(
                                            text = stringResource(
                                                R.string.download_speed, downloadState.speed
                                            ),
                                            color = Color.White.copy(alpha = 0.7f),
                                            fontSize = 12.sp,
                                            textAlign = TextAlign.Center,
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    }

                                    Spacer(modifier = Modifier.height(16.dp))

                                    if (dismissOnBackPress || dismissOnClickOutside) {
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.End
                                        ) {
                                            // 关闭按钮
                                            TextButton(onClick = onClose) {
                                                Text(
                                                    text = stringResource(R.string.close),
                                                    fontSize = 14.sp,
                                                    fontWeight = FontWeight.Medium
                                                )
                                            }

                                            // 取消按钮
                                            TextButton(onClick = onCancel) {
                                                Text(
                                                    text = stringResource(R.string.cancel),
                                                    fontSize = 14.sp,
                                                    fontWeight = FontWeight.Medium
                                                )
                                            }
                                        }
                                    }
                                }

                                is DownloadState.Downloaded -> {
                                    // 下载完成
                                    Text(
                                        text = stringResource(R.string.download_completed),
                                        color = Color.White,
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium
                                    )

                                    Spacer(modifier = Modifier.height(16.dp))

                                    Text(
                                        text = stringResource(R.string.download_completed_message),
                                        color = Color.White.copy(alpha = 0.8f),
                                        fontSize = 14.sp,
                                        textAlign = TextAlign.Center,
                                        lineHeight = 20.sp
                                    )

                                    Spacer(modifier = Modifier.height(16.dp))

                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.End
                                    ) {
                                        if (dismissOnBackPress || dismissOnClickOutside) {
                                            TextButton(onClick = onClose) {
                                                Text(
                                                    text = stringResource(R.string.cancel),
                                                    color = DisabledColor,
                                                    fontSize = 14.sp,
                                                    fontWeight = FontWeight.Medium
                                                )
                                            }
                                        }

                                        TextButton(onClick = onInstall) {
                                            Text(
                                                text = stringResource(R.string.ok),
                                                color = Primary,
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }
                                    }
                                }

                                is DownloadState.DownloadError -> {
                                    // 下载失败
                                    Text(
                                        text = stringResource(R.string.download_failed),
                                        color = Color.White,
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium
                                    )

                                    Spacer(modifier = Modifier.height(16.dp))

                                    Text(
                                        text = downloadState.error,
                                        color = Color.White.copy(alpha = 0.8f),
                                        fontSize = 14.sp,
                                        textAlign = TextAlign.Center,
                                        lineHeight = 20.sp
                                    )

                                    Spacer(modifier = Modifier.height(16.dp))

                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.End
                                    ) {
                                        TextButton(onClick = onDismiss) {
                                            Text(
                                                text = stringResource(R.string.cancel),
                                                fontSize = 14.sp,
                                                color = DisabledColor,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }

                                        Spacer(modifier = Modifier.width(8.dp))

                                        TextButton(onClick = onRetry) {
                                            Text(
                                                text = stringResource(R.string.download_retry),
                                                color = Primary,
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }
                                    }
                                }

                                is DownloadState.DownloadCancelled -> {
                                    // 下载已取消
                                    Text(
                                        text = stringResource(R.string.download_cancelled),
                                        color = Color.White,
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium
                                    )

                                    Spacer(modifier = Modifier.height(16.dp))

                                    Text(
                                        text = stringResource(R.string.download_cancelled_message),
                                        color = Color.White.copy(alpha = 0.8f),
                                        fontSize = 14.sp,
                                        textAlign = TextAlign.Center,
                                        lineHeight = 20.sp
                                    )

                                    Spacer(modifier = Modifier.height(16.dp))

                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.End
                                    ) {
                                        TextButton(onClick = onDismiss) {
                                            Text(
                                                text = stringResource(R.string.ok),
                                                color = Primary,
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }
                                    }
                                }

                                else -> {
                                    // 默认状态
                                    CircularProgressIndicator(
                                        color = Primary,
                                        modifier = Modifier.size(40.dp),
                                        strokeWidth = 3.dp
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

private fun formatBytes(bytes: Long): String {
    return when {
        bytes < 1024 -> "${bytes}B"
        bytes < 1024 * 1024 -> "${bytes / 1024}KB"
        else -> String.format("%.1fMB", bytes / (1024.0 * 1024.0))
    }
}

@Preview
@Composable
fun UpdateProgressDialogDownloadingPreview() {
    AnchorTheme {
        UpdateProgressDialog(
            visible = true, downloadState = DownloadState.Downloading(
            progress = 45,
            downloadedBytes = 11 * 1024 * 1024,
            totalBytes = 25 * 1024 * 1024,
            speed = "1.2MB/s"
        ), onCancel = {}, onRetry = {}, onDismiss = {})
    }
}

@Preview
@Composable
fun UpdateProgressDialogCompletePreview() {
    AnchorTheme {
        UpdateProgressDialog(
            visible = true,
            downloadState = DownloadState.Downloaded("/path/to/file.apk"),
            onCancel = {},
            onRetry = {},
            onDismiss = {})
    }
}
