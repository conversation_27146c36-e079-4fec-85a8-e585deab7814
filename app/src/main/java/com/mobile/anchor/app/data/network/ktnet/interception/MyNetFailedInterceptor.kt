package com.mobile.anchor.app.data.network.ktnet.interception

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.ktnet.interception.NetWorkFailedInterceptor
import com.bdc.android.library.utils.ActivityManager
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.network.ktnet.error.code
import com.mobile.anchor.app.extension.formatDateTime
import com.mobile.anchor.app.ui.activities.LoginActivity
import com.mobile.anchor.app.utils.Constants
import com.mobile.anchor.app.utils.ContextHolder
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 *
 * @Author： Lxf
 * @Time： 2022/6/1 2:25 下午
 * @description：网络错误拦截器 返回false表示有自己的处理 不弹toast提示 默认返回true表示使用默认的异常处理
 */
class MyNetFailedInterceptor : NetWorkFailedInterceptor {
    override fun intercept(throwable: Throwable): Boolean {
        when (throwable.code) {
            Constants.ErrorCode.USER_NOT_EXIST, Constants.ErrorCode.TOKEN_EXPIRED, Constants.ErrorCode.USER_BLOCK -> {
                ActivityManager.current?.let {
                    it as AppCompatActivity
                }?.let {
                    if (it is LoginActivity) {
                        return true
                    } else {
                        it.lifecycleScope.launch {
                            val msg = if (throwable.code == Constants.ErrorCode.USER_BLOCK) {
                                JSONObject(throwable.message?:"{}").let {
                                    it.optString("reason").plus("\n").plus(ContextHolder.context?.getString(R.string.unblocking_time))
                                        .plus(it.optLong("end_at").formatDateTime())
                                }
                            } else {
                                null
                            }

                            FlowBus.with<String?>(Constants.TOKEN_EXPIRED)
                                .post(msg)
                        }
                    }
                    return false
                }
            }

//            HTTP_BAD_REQUEST, HTTP_INTERNAL_ERROR -> {
////                ActivityManager.current?.let {
////                    it as? AppCompatActivity
////                }?.let {
////                    it.runOnUiThread {
////                        ToastUtil.show(throwable.message)
////                    }
////                }
//                return false
//            }
        }
        return true
    }
}