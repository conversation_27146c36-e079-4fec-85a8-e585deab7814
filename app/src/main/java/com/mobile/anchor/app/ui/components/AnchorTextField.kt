package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.mobile.anchor.app.ui.theme.Primary

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/7 16:00
 * @description :
 */
@Composable
fun AnchorTextField(
    value: String,
    placeholder: String = "",
    containerColor: Color = Color(0xFF1A1D2E),
    placeholderColor: Color = Color(0xFF999999),
    readOnly: Boolean = false,
    keyboardType: KeyboardType = KeyboardType.Text,
    modifier: Modifier = Modifier,
    focusRequester: FocusRequester? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    onValueChange: (String) -> Unit
) {
    OutlinedTextField(
        value = value,
        onValueChange = onValueChange,
        placeholder = {
            Text(
                text = placeholder, color = placeholderColor
            )
        },
        singleLine = true,
        readOnly = readOnly,
        keyboardOptions = KeyboardOptions(keyboardType = keyboardType),
        colors = OutlinedTextFieldDefaults.colors(
            focusedTextColor = Color.White,
            unfocusedTextColor = Color.White,
            focusedBorderColor = containerColor,
            unfocusedBorderColor = containerColor,
            focusedContainerColor = containerColor,
            unfocusedContainerColor = containerColor,
            cursorColor = Primary
        ),
        trailingIcon = trailingIcon,
        shape = RoundedCornerShape(56.dp),
        modifier = modifier.fillMaxWidth().height(56.dp).let { modifier ->
            if (focusRequester != null) {
                modifier.focusRequester(focusRequester)
            } else {
                modifier
            }
        })
}

@Preview
@Composable
fun AnchorTextFieldPreview() {
    AnchorTextField(value = "", placeholder = "请输入...", onValueChange = { })
}
