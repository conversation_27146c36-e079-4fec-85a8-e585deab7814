package com.mobile.anchor.app.ui.viewmodels

import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.BodyParams
import com.mobile.anchor.app.data.service.UserApiService

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/6/23 17:11
 * @description :
 */
class LevelViewModel : BaseViewModel() {

    val userApiService: UserApiService by NetDelegates()

    fun updateCallPrice(price: Int) {
        ktHttpRequest {
            userApiService.setupCallPrice(BodyParams.setupCallPriceBody(price * 100)).await()
        }
    }
}