package com.mobile.anchor.app.ui.view

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Color
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.LinearInterpolator
import android.widget.LinearLayout
import android.widget.TextView
import com.mobile.anchor.app.R
import java.util.Timer
import java.util.TimerTask
import kotlin.text.toInt
import kotlin.times

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/4/28 16:55
 * @description : 上下滚动的跑马灯View，支持文本换行，每2秒滚动一次
 */
class MarqueeView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    // 滚动间隔时间，默认5秒
    private var scrollInterval = 5000L

    // 动画持续时间
    private var animDuration = 500L

    // 当前显示的位置
    private var currentIndex = 0

    // 数据列表
    private var dataList: List<String> = emptyList()

    // 当前显示的View
    private var currentView: TextView? = null

    // 下一个要显示的View
    private var nextView: TextView? = null

    // 定时器
    private var timer: Timer? = null

    // 定时任务
    private var timerTask: TimerTask? = null

    // 是否正在执行动画
    private var isAnimating = false

    // 是否自动滚动
    private var isAutoScroll = true

    // 文本颜色
    private var textColor = Color.BLACK

    // 文本大小
    private var textSize = context.resources.getDimension(R.dimen.sp_14)

    // 最大行数
    private var maxLines = 5

    // 原始设置的最大行数（用于限制动态计算的行数）
    private var originalMaxLines = 5

    // 文本对齐方式
    private var textGravity = Gravity.CENTER_VERTICAL or Gravity.START

    // 动态高度相关属性
    private var currentTextHeight = 0
    private var targetTextHeight = 0
    private var heightAnimator: ValueAnimator? = null
    private var isHeightAnimating = false

    // 存储每个文本的计算高度
    private val textHeightCache = mutableMapOf<String, Int>()

    // 高度变化动画持续时间
    private var heightAnimDuration = 100L

    init {
        // 设置垂直方向
        orientation = VERTICAL
        gravity = Gravity.TOP or Gravity.START  // 改为顶部对齐，避免居中导致的空白

        // 获取自定义属性
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.MarqueeView)

        // 获取文本颜色
        textColor = typedArray.getColor(
            R.styleable.MarqueeView_marqueeTextColor,
            Color.WHITE
        )

        // 获取文本大小
        textSize = typedArray.getDimension(
            R.styleable.MarqueeView_marqueeTextSize,
            TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP, 14f, resources.displayMetrics)
        )

        // 获取滚动间隔时间
        scrollInterval = typedArray.getInteger(
            R.styleable.MarqueeView_marqueeInterval,
            5000
        ).toLong()

        // 获取动画持续时间
        animDuration = typedArray.getInteger(
            R.styleable.MarqueeView_marqueeDuration,
            500
        ).toLong()

        // 获取最大行数
        maxLines = typedArray.getInteger(
            R.styleable.MarqueeView_marqueeMaxLines,
            5  // 改为默认5行，与使用时保持一致
        )
        // 保存原始的最大行数设置
        originalMaxLines = maxLines

        // 获取是否自动滚动
        isAutoScroll = typedArray.getBoolean(
            R.styleable.MarqueeView_marqueeAutoScroll,
            true
        )

        // 获取文本对齐方式
        textGravity = typedArray.getInt(
            R.styleable.MarqueeView_marqueeTextGravity,
            Gravity.CENTER_VERTICAL or Gravity.START
        )

        // 回收TypedArray
        typedArray.recycle()

        // 检查并调整文本大小
        checkAndAdjustTextSize()

        // 初始化两个TextView用于切换显示
        currentView = createTextView()
        nextView = createTextView()

        // 添加到布局中
        addView(currentView)
        addView(nextView)

        // 初始状态下，nextView不可见
        nextView?.visibility = INVISIBLE
    }

    /**
     * 创建TextView
     */
//    private fun createTextView(): TextView {
//        return TextView(context).apply {
//            // 使用MATCH_PARENT确保TextView填充整个MarqueeView的宽高
//            layoutParams = LayoutParams(
//                ViewGroup.LayoutParams.MATCH_PARENT,
//                ViewGroup.LayoutParams.MATCH_PARENT
//            )
//            // 设置文本对齐方式
//            gravity = textGravity
//            // 支持多行显示
//            isSingleLine = false
//            // 设置最大行数
//            maxLines = <EMAIL>
//            // 设置文本颜色
//            setTextColor(textColor)
//            // 设置文本大小
//            setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
//            // 设置内边距，避免文本贴边
//            val paddingDp = TypedValue.applyDimension(
//                TypedValue.COMPLEX_UNIT_DIP,
//                16f,
//                resources.displayMetrics
//            ).toInt()
//            setPadding(paddingDp, paddingDp / 2, paddingDp, paddingDp / 2)
//        }
//    }

    /**
     * 设置数据
     * @param data 字符串数组
     */
    fun setData(data: Array<String>) {
        if (data.isEmpty()) return

        // 停止当前的滚动
        stopScroll()

        // 设置数据
        dataList = data.toList()

        // 重置索引
        currentIndex = 0

        // 清除高度缓存
        textHeightCache.clear()

        // 确保文本大小正确
        checkAndAdjustTextSize()

        // 如果宽度已经可用，立即计算内容高度；否则在布局时计算
        if (width > 0) {
            calculateContentHeight()

            // 预计算所有文本的高度
            dataList.forEach { text ->
                calculateTextHeight(text)
            }
        }

        // 更新当前显示的内容
        updateCurrentView()

        // 重置View位置，确保从初始状态开始
        currentView?.translationY = 0f
        // 确保高度不为0，避免动画问题
        val animHeight = if (height > 0) height.toFloat() else 100f
        nextView?.translationY = animHeight

        // 请求重新测量和布局
        requestLayout()

        // 延迟处理滚动和高度计算，确保布局完成
        post {
            // 如果数据多于1条，开始滚动
            if (dataList.size > 1 && isAutoScroll) {
                // 延迟启动滚动，确保第一条消息有足够的显示时间
                postDelayed({ startScroll() }, 100)
            }
        }
    }

    /**
     * 设置数据
     * @param data 字符串列表
     */
    fun setData(data: List<String>) {
        setData(data.toTypedArray())
    }

    /**
     * 设置是否自动滚动
     */
    fun setAutoScroll(autoScroll: Boolean) {
        isAutoScroll = autoScroll
        if (isAutoScroll && dataList.size > 1) {
            startScroll()
        } else {
            stopScroll()
        }
    }

    /**
     * 设置文本对齐方式
     * @param gravity 对齐方式，如Gravity.CENTER, Gravity.LEFT等
     */
    fun setTextGravity(gravity: Int) {
        textGravity = gravity

        // 更新现有TextView的对齐方式
        currentView?.gravity = gravity
        nextView?.gravity = gravity
    }

    /**
     * 获取当前文本对齐方式
     */
    fun getTextGravity(): Int {
        return textGravity
    }

    /**
     * 设置最大行数
     * @param maxLines 最大行数
     */
    fun setMaxLines(maxLines: Int) {
        if (this.originalMaxLines != maxLines) {
            this.originalMaxLines = maxLines
            this.maxLines = maxLines

            // 如果有数据，重新计算内容高度
            if (dataList.isNotEmpty() && width > 0) {
                calculateContentHeight()
                requestLayout()
            }
        }
    }

    /**
     * 检查并调整文本大小，确保在不同分辨率下的一致性
     */
    private fun checkAndAdjustTextSize() {
        val density = resources.displayMetrics.density
        val scaledDensity = resources.displayMetrics.scaledDensity

        // 确保文本大小在不同设备上的一致性
        // 如果当前文本大小是通过 getDimension 获取的，需要确保它是正确的 sp 值
        if (textSize <= 0) {
            textSize = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_SP,
                14f,
                resources.displayMetrics
            )
        }
    }

    /**
     * 计算单个文本的实际显示高度
     * 参考 MarqueeText.kt 的实现方式
     */
    private fun calculateTextHeight(text: String): Int {
        if (text.isEmpty()) return 0

        // 从缓存中获取
        textHeightCache[text]?.let { return it }

        val availableWidth = width
        if (availableWidth <= 0) return 0

        // 创建 TextPaint 用于测量
        val paint = TextPaint().apply {
            textSize = <EMAIL>
            isAntiAlias = true
        }

        // 计算实际可用宽度，考虑 padding
        val paddingDp = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            4f,
            resources.displayMetrics
        ).toInt()
        val totalPaddingHorizontal = paddingLeft + paddingRight + paddingDp * 2
        val actualAvailableWidth = availableWidth - totalPaddingHorizontal

        if (actualAvailableWidth <= 0) return 0

        // 使用 StaticLayout 精确计算高度
        val staticLayout = StaticLayout.Builder.obtain(
            text, 0, text.length, paint, actualAvailableWidth
        ).setAlignment(Layout.Alignment.ALIGN_NORMAL)
            .setLineSpacing(0f, 1.0f) // 与 TextView 保持一致的行间距
            .setIncludePad(true) // 包含文本 padding，避免底部裁剪
            .build()

        // 限制行数
        val actualLines = minOf(staticLayout.lineCount, maxLines)
        val textHeight = if (actualLines < staticLayout.lineCount) {
            // 如果超过最大行数，按最大行数计算高度
            (staticLayout.getLineBottom(actualLines - 1) - staticLayout.getLineTop(0))
        } else {
            staticLayout.height
        }

        // 加上垂直 padding
        val totalHeight = textHeight + paddingDp

        // 缓存结果
        textHeightCache[text] = totalHeight

        return totalHeight
    }

    /**
     * 启动高度变化动画
     * 参考 MarqueeText.kt 的平滑过渡实现
     */
    private fun animateHeightChange(fromHeight: Int, toHeight: Int) {
        if (fromHeight == toHeight) return

        // 取消之前的动画
        heightAnimator?.cancel()

        heightAnimator = ValueAnimator.ofInt(fromHeight, toHeight).apply {
            duration = heightAnimDuration
            interpolator = LinearInterpolator() // 使用线性插值器，类似 LinearOutSlowInEasing

            addUpdateListener { animator ->
                val animatedHeight = animator.animatedValue as Int
                currentTextHeight = animatedHeight

                // 更新布局参数
                layoutParams?.let { params ->
                    params.height = animatedHeight
                    layoutParams = params
                }

                // 请求重新布局
                requestLayout()
            }

            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    isHeightAnimating = true
                }

                override fun onAnimationEnd(animation: Animator) {
                    isHeightAnimating = false
                    currentTextHeight = toHeight
                }

                override fun onAnimationCancel(animation: Animator) {
                    isHeightAnimating = false
                }
            })

            start()
        }
    }

    /**
     * 更新当前文本的高度，带平滑过渡
     */
    private fun updateTextHeight(text: String) {
        if (width <= 0) return

        val newHeight = calculateTextHeight(text)
        if (newHeight <= 0) return

        val oldHeight = currentTextHeight
        targetTextHeight = newHeight

        if (oldHeight > 0 && oldHeight != newHeight) {
            // 启动高度变化动画
            animateHeightChange(oldHeight, newHeight)
        } else {
            // 直接设置高度（首次设置或高度相同）
            currentTextHeight = newHeight
            layoutParams?.let { params ->
                params.height = newHeight
                layoutParams = params
            }
            requestLayout()
        }
    }

    /**
     * 获取当前最大行数
     */
    fun getMaxLines(): Int {
        return maxLines
    }

    /**
     * 设置高度变化动画持续时间
     * @param duration 动画持续时间（毫秒）
     */
    fun setHeightAnimationDuration(duration: Long) {
        heightAnimDuration = duration
    }

    /**
     * 获取当前文本的实际高度
     */
    fun getCurrentTextHeight(): Int {
        return currentTextHeight
    }

    /**
     * 是否正在执行高度动画
     */
    fun isHeightAnimating(): Boolean {
        return isHeightAnimating
    }

    /**
     * 更新当前显示的内容
     */
    private fun updateCurrentView() {
        if (dataList.isEmpty()) return

        val currentText = dataList[currentIndex]
        currentView?.text = currentText
        currentView?.visibility = VISIBLE
        nextView?.visibility = INVISIBLE

        // 更新高度，带平滑过渡
        updateTextHeight(currentText)
    }

    /**
     * 计算内容的最大高度和所需行数
     */
    private fun calculateContentHeight() {
        // 这个方法现在主要用于动态更新最大行数
        val availableWidth = width

        if (availableWidth <= 0) return

        var maxRequiredLines = 1

        // 创建一个临时的 TextView 来获取准确的 paint 和 padding 信息
        val tempTextView = createTextViewForMeasure()
        val paint = tempTextView.paint

        // 计算实际可用宽度，考虑 MarqueeView 和 TextView 的 padding
        val totalPaddingHorizontal = paddingLeft + paddingRight +
                                   tempTextView.paddingLeft + tempTextView.paddingRight
        val actualAvailableWidth = availableWidth - totalPaddingHorizontal

        if (actualAvailableWidth <= 0) return

        for (text in dataList) {
            if (text.isEmpty()) continue

            // 使用 StaticLayout 来精确计算行数，使用实际可用宽度
            val staticLayout = StaticLayout.Builder.obtain(
                text, 0, text.length, paint, actualAvailableWidth
            ).setAlignment(Layout.Alignment.ALIGN_NORMAL)
                .setLineSpacing(0f, 1.0f) // 与 TextView 保持一致的行间距
                .setIncludePad(false) // 不包含额外的 padding
                .build()

            // 计算当前文本所需的行数
            val requiredLines = staticLayout.lineCount
            maxRequiredLines = maxOf(maxRequiredLines, requiredLines)
        }

        // 动态更新最大行数，允许适当超过原始设置以确保内容完整显示
        // 但设置一个合理的上限（原始设置的1.5倍，最多不超过8行）
        val maxAllowedLines = minOf((originalMaxLines * 1.5).toInt(), 8)
        val newMaxLines = minOf(maxRequiredLines, maxAllowedLines)

        // 如果行数发生变化，需要重新创建TextView
        if (newMaxLines != maxLines) {
            maxLines = newMaxLines
            recreateTextViews()
            requestLayout() // 重新测量和布局
        }
    }

    /**
     * 计算文本所需的行数
     */
    private fun calculateRequiredLines(text: String, textView: TextView): Int {
        return calculateRequiredLines(text, textView, width)
    }

    /**
     * 计算文本所需的行数（指定宽度）
     */
    private fun calculateRequiredLines(text: String, textView: TextView, containerWidth: Int): Int {
        if (text.isEmpty()) return 1

        val paint = textView.paint
        val totalPaddingHorizontal = paddingLeft + paddingRight + textView.paddingLeft + textView.paddingRight
        val availableWidth = containerWidth - totalPaddingHorizontal

        if (availableWidth <= 0) return 1

        // 使用 StaticLayout 来精确计算行数，这是 Android 系统内部用于文本布局的类
        val staticLayout = StaticLayout.Builder.obtain(
            text, 0, text.length, paint, availableWidth
        ).setAlignment(Layout.Alignment.ALIGN_NORMAL)
            .setLineSpacing(0f, 1.0f) // 设置行间距，与 TextView 保持一致
            .setIncludePad(false) // 不包含额外的 padding
            .build()

        return staticLayout.lineCount
    }

    /**
     * 创建用于测量的TextView（不限制行数）
     */
    private fun createTextViewForMeasure(): TextView {
        return TextView(context).apply {
            layoutParams = LayoutParams(
                LayoutParams.MATCH_PARENT,
                LayoutParams.WRAP_CONTENT
            )
            gravity = textGravity
            isSingleLine = false
            // 不限制行数，用于准确测量
            maxLines = Int.MAX_VALUE
            setTextColor(textColor)
            setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
            // 设置与实际 TextView 相同的 padding，确保测量准确
            val paddingDp = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                4f,  // 与 createTextView() 保持一致
                resources.displayMetrics
            ).toInt()
            setPadding(paddingDp, paddingDp / 2, paddingDp, paddingDp / 2)
            // 设置行间距为1.0，与实际 TextView 保持一致
            setLineSpacing(0f, 1.0f)
        }
    }

    /**
     * 重新创建TextView
     */
    private fun recreateTextViews() {
        // 保存当前显示的文本
        val currentText = currentView?.text?.toString()

        // 移除旧的TextView
        removeAllViews()

        // 创建新的TextView
        currentView = createTextView()
        nextView = createTextView()

        // 添加到布局中
        addView(currentView)
        addView(nextView)

        // 恢复当前显示的内容
        if (!currentText.isNullOrEmpty()) {
            currentView?.text = currentText
            currentView?.visibility = VISIBLE
        }

        // 初始状态下，nextView不可见
        nextView?.visibility = INVISIBLE
    }

    /**
     * 开始滚动
     */
    private fun startScroll() {
        stopScroll()

        timer = Timer()
        timerTask = object : TimerTask() {
            override fun run() {
                post {
                    if (!isAnimating && dataList.size > 1) {
                        scrollToNext()
                    }
                }
            }
        }

        // 确保第一条消息也有完整的显示时间
        timer?.schedule(timerTask, scrollInterval, scrollInterval)
    }

    /**
     * 创建TextView并计算内容高度
     */
    private fun createTextView(): TextView {
        return TextView(context).apply {
            layoutParams = LayoutParams(
                LayoutParams.MATCH_PARENT,
                LayoutParams.WRAP_CONTENT  // 改为 WRAP_CONTENT
            )
            gravity = textGravity
            isSingleLine = false
            maxLines = <EMAIL>
            setTextColor(textColor)
            setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
            // 设置合理的 padding，避免文本被裁剪，但不要过大
            val paddingDp = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                4f,  // 减少到4dp
                resources.displayMetrics
            ).toInt()
            setPadding(paddingDp, paddingDp / 2, paddingDp, paddingDp / 2)
            // 设置行间距为1.0，避免额外的行间距
            setLineSpacing(0f, 1.0f)
            // 确保文本不会被裁剪
            includeFontPadding = true
            // 设置椭圆化方式为末尾省略
            ellipsize = android.text.TextUtils.TruncateAt.END
        }
    }

    /**
     * 停止滚动
     */
    private fun stopScroll() {
        timerTask?.cancel()
        timerTask = null
        timer?.cancel()
        timer = null
    }

    /**
     * 滚动到下一条
     */
    private fun scrollToNext() {
        if (isAnimating || dataList.isEmpty() || dataList.size <= 1) return

        // 计算下一个索引
        val nextIndex = (currentIndex + 1) % dataList.size

        // 设置下一个要显示的内容
        val nextText = dataList[nextIndex]
        nextView?.text = nextText
        nextView?.visibility = VISIBLE

        // 预先计算下一个文本的高度（如果还没有计算过）
        if (width > 0) {
            calculateTextHeight(nextText)
        }

        // 确保高度不为0，避免动画问题
        val animHeight = if (height > 0) height.toFloat() else 100f

        // 设置初始位置
        nextView?.translationY = animHeight

        // 创建当前View向上移动的动画
        val currentOutAnim = ObjectAnimator.ofFloat(currentView, "translationY", 0f, -animHeight)

        // 创建下一个View从下向上移动的动画
        val nextInAnim = ObjectAnimator.ofFloat(nextView, "translationY", animHeight, 0f)

        // 创建动画集合
        val animSet = AnimatorSet()
        animSet.playTogether(currentOutAnim, nextInAnim)
        animSet.duration = animDuration
        animSet.interpolator = AccelerateDecelerateInterpolator()

        // 添加动画监听
        animSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                isAnimating = true
            }

            override fun onAnimationEnd(animation: Animator) {
                // 交换两个View的引用
                val temp = currentView
                currentView = nextView
                nextView = temp

                // 更新索引
                currentIndex = nextIndex

                // 隐藏nextView
                nextView?.visibility = INVISIBLE

                // 更新高度到新文本的高度，带平滑过渡
                val currentText = dataList[currentIndex]
                updateTextHeight(currentText)

                // 动画结束后，重置isAnimating状态
                isAnimating = false

                // 如果是最后一条到第一条的循环，重置定时器，确保显示时间一致
                if (nextIndex == 0) {
                    // 延迟一点时间再重置定时器，确保第一条消息有足够的显示时间
                    postDelayed({ resetTimer() }, 100)
                }
            }
        })

        // 开始动画
        animSet.start()
    }

    /**
     * 重置定时器，确保每条消息显示时间一致
     */
    private fun resetTimer() {
        if (!isAutoScroll || dataList.size <= 1) return

        stopScroll()
        startScroll()
    }

    /**
     * 手动滚动到下一条
     * 可以在外部调用，手动触发滚动
     */
    fun scrollToNextManually() {
        if (!isAnimating && dataList.size > 1) {
            // 停止当前的自动滚动
            stopScroll()

            // 执行滚动
            scrollToNext()

            // 如果需要自动滚动，重新开始
            if (isAutoScroll) {
                // 延迟启动，确保有足够的显示时间
                postDelayed({ startScroll() }, 100)
            }
        }
    }

    /**
     * 停止高度动画
     */
    private fun stopHeightAnimation() {
        heightAnimator?.cancel()
        heightAnimator = null
        isHeightAnimating = false
    }

    /**
     * 在View被移除时停止滚动和动画
     */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stopScroll()
        stopHeightAnimation()
    }

    /**
     * 测量View的大小
     */
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)

        // 先测量子View，然后根据子View的高度来设置自己的高度
        val childWidthMeasureSpec = MeasureSpec.makeMeasureSpec(
            widthSize,
            MeasureSpec.EXACTLY
        )
        val childHeightMeasureSpec = MeasureSpec.makeMeasureSpec(
            0,
            MeasureSpec.UNSPECIFIED
        )

        var maxChildHeight = 0
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            child.measure(childWidthMeasureSpec, childHeightMeasureSpec)
            maxChildHeight = maxOf(maxChildHeight, child.measuredHeight)
        }

        // 计算最终高度，确保有足够的空间显示所有行
        val height = when (heightMode) {
            MeasureSpec.EXACTLY -> {
                // 如果是固定高度，使用指定的高度
                heightSize
            }
            MeasureSpec.AT_MOST -> {
                // 优先使用动态计算的高度，但不超过限制
                val dynamicHeight = if (currentTextHeight > 0) currentTextHeight else maxChildHeight
                // 确保至少有足够的高度显示内容
                val minRequiredHeight = calculateMinRequiredHeight()
                val finalHeight = maxOf(minRequiredHeight, minOf(dynamicHeight, heightSize))
                finalHeight
            }
            MeasureSpec.UNSPECIFIED -> {
                // 优先使用动态计算的高度
                val dynamicHeight = if (currentTextHeight > 0) currentTextHeight else maxChildHeight
                val minRequiredHeight = calculateMinRequiredHeight()
                maxOf(minRequiredHeight, dynamicHeight)
            }
            else -> {
                heightSize
            }
        }

        setMeasuredDimension(
            getDefaultSize(suggestedMinimumWidth, widthMeasureSpec),
            height
        )
    }

    /**
     * 计算显示当前文本所需的最小高度
     */
    private fun calculateMinRequiredHeight(): Int {
        if (dataList.isEmpty() || width <= 0) return 0

        val currentText = dataList[currentIndex]
        if (currentText.isEmpty()) return 0

        // 计算单行文本高度
        val paint = android.text.TextPaint().apply {
            textSize = <EMAIL>
            isAntiAlias = true
        }

        val fontMetrics = paint.fontMetrics
        val lineHeight = (fontMetrics.bottom - fontMetrics.top).toInt()

        // 计算 padding
        val paddingDp = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            4f,
            resources.displayMetrics
        ).toInt()

        // 返回最大行数对应的高度 + padding
        return lineHeight * maxLines + paddingDp
    }

    /**
     * 布局子View
     */
    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        // 让每个子View使用它们的实际测量高度，而不是强制填满容器
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            val childHeight = child.measuredHeight

            // 使用子View的实际测量高度，确保完整显示
            child.layout(0, 0, width, childHeight)
        }

        // 如果布局发生变化且有数据，重新计算内容高度
        if (changed && dataList.isNotEmpty() && width > 0) {
            // 清除高度缓存，因为宽度可能发生了变化
            textHeightCache.clear()

            // 延迟执行，确保布局完成后再计算
            post {
                calculateContentHeight()

                // 重新计算当前文本的高度
                if (dataList.isNotEmpty()) {
                    val currentText = dataList[currentIndex]
                    updateTextHeight(currentText)
                }
            }
        }
    }
}