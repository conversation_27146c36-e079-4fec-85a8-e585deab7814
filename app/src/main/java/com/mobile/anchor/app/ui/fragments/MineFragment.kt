package com.mobile.anchor.app.ui.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.mobile.anchor.app.navigation.Screen
import com.mobile.anchor.app.ui.screens.mine.MineScreen
import com.mobile.anchor.app.ui.screens.webview.WebViewScreen
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Background

/**
 * 个人资料Fragment
 * 使用Compose实现UI，支持内部导航到二级页面
 */
class MineFragment : Fragment() {

    private var navController: androidx.navigation.NavController? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                AnchorTheme {
                    MineFragmentContent()
                }
            }
        }
    }

    @Composable
    private fun MineFragmentContent() {
        val localNavController = rememberNavController()

        Scaffold(
            modifier = Modifier.fillMaxSize(), containerColor = Background
        ) { paddingValues ->
            NavHost(
                navController = localNavController,
                startDestination = "profile_main",
            ) {
                // 个人资料主界面
                composable("profile_main") {
                    MineScreen()
                }
            }
        }
    }

    companion object {
        fun newInstance(): MineFragment {
            return MineFragment()
        }
    }
}
