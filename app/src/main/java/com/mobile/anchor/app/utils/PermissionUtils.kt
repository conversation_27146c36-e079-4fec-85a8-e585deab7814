package com.mobile.anchor.app.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * 权限管理工具类
 * 提供常用权限的检查和请求功能
 */
object PermissionUtils {
    
    // 常用权限常量
    const val CAMERA = Manifest.permission.CAMERA
    const val WRITE_EXTERNAL_STORAGE = Manifest.permission.WRITE_EXTERNAL_STORAGE
    const val READ_EXTERNAL_STORAGE = Manifest.permission.READ_EXTERNAL_STORAGE
    const val ACCESS_FINE_LOCATION = Manifest.permission.ACCESS_FINE_LOCATION
    const val ACCESS_COARSE_LOCATION = Manifest.permission.ACCESS_COARSE_LOCATION
    const val RECORD_AUDIO = Manifest.permission.RECORD_AUDIO
    const val READ_PHONE_STATE = Manifest.permission.READ_PHONE_STATE
    const val CALL_PHONE = Manifest.permission.CALL_PHONE
    const val READ_CONTACTS = Manifest.permission.READ_CONTACTS
    const val WRITE_CONTACTS = Manifest.permission.WRITE_CONTACTS
    const val READ_SMS = Manifest.permission.READ_SMS
    const val SEND_SMS = Manifest.permission.SEND_SMS
    
    // Android 13+ 新权限
    val READ_MEDIA_IMAGES = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        Manifest.permission.READ_MEDIA_IMAGES
    } else {
        READ_EXTERNAL_STORAGE
    }

    val READ_MEDIA_VIDEO = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        Manifest.permission.READ_MEDIA_VIDEO
    } else {
        READ_EXTERNAL_STORAGE
    }

    val READ_MEDIA_AUDIO = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        Manifest.permission.READ_MEDIA_AUDIO
    } else {
        READ_EXTERNAL_STORAGE
    }

    val POST_NOTIFICATIONS = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        Manifest.permission.POST_NOTIFICATIONS
    } else {
        ""
    }
    
    /**
     * 检查单个权限是否已授予
     */
    fun isPermissionGranted(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查多个权限是否都已授予
     */
    fun arePermissionsGranted(context: Context, permissions: Array<String>): Boolean {
        return permissions.all { isPermissionGranted(context, it) }
    }
    
    /**
     * 获取未授予的权限列表
     */
    fun getDeniedPermissions(context: Context, permissions: Array<String>): Array<String> {
        return permissions.filter { !isPermissionGranted(context, it) }.toTypedArray()
    }
    
    /**
     * 请求单个权限
     */
    fun requestPermission(activity: Activity, permission: String, requestCode: Int) {
        ActivityCompat.requestPermissions(activity, arrayOf(permission), requestCode)
    }
    
    /**
     * 请求多个权限
     */
    fun requestPermissions(activity: Activity, permissions: Array<String>, requestCode: Int) {
        ActivityCompat.requestPermissions(activity, permissions, requestCode)
    }
    
    /**
     * 检查是否应该显示权限说明
     */
    fun shouldShowRequestPermissionRationale(activity: Activity, permission: String): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
    }
    
    /**
     * 检查是否有任何权限需要显示说明
     */
    fun shouldShowRequestPermissionRationale(activity: Activity, permissions: Array<String>): Boolean {
        return permissions.any { shouldShowRequestPermissionRationale(activity, it) }
    }
    
    /**
     * 打开应用设置页面
     */
    fun openAppSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            setData(Uri.fromParts("package", context.packageName, null))
        }
        context.startActivity(intent)
    }
    
    /**
     * 检查相机权限
     */
    fun isCameraPermissionGranted(context: Context): Boolean {
        return isPermissionGranted(context, CAMERA)
    }
    
    /**
     * 检查存储权限（根据 Android 版本自动选择）
     */
    fun isStoragePermissionGranted(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 不需要存储权限，或者检查具体的媒体权限
            true
        } else {
            isPermissionGranted(context, READ_EXTERNAL_STORAGE)
        }
    }
    
    /**
     * 检查位置权限
     */
    fun isLocationPermissionGranted(context: Context): Boolean {
        return isPermissionGranted(context, ACCESS_FINE_LOCATION) ||
                isPermissionGranted(context, ACCESS_COARSE_LOCATION)
    }
    
    /**
     * 检查录音权限
     */
    fun isAudioPermissionGranted(context: Context): Boolean {
        return isPermissionGranted(context, RECORD_AUDIO)
    }
    
    /**
     * 检查通知权限（Android 13+）
     */
    fun isNotificationPermissionGranted(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            isPermissionGranted(context, POST_NOTIFICATIONS)
        } else {
            true // Android 13 以下默认有通知权限
        }
    }
    
    /**
     * 获取读取图片所需的权限
     */
    fun getImagePermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(READ_MEDIA_IMAGES)
        } else {
            arrayOf(READ_EXTERNAL_STORAGE)
        }
    }
    
    /**
     * 获取读取视频所需的权限
     */
    fun getVideoPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(READ_MEDIA_VIDEO)
        } else {
            arrayOf(READ_EXTERNAL_STORAGE)
        }
    }
    
    /**
     * 获取读取音频所需的权限
     */
    fun getAudioPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(READ_MEDIA_AUDIO)
        } else {
            arrayOf(READ_EXTERNAL_STORAGE)
        }
    }
    
    /**
     * 获取相机和存储权限
     */
    fun getCameraAndStoragePermissions(): Array<String> {
        val permissions = mutableListOf(CAMERA)
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            permissions.add(WRITE_EXTERNAL_STORAGE)
        }
        return permissions.toTypedArray()
    }
    
    /**
     * 获取位置权限
     */
    fun getLocationPermissions(): Array<String> {
        return arrayOf(ACCESS_FINE_LOCATION, ACCESS_COARSE_LOCATION)
    }
    
    /**
     * 权限请求结果处理
     */
    fun handlePermissionResult(
        permissions: Array<String>,
        grantResults: IntArray,
        onAllGranted: () -> Unit,
        onDenied: (deniedPermissions: List<String>) -> Unit
    ) {
        val deniedPermissions = mutableListOf<String>()
        
        for (i in permissions.indices) {
            if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                deniedPermissions.add(permissions[i])
            }
        }
        
        if (deniedPermissions.isEmpty()) {
            onAllGranted()
        } else {
            onDenied(deniedPermissions)
        }
    }
}
