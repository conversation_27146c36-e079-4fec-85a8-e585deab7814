package com.mobile.anchor.app.ui.viewmodels

import android.net.Uri
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bdc.android.library.ktnet.tryAwait
import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.setEvent
import com.mobile.anchor.app.data.model.ImageStatusBean
import com.mobile.anchor.app.data.model.InfoStatusBean
import com.mobile.anchor.app.data.model.OnlineStatus
import com.mobile.anchor.app.data.model.ReviewBean
import com.mobile.anchor.app.data.model.SupportMeStatus
import com.mobile.anchor.app.data.model.UpdateProfileResponse
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.data.network.ApiResult
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.BodyParams
import com.mobile.anchor.app.data.service.CommonApiService
import com.mobile.anchor.app.data.service.UserApiService
import com.mobile.anchor.app.extension.extractS3ObjectKey
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.utils.toJson
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imlib.model.UserInfo
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import androidx.core.net.toUri

/**
 * 个人资料页面的ViewModel
 */
class UserViewModel : BaseViewModel() {

    private val userApiService: UserApiService by NetDelegates()
    private val commonApiService: CommonApiService by NetDelegates()

    val userData = MutableLiveData<UserBean?>()

    // 在线状态
    val onlineStatus = mutableStateOf(OnlineStatus.ONLINE)

    // 更新资料状态
    private val _updateProfileState = MutableLiveData<ApiResult<UpdateProfileResponse>?>()
    val updateProfileState: LiveData<ApiResult<UpdateProfileResponse>?> = _updateProfileState

    // 审核信息
    private val _reviewInfo = MutableLiveData<ReviewBean?>()
    val reviewInfo: MutableLiveData<ReviewBean?> = _reviewInfo

    private val _pageEvents = SharedFlowEvents<UserRequestEvent>()
    val pageEvents = _pageEvents.asSharedFlow()

    /**
     * 获取用户信息 - 使用ktHttpRequest模式（类似HomeViewModel）
     */
    fun fetchUserInfo() {
        val userId = DataStoreManager.getUserIdSync() ?: ""
        if (userId.isEmpty()) {
            LogX.e("用户ID为空，无法获取用户信息")
            return
        }

        ktHttpRequest {
            val response = userApiService.getUser().await()
            response?.let { userResponse ->
                userData.value = userResponse.anchor
                // 保存用户对象到DataStore
                DataStoreManager.saveLoginData(userResponse)
                LogX.d("用户信息获取成功: ${userResponse.anchor?.nickname}")

                // 同时获取审核信息
                fetchReviewInfo()
            }
        }
    }

    /**
     * 获取审核信息
     */
    fun fetchReviewInfo() {
        ktHttpRequest {
            val response = userApiService.getReviewInfo().await()
            response?.let {
                DataStoreManager.saveReviewObject(it)
                _reviewInfo.value = it
                LogX.d("审核信息获取成功: 审核状态=${it.stat}, 头像状态=${it.avatar_result}, 昵称状态=${it.nickname_result}")
            }
        }
    }

    /**
     * 更新在线状态
     */
    fun updateOnlineStatus(status: OnlineStatus) {
        viewModelScope.launch {
            // 模拟API调用
            // 实际项目中应该调用API更新状态
            onlineStatus.value = status

            // 更新本地用户数据
//            _userData.value?.let { currentUser ->
//                _userData.postValue(
//                    currentUser.copy(onlineStatus = status.value)
//                )
//            }

            // 这里可以添加成功/失败的回调
        }
    }

    /**
     * 更新用户资料 - 使用ktHttpRequest模式（整体提交，用于BuildProfileScreen）
     */
    fun updateProfile(
        avatar: String,
        birthdayAt: Long,
        firebaseToken: String = "",
        inviteCode: String,
        nickname: String,
        updateType: Int = 0,
        onSuccess: (() -> Unit)? = null
    ) {
        ktHttpRequest {
            val response = userApiService.updateProfile(
                BodyParams.updateProfileBody(
                    avatar = avatar,
                    birthdayAt = birthdayAt,
                    gender = 2,
                    inviteCode = inviteCode,
                    nickname = nickname,
                    firebaseToken = firebaseToken,
                    updateType = updateType
                )
            ).await()
            response?.let {
                // 设置资料已完善
                DataStoreManager.setProfileCompleted(true)
                LogX.d("用户资料更新成功，准备跳转到首页")

                // 调用成功回调，通知调用方跳转到首页
                onSuccess?.invoke()
            }
        }
    }

    /**
     * 更新单个字段 - 用于ProfileEditScreen的即时更新
     */
    fun updateSingleField(
        avatar: String? = null,
        birthdayAt: Long? = null,
        nickname: String? = null,
        inviteCode: String? = null,
        firebaseToken: String = "",
        updateType: Int = 3, // 1=更新基本信息
        onSuccess: ((UserBean?) -> Unit)? = null,
        onError: ((String) -> Unit)? = null
    ) {
        ktHttpRequest {
            // 获取当前用户数据作为基础
            val currentUser = userData.value
            val response = userApiService.updateProfile(
                BodyParams.updateProfileBody(
                    avatar = avatar ?: currentUser?.avatar?.extractS3ObjectKey() ?: "",
                    birthdayAt = birthdayAt ?: currentUser?.birthday_at?.toLongOrNull() ?: 0L,
                    gender = 2,
                    inviteCode = inviteCode ?: "",
                    nickname = nickname ?: currentUser?.nickname ?: "",
                    firebaseToken = firebaseToken,
                    updateType = updateType
                )
            ).await()

            response?.let {
                _reviewInfo.value = null

                LogX.d("单个字段更新成功")
                // 更新本地用户缓存
                viewModelScope.launch {
                    updateLocalUserData(avatar, birthdayAt, nickname, inviteCode)
                }

                // 重新获取用户信息以确保数据同步
                fetchUserInfo()

                // 调用成功回调
                onSuccess?.invoke(userData.value)

            }
        }
    }

    /**
     * 更新本地用户数据缓存
     */
    private suspend fun updateLocalUserData(
        avatar: String? = null,
        birthdayAt: Long? = null,
        nickname: String? = null,
        inviteCode: String? = null
    ) {
        try {
            DataStoreManager.updateUserObject { currentUser ->
                currentUser?.copy(
                    avatar = avatar ?: currentUser.avatar,
                    birthday_at = birthdayAt?.toString() ?: currentUser.birthday_at,
                    nickname = nickname ?: currentUser.nickname
                    // inviteCode 通常不在UserBean中，如果需要可以添加
                )
            }
            LogX.d("本地用户数据缓存更新成功")
        } catch (e: Exception) {
            LogX.e("更新本地用户数据缓存失败", e)
        }
    }


    /**
     * 更新主播相册
     */
    fun updateAlbum(items: List<UserBean.AlbumBean>, onResult: (Boolean) -> Unit = {}) {
        ktHttpRequest {
            userApiService.updateAlbum(BodyParams.updateAlbumBody(items)).await()
            LogX.d("更新主播相册成功")
            onResult(true)
        }
    }

    fun checkImageStatus(objectKey: String, block: (ImageStatusBean) -> Unit = {}) {
        ktHttpRequest {
            val response =
                commonApiService.checkImageStatus(BodyParams.checkImageStatusBody(objectKey))
                    .await()
            response?.let {
                block.invoke(it)
            }
        }
    }

    fun checkInfo(nickname: String, inviteCode: String, block: (InfoStatusBean) -> Unit) {
        ktHttpRequest {
            val response = commonApiService.checkInfoStatus(
                BodyParams.checkInfoBody(
                    nickname, inviteCode
                )
            ).await()
            response?.let {
                block.invoke(it)
            }
        }
    }

    /**
     * 清除更新资料状态
     */
    fun clearUpdateProfileState() {
        _updateProfileState.postValue(null)
    }

    /**
     * 批量获取用户信息
     */
    fun fetchUserList(anchorIds: MutableList<String>) {
        viewModelScope.launch {
            val anchors =
                userApiService.getUserList(BodyParams.getUserListBody(anchorIds)).tryAwait {}
            anchors?.records?.forEach {
                val userInfo = UserInfo(it.id, it.nickname, it.showAvatar.toUri())
                userInfo.extra = it.toJson()
                RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
            }
        }
    }

    fun fetchSupportMeStatus(userId: String) {
        ktHttpRequest {
            val response = userApiService.getSupportMeStatus(userId).tryAwait {
                LogX.e("fetchSupportMe request error $it")
            }

            response?.let {
                _pageEvents.setEvent(UserRequestEvent.FetchSupportMeStatus(it))
            }
        }
    }

    fun requestSupportMe(userId: String) {
        ktHttpRequest {
            val response =
                userApiService.requestSupportMe(BodyParams.supportMeBody(userId.toIntOrNull() ?: 0))
                    .tryAwait {
                        _pageEvents.setEvent(UserRequestEvent.RequestSupport(false))
                    }
            response?.let {
                _pageEvents.setEvent(UserRequestEvent.RequestSupport(true))
            }
        }
    }

}

sealed class UserRequestEvent {
    data class FetchUserListFailed(val anchorIds: MutableList<String>, val msg: String) :
        UserRequestEvent()

    data class FetchSupportMeStatus(val status: SupportMeStatus) : UserRequestEvent()

    data class RequestSupport(val result: Boolean) : UserRequestEvent()
}