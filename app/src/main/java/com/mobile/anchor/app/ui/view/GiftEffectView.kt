package com.mobile.anchor.app.ui.view

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.Keyframe
import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import com.bdc.android.library.extension.setDrawableRight
import com.mobile.anchor.app.R
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.opensource.svgaplayer.SVGAVideoEntity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/7/29 16:57
 * @description :
 */
class GiftEffectView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs), CoroutineScope by MainScope() {

    private val svgaImageView = SVGAImageView(context).apply {
        val size = context.resources.getDimension(R.dimen.dp_80) // dp to px
        layoutParams = LayoutParams(size.toInt(), size.toInt()).apply {
            gravity = Gravity.CENTER
        }
        scaleType = ImageView.ScaleType.FIT_CENTER
    }

    private val imageView = ImageView(context).apply {
        val size = context.resources.getDimension(R.dimen.dp_16) // dp to px
        layoutParams = LayoutParams(size.toInt(), size.toInt()).apply {
            gravity = Gravity.CENTER_VERTICAL or Gravity.START
        }
        setBackgroundResource(R.mipmap.ic_gift_heart)
        scaleType = ImageView.ScaleType.FIT_CENTER
    }

    // 新增：用于 "x" 前缀
    private val countPrefixTextView = TextView(context).apply {
        text = "x "
        typeface = ResourcesCompat.getFont(context, R.font.fugazone_regular)
        setTextColor(Color.WHITE)
        setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f)
        setShadowLayer(4f, 0f, 0f, Color.BLACK)
        layoutParams = LinearLayout.LayoutParams( // 使用 LinearLayout.LayoutParams
            LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
        )
    }

    // 新增：用于显示当前数字
    private val currentCountNumberTextView = TextView(context).apply {
        text = "0" // 初始显示为 0
        typeface = ResourcesCompat.getFont(context, R.font.fugazone_regular)
        setTextColor(Color.WHITE)
        setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
        setShadowLayer(4f, 0f, 0f, Color.BLACK)
        layoutParams = FrameLayout.LayoutParams( // 使用 FrameLayout.LayoutParams 实现重叠
            LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.CENTER // 默认在 FrameLayout 中居中，具体位置由父容器决定
        }
    }

    // 新增：用于显示即将翻转到的数字
    private val nextCountNumberTextView = TextView(context).apply {
        text = "0" // 初始值，用于第一次翻转
        typeface = ResourcesCompat.getFont(context, R.font.fugazone_regular)
        setTextColor(Color.WHITE)
        setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
        setShadowLayer(4f, 0f, 0f, Color.BLACK)
        rotationX = -90f // 初始隐藏，向上翻转 90 度
        layoutParams = FrameLayout.LayoutParams( // 使用 FrameLayout.LayoutParams 实现重叠
            LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
        )
    }

    // 组合 "x" 和数字部分的容器
    private val countTextContainer = LinearLayout(context).apply {
        orientation = LinearLayout.HORIZONTAL
        gravity = Gravity.CENTER_VERTICAL // 内部元素水平居中
        layoutParams = LayoutParams(
            LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.CENTER_VERTICAL or Gravity.END
//            topMargin = svgaImageView.layoutParams.height
            leftMargin =
                imageView.layoutParams.width + context.resources.getDimensionPixelSize(R.dimen.dp_5)
        }
        addView(countPrefixTextView) // 添加 "x"
        // 为可翻转数字创建一个 FrameLayout，使其重叠
        val flipNumberContainer = FrameLayout(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
            )
            addView(currentCountNumberTextView)
            addView(nextCountNumberTextView)
        }
        addView(flipNumberContainer) // 添加翻转数字容器

//        addView(ImageView(context).apply {
//            setImageResource(R.mipmap.ic_coin_small)
//            layoutParams = LinearLayout.LayoutParams(
//                LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
//            ).apply {
//                gravity = Gravity.CENTER_VERTICAL
//                leftMargin = resources.getDimensionPixelSize(R.dimen.dp_4)
//            }
//        })
    }

    private var svgaParser = SVGAParser(context)
    private var giftFile: String? = null
    private var currentCount = 0 // 单次计数器
    private var totalCount = 0 // 总计数器，用于翻转效果
    private var countdownJob: Job? = null
    private var onGiftShown: ((Int) -> Unit)? = null

    init {
        clipChildren = false
        clipToPadding = false

        setBackgroundResource(R.drawable.discover_video_bg_03)
        addView(imageView)
        addView(countTextContainer) // 添加新的计数文本容器

        setPadding(
            context.resources.getDimensionPixelSize(R.dimen.dp_12),
            context.resources.getDimensionPixelSize(R.dimen.dp_3),
            context.resources.getDimensionPixelSize(R.dimen.dp_12),
            context.resources.getDimensionPixelSize(R.dimen.dp_3)
        )
    }

    fun setGiftAnimation(file: String) {
        this.giftFile = file
//        play(true)
    }

    fun setOnGiftShownListener(listener: (Int) -> Unit) {
        this.onGiftShown = listener
    }

    fun play(initialize: Boolean = false, coin: Int = 0) {
        // 播放 SVGA 动画（若没在播）
        giftFile?.let { file ->
            if (!svgaImageView.isAnimating) {
                try {
                    svgaParser.decodeFromAssets(file, object : SVGAParser.ParseCompletion {
                        override fun onComplete(videoItem: SVGAVideoEntity) {
                            svgaImageView.setVideoItem(videoItem)
                            svgaImageView.startAnimation()
                        }

                        override fun onError() {
                            // 可选日志
                        }
                    })
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        if (initialize) {
            return
        }

        currentCount++
        // 更新总计数
        totalCount++

        // 触发数字翻转动画
        animateNumberFlip(totalCount)

        // 您原有的上浮数字动画 (如果还需要这个效果)
        animateCountText("+$coin")

        onGiftShown?.invoke(totalCount)

        // 倒计时清零逻辑（用于重置连击计数，例如 3 秒内无新礼物则清零）
        countdownJob?.cancel()
        countdownJob = launch {
            delay(3000)
            currentCount = 0 // 连击计数清零
            // 重置显示的数字
//            currentCountNumberTextView.text = "0"
//            nextCountNumberTextView.text = "1" // 准备下一次动画
            currentCountNumberTextView.rotationX = 0f // 确保复位
            nextCountNumberTextView.rotationX = -90f // 确保复位
        }
    }

    /**
     * 为数字部分执行 3D 翻转动画
     * @param newCount 即将显示的新数字
     */
    private fun animateNumberFlip(newCount: Int) {
        val animationDuration = 200L // 翻转动画时长

        // 设置下一个要显示的数字
        nextCountNumberTextView.text = newCount.toString()

        // 当前数字翻出 (0度 -> 90度)
        val currentFlipOut =
            ObjectAnimator.ofFloat(currentCountNumberTextView, "rotationX", 0f, 90f).apply {
                duration = animationDuration
                interpolator = AccelerateDecelerateInterpolator()
            }

        // 即将翻入的数字翻入 (-90度 -> 0度)
        val nextFlipIn =
            ObjectAnimator.ofFloat(nextCountNumberTextView, "rotationX", -90f, 0f).apply {
                duration = animationDuration
                interpolator = AccelerateDecelerateInterpolator()
            }

        // 组合动画并播放
        AnimatorSet().apply {
            playTogether(currentFlipOut, nextFlipIn) // 同时播放两个动画
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // 动画结束后，将当前数字 TextView 的文本更新为新数字
                    // 并将其旋转状态复位，以便下一次动画
                    currentCountNumberTextView.text = newCount.toString()
                    currentCountNumberTextView.rotationX = 0f
                    nextCountNumberTextView.rotationX = -90f // 隐藏 next 准备下一次翻转
                }
            })
            start()
        }
    }

    // 原有的上浮文字动画，保持不变
    private fun animateCountText(text: String = "x1") {
        val context = this.context
        post {
            val textView = TextView(context).apply {
                this.text = text
                setTypeface(ResourcesCompat.getFont(context, R.font.fugazone_regular))
                setTextColor(Color.WHITE)
                setDrawableRight(
                    R.mipmap.ic_coin_small, context.resources.getDimensionPixelSize(R.dimen.dp_1)
                )
                setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
                setShadowLayer(4f, 0f, 0f, Color.BLACK)
                layoutParams = LayoutParams(
                    LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT
                ).apply {
                    gravity = Gravity.CENTER_HORIZONTAL or Gravity.CENTER
                }
                gravity = Gravity.CENTER_HORIZONTAL or Gravity.CENTER
            }

            // 添加到 GiftEffectView 内部
            addView(textView)

            // ===== 定位起点：SVGA 图标正上方中央 =====
            // 确保 svgaImageView 已经测量布局，否则其 top 和 measuredHeight 可能为 0
//            val layoutParams = textView.layoutParams as FrameLayout.LayoutParams
//            // 更好的方式是使用 svgaImageView 的中心点作为参考
//            layoutParams.topMargin = (svgaImageView.top + svgaImageView.height / 2 - textView.measuredHeight / 2).coerceAtLeast(0)
//            textView.layoutParams = layoutParams

            // ==== 动画构建 ====
            val duration = 1000L

            // 上飘
            val translateY = ObjectAnimator.ofFloat(textView, "translationY", 0f, -150f)

            // 淡出
            val fadeOut = ObjectAnimator.ofFloat(textView, "alpha", 1f, 0f)

            // 左右轻微抖动（不规则 S 形）
            val kf0 = Keyframe.ofFloat(0f, 0f)
            val kf1 = Keyframe.ofFloat(0.25f, (-6..6).random().toFloat())
            val kf2 = Keyframe.ofFloat(0.5f, (-8..8).random().toFloat())
            val kf3 = Keyframe.ofFloat(0.75f, (-9..9).random().toFloat())
            val kf4 = Keyframe.ofFloat(1f, (-6..6).random().toFloat())

            val holder = PropertyValuesHolder.ofKeyframe("translationX", kf0, kf1, kf2, kf3, kf4)
            val translateX = ObjectAnimator.ofPropertyValuesHolder(textView, holder).apply {
                this.duration = duration
                interpolator = AccelerateDecelerateInterpolator()
            }

            // 播放组合动画
            AnimatorSet().apply {
                playTogether(translateY, fadeOut, translateX)
                this.duration = duration
                start()
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        <EMAIL>(textView)
                    }
                })
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        countdownJob?.cancel()
        cancel()
        // 确保 SVGA 资源完全释放
        svgaImageView.stopAnimation()
        svgaImageView.clearAnimation()
        svgaImageView.setVideoItem(null)
    }
}