package com.mobile.anchor.app.ui.screens.language

import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.BodyParams
import com.mobile.anchor.app.data.service.UserApiService
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.viewmodels.BaseViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 语言选择页面ViewModel
 */
class LanguageViewModel : BaseViewModel() {

    private val userApiService: UserApiService by NetDelegates()

    private val _uiState = MutableStateFlow(LanguageUiState())
    val uiState: StateFlow<LanguageUiState> = _uiState.asStateFlow()

    /**
     * 加载语言列表
     */
    fun loadLanguages() {
        ktHttpRequest {
            val response = userApiService.getLanguageList().await()
            response?.let { languageList ->
                _uiState.value = _uiState.value.copy(
                    languages = languageList.records ?: emptyList(), isLoading = false
                )
                LogX.d("语言列表加载成功，共${languageList.records?.size}种语言")
            }
        }
    }

    /**
     * 选择语言
     */
    fun selectLanguage(currentLanguage: String) {
        ktHttpRequest {
            val response = userApiService.setLanguage(
                BodyParams.setLanguageBody(currentLanguage)
            ).await()

            response?.let {
                // 更新本地UI状态
                val updatedLanguages = _uiState.value.languages.map { language ->
                    language.copy(is_select = language.language == currentLanguage)
                }

                _uiState.value = _uiState.value.copy(
                    languages = updatedLanguages,
                    isChanged = true,
                    currentCode = currentLanguage
                )
                DataStoreManager.saveCurrentLanguage(currentLanguage)

                LogX.d("语言设置成功: $currentLanguage")
            }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * 语言选择页面UI状态
 */
data class LanguageUiState(
    val languages: List<UserBean.LanguageBean> = emptyList(),
    val isLoading: Boolean = true,
    val error: String? = null,
    val isChanged: Boolean = false,
    val currentCode: String? = null
)
