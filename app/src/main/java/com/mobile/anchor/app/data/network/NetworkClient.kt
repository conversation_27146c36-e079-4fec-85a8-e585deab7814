package com.mobile.anchor.app.data.network

import com.bdc.android.library.http.CoreRetrofit.SSLSocketClient
import com.google.gson.GsonBuilder
import com.mobile.anchor.app.data.service.ApiService
import kotlinx.serialization.json.Json
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.kotlinx.serialization.asConverterFactory
import java.util.concurrent.TimeUnit

/**
 * 网络客户端单例
 * 负责配置和提供 Retrofit 实例
 */
object NetworkClient {

    private val networkConfig = NetworkConfig
    private val authManager = AuthManager
    private val apiParamsBuilder = ApiParamsBuilder

    // JSON 配置
    private val json = Json {
        ignoreUnknownKeys = true
        coerceInputValues = true
        encodeDefaults = true
    }

    // OkHttp 客户端
    private val okHttpClient: OkHttpClient by lazy {
        OkHttpClient.Builder().connectTimeout(networkConfig.getConnectTimeout(), TimeUnit.SECONDS)
            .readTimeout(networkConfig.getReadTimeout(), TimeUnit.SECONDS)
            .writeTimeout(networkConfig.getWriteTimeout(), TimeUnit.SECONDS)
            .addInterceptor(createCommonParamsInterceptor())
            .addInterceptor(createLoggingInterceptor()).addInterceptor(createAuthInterceptor())
//            .addInterceptor(LoggerInterceptor())
            .addInterceptor(DetailedLogInterceptor())
            .sslSocketFactory(SSLSocketClient.sSLSocketFactory, SSLSocketClient.trustManager)
            .hostnameVerifier(SSLSocketClient.hostnameVerifier).build()
    }

    // Retrofit 实例
    private val retrofit: Retrofit by lazy {
        Retrofit.Builder().baseUrl(networkConfig.getBaseUrl()).client(okHttpClient)
            .addConverterFactory(
                GsonConverterFactory.create(
                    GsonBuilder().setLenient().registerTypeAdapterFactory(ApiResultAdapterFactory())
                        .create()
                )
            ).addConverterFactory(json.asConverterFactory("application/json".toMediaType())).build()
    }

    /**
     * 创建API服务
     */
    fun <T> createService(serviceClass: Class<T>): T {
        return retrofit.create(serviceClass)
    }

    /**
     * 创建日志拦截器
     */
    private fun createLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level = if (networkConfig.isLoggingEnabled()) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.NONE
            }
        }
    }

    /**
     * 创建鉴权拦截器
     */
    private fun createAuthInterceptor() = Interceptor { chain ->
        val originalRequest = chain.request()
        val authHeaders = authManager.createAuthHeaders()

        val newRequestBuilder = originalRequest.newBuilder()
        authHeaders.forEach { (key, value) ->
            newRequestBuilder.addHeader(key, value)
        }

        val newRequest = newRequestBuilder.build()
        chain.proceed(newRequest)
    }

    /**
     * 创建通用参数拦截器
     */
    private fun createCommonParamsInterceptor() = Interceptor { chain ->
        val originalRequest = chain.request()
        val originalUrl = originalRequest.url

        // 获取通用参数
        val commonParams = apiParamsBuilder.createCommonParams()

        // 构建新的URL，添加通用查询参数
        val newUrlBuilder = originalUrl.newBuilder()
        commonParams.forEach { (key, value) ->
            newUrlBuilder.addQueryParameter(key, value.toString())
        }

        val newRequest = originalRequest.newBuilder().url(newUrlBuilder.build()).build()

        chain.proceed(newRequest)
    }


    /**
     * 更新 Base URL（如果需要动态切换环境）
     */
    fun updateBaseUrl(newBaseUrl: String): ApiService {
        return Retrofit.Builder().baseUrl(newBaseUrl).client(okHttpClient)
            .addConverterFactory(json.asConverterFactory("application/json".toMediaType())).build()
            .create(ApiService::class.java)
    }

    /**
     * 获取鉴权管理器
     */
    fun getAuthManager(): AuthManager {
        return authManager
    }

    /**
     * 获取通用参数管理器
     */
    fun getApiParamsBuilder(): ApiParamsBuilder {
        return apiParamsBuilder
    }
}
