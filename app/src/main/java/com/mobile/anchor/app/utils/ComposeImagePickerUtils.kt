package com.mobile.anchor.app.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import com.mobile.anchor.app.data.network.ApiResult
import com.mobile.anchor.app.manager.FileUploadManager
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Compose 图片选择工具类
 * 提供在 Compose 中使用的图片选择功能
 */
object ComposeImagePickerUtils {

    /**
     * 图片选择类型
     */
    enum class ImagePickerType {
        CAMERA,     // 相机拍照
        GALLERY,    // 相册选择
        GOOGLE_PHOTOS // Google Photos
    }

    /**
     * 图片选择配置
     */
    data class ImagePickerConfig(
        val showCamera: Boolean = true,
        val showGallery: Boolean = true,
        val showGooglePhotos: Boolean = false,
        val cameraTitle: String = "Camera",
        val galleryTitle: String = "Gallery",
        val googlePhotosTitle: String = "Google Photos"
    )

    /**
     * 检查并请求权限
     */
    fun checkAndRequestPermissions(
        context: Context,
        permissionLauncher: ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>,
        action: () -> Unit
    ) {
        val permissions = mutableListOf<String>()

        // 检查相机权限
        if (ContextCompat.checkSelfPermission(
                context, Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            permissions.add(Manifest.permission.CAMERA)
        }

        // 检查存储权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    context, Manifest.permission.READ_MEDIA_IMAGES
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
            }
        } else {
            if (ContextCompat.checkSelfPermission(
                    context, Manifest.permission.READ_EXTERNAL_STORAGE
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
        }

        if (permissions.isNotEmpty()) {
            permissionLauncher.launch(permissions.toTypedArray())
        } else {
            action()
        }
    }

    /**
     * 创建图片URI用于相机拍照
     */
    fun createImageUri(context: Context): Uri {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val imageFileName = "JPEG_${timeStamp}_"
        val storageDir = File(context.cacheDir, "images")

        if (!storageDir.exists()) {
            storageDir.mkdirs()
        }

        val imageFile = File.createTempFile(imageFileName, ".jpg", storageDir)

        return FileProvider.getUriForFile(
            context, "${context.packageName}.fileprovider", imageFile
        )
    }

    /**
     * 启动相机拍照
     */
    fun launchCamera(
        context: Context,
        cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>,
        permissionLauncher: ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>,
        onUriCreated: (Uri) -> Unit
    ) {
        checkAndRequestPermissions(context, permissionLauncher) {
            val uri = createImageUri(context)
            onUriCreated(uri)
            cameraLauncher.launch(uri)
        }
    }

    /**
     * 启动相册选择
     */
    fun launchGallery(
        context: Context,
        galleryLauncher: ManagedActivityResultLauncher<String, Uri?>,
        permissionLauncher: ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>
    ) {
        checkAndRequestPermissions(context, permissionLauncher) {
            galleryLauncher.launch("image/*")
        }
    }

    /**
     * 启动Google Photos
     */
    fun launchGooglePhotos(
        context: Context,
        photoPickerLauncher: ManagedActivityResultLauncher<Intent, ActivityResult>,
        permissionLauncher: ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>
    ) {
        checkAndRequestPermissions(context, permissionLauncher) {
            val intent = Intent(
                Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI
            ).apply {
                type = "image/*"
                setPackage("com.google.android.apps.photos")
            }

            // 如果Google Photos不可用，回退到系统相册
            if (intent.resolveActivity(context.packageManager) == null) {
                intent.setPackage(null)
            }

            photoPickerLauncher.launch(intent)
        }
    }

    /**
     * 获取图片选择选项列表
     */
    fun getImagePickerOptions(config: ImagePickerConfig = ImagePickerConfig()): List<String> {
        val options = mutableListOf<String>()
        if (config.showCamera) options.add(config.cameraTitle)
        if (config.showGallery) options.add(config.galleryTitle)
        if (config.showGooglePhotos) options.add(config.googlePhotosTitle)
        return options
    }

    /**
     * 根据选项索引和配置获取图片选择类型
     */
    fun getImagePickerType(
        index: Int, config: ImagePickerConfig = ImagePickerConfig()
    ): ImagePickerType {
        val options = mutableListOf<ImagePickerType>()
        if (config.showCamera) options.add(ImagePickerType.CAMERA)
        if (config.showGallery) options.add(ImagePickerType.GALLERY)
        if (config.showGooglePhotos) options.add(ImagePickerType.GOOGLE_PHOTOS)

        return if (index in options.indices) options[index] else ImagePickerType.GALLERY
    }

    /**
     * 处理图片选择结果
     */
    fun handleImagePickerResult(
        type: ImagePickerType,
        context: Context,
        cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>,
        galleryLauncher: ManagedActivityResultLauncher<String, Uri?>,
        photoPickerLauncher: ManagedActivityResultLauncher<Intent, ActivityResult>,
        permissionLauncher: ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>,
        onUriCreated: (Uri) -> Unit = {}
    ) {
        when (type) {
            ImagePickerType.CAMERA -> {
                launchCamera(context, cameraLauncher, permissionLauncher, onUriCreated)
            }

            ImagePickerType.GALLERY -> {
                launchGallery(context, galleryLauncher, permissionLauncher)
            }

            ImagePickerType.GOOGLE_PHOTOS -> {
                launchGooglePhotos(context, photoPickerLauncher, permissionLauncher)
            }
        }
    }

    /**
     * 处理选项选择
     */
    fun handleOptionSelected(
        index: Int,
        context: Context,
        cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>,
        galleryLauncher: ManagedActivityResultLauncher<String, Uri?>,
        photoPickerLauncher: ManagedActivityResultLauncher<Intent, ActivityResult>,
        permissionLauncher: ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>,
        config: ImagePickerConfig = ImagePickerConfig(),
        onUriCreated: (Uri) -> Unit = {}
    ) {
        val type = getImagePickerType(index, config)
        handleImagePickerResult(
            type = type,
            context = context,
            cameraLauncher = cameraLauncher,
            galleryLauncher = galleryLauncher,
            photoPickerLauncher = photoPickerLauncher,
            permissionLauncher = permissionLauncher,
            onUriCreated = onUriCreated
        )
    }

    /**
     * 带权限申请的图片选择处理（支持权限申请后继续执行用户意图）
     */
    fun handleOptionSelectedWithPermissionContinuation(
        index: Int,
        context: Context,
        cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>,
        galleryLauncher: ManagedActivityResultLauncher<PickVisualMediaRequest, Uri?>,
        photoPickerLauncher: ManagedActivityResultLauncher<Intent, ActivityResult>,
        permissionLauncher: ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>,
        config: ImagePickerConfig = ImagePickerConfig(),
        onUriCreated: (Uri) -> Unit = {},
        onPermissionGranted: () -> Unit = {},
        onPermissionDenied: () -> Unit = {}
    ) {
        val type = getImagePickerType(index, config)

        // 检查权限
        val permissions = mutableListOf<String>()

        when (type) {
            ImagePickerType.CAMERA -> {
                if (ContextCompat.checkSelfPermission(
                        context, Manifest.permission.CAMERA
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    permissions.add(Manifest.permission.CAMERA)
                }
            }

            ImagePickerType.GALLERY, ImagePickerType.GOOGLE_PHOTOS -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    if (ContextCompat.checkSelfPermission(
                            context, Manifest.permission.READ_MEDIA_IMAGES
                        ) != PackageManager.PERMISSION_GRANTED
                    ) {
                        permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
                    }
                } else {
                    if (ContextCompat.checkSelfPermission(
                            context, Manifest.permission.READ_EXTERNAL_STORAGE
                        ) != PackageManager.PERMISSION_GRANTED
                    ) {
                        permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
                    }
                }
            }
        }

        if (permissions.isNotEmpty()) {
            // 需要申请权限
            permissionLauncher.launch(permissions.toTypedArray())
        } else {
            // 权限已授予，直接执行操作
            onPermissionGranted()
            executeImagePickerAction(
                type = type,
                context = context,
                cameraLauncher = cameraLauncher,
                galleryLauncher = galleryLauncher,
                photoPickerLauncher = photoPickerLauncher,
                onUriCreated = onUriCreated
            )
        }
    }

    /**
     * 执行图片选择操作（权限申请成功后调用）
     */
    fun executeImagePickerAction(
        type: ImagePickerType,
        context: Context,
        cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>? = null,
        galleryLauncher: ManagedActivityResultLauncher<PickVisualMediaRequest, Uri?>? = null,
        multipleMediaPickerLauncher: ManagedActivityResultLauncher<PickVisualMediaRequest, List<@JvmSuppressWildcards Uri>>? = null,
        photoPickerLauncher: ManagedActivityResultLauncher<Intent, ActivityResult>? = null,
        onUriCreated: (Uri) -> Unit
    ) {
        when (type) {
            ImagePickerType.CAMERA -> {
                val uri = createImageUri(context)
                onUriCreated(uri)
                cameraLauncher?.launch(uri)
            }

            ImagePickerType.GALLERY -> {
                galleryLauncher?.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
                    ?: run {
                        multipleMediaPickerLauncher?.launch(
                            PickVisualMediaRequest(
                                ActivityResultContracts.PickVisualMedia.ImageAndVideo
                            )
                        )
                    }
            }

            ImagePickerType.GOOGLE_PHOTOS -> {
                val intent = Intent(Intent.ACTION_PICK).apply {
                    setType("image/*")
                    putExtra(Intent.EXTRA_ALLOW_MULTIPLE, false)
                }
                photoPickerLauncher?.launch(intent)
            }
        }
    }
}

/**
 * Compose 中记住图片选择配置
 */
@Composable
fun rememberImagePickerConfig(
    showCamera: Boolean = true,
    showGallery: Boolean = true,
    showGooglePhotos: Boolean = false,
    cameraTitle: String = "Camera",
    galleryTitle: String = "Gallery",
    googlePhotosTitle: String = "Google Photos"
): ComposeImagePickerUtils.ImagePickerConfig {
    return remember {
        ComposeImagePickerUtils.ImagePickerConfig(
            showCamera = showCamera,
            showGallery = showGallery,
            showGooglePhotos = showGooglePhotos,
            cameraTitle = cameraTitle,
            galleryTitle = galleryTitle,
            googlePhotosTitle = googlePhotosTitle
        )
    }
}

/**
 * 头像选择器状态管理
 */
@Composable
fun rememberAvatarPickerState(
    onAvatarSelected: (String) -> Unit,
    onUploadStart: () -> Unit = {},
    onUploadEnd: () -> Unit = {},
    onError: (String) -> Unit = {},
    config: ComposeImagePickerUtils.ImagePickerConfig = rememberImagePickerConfig()
): AvatarPickerState {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    var currentPhotoUri by remember { mutableStateOf<Uri?>(null) }
    var pendingImagePickerType by remember { mutableStateOf(ComposeImagePickerUtils.ImagePickerType.GALLERY) }
    var isUploading by remember { mutableStateOf(false) }

    // 处理头像上传
    val handleAvatarUpload = { uri: Uri? ->
        uri?.let {
            isUploading = true
            onUploadStart()
            coroutineScope.launch {
                try {
                    FileUploadManager.upload(
                        context, uri, fileType = FileUploadManager.FileType.IMAGE
                    ).collect { result ->
                        when (result) {
                            is ApiResult.Success -> {
                                val objectKey = result.getDataOrNull()?.objectKey ?: ""
                                onAvatarSelected(objectKey)
                                isUploading = false
                                onUploadEnd()
                            }

                            is ApiResult.Error -> {
                                onError(result.message)
                                isUploading = false
                                onUploadEnd()
                            }

                            is ApiResult.Loading -> {
                                // 保持加载状态
                            }
                        }
                    }
                } catch (e: Exception) {
                    onError(e.message ?: "Upload failed")
                    isUploading = false
                    onUploadEnd()
                }
            }
        }
    }

    // 相机拍照启动器
    val cameraLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        if (success && currentPhotoUri != null) {
            handleAvatarUpload(currentPhotoUri)
        }
        currentPhotoUri = null
    }

    // 相册选择启动器
    val galleryLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.PickVisualMedia()
    ) { uri ->
        if (uri != null) {
            handleAvatarUpload(uri)
        }
    }

    // Google Photos启动器
    val photoPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val uri = result.data?.data
            if (uri != null) {
                handleAvatarUpload(uri)
            }
        }
    }

    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            // 权限申请成功，继续执行用户意图
            ComposeImagePickerUtils.executeImagePickerAction(
                type = pendingImagePickerType,
                context = context,
                cameraLauncher = cameraLauncher,
                galleryLauncher = galleryLauncher,
                photoPickerLauncher = photoPickerLauncher,
                onUriCreated = { uri -> currentPhotoUri = uri })
        }
    }

    return remember {
        AvatarPickerState(
            context = context,
            config = config,
            cameraLauncher = cameraLauncher,
            galleryLauncher = galleryLauncher,
            photoPickerLauncher = photoPickerLauncher,
            permissionLauncher = permissionLauncher,
            isUploading = isUploading,
            onSetPendingType = { type -> pendingImagePickerType = type },
            onSetCurrentUri = { uri -> currentPhotoUri = uri })
    }
}

/**
 * 头像选择器状态类
 */
class AvatarPickerState(
    private val context: Context,
    private val config: ComposeImagePickerUtils.ImagePickerConfig,
    private val cameraLauncher: ManagedActivityResultLauncher<Uri, Boolean>,
    private val galleryLauncher: ManagedActivityResultLauncher<PickVisualMediaRequest, Uri?>,
    private val photoPickerLauncher: ManagedActivityResultLauncher<Intent, ActivityResult>,
    private val permissionLauncher: ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>,
    val isUploading: Boolean,
    private val onSetPendingType: (ComposeImagePickerUtils.ImagePickerType) -> Unit,
    private val onSetCurrentUri: (Uri) -> Unit
) {

    /**
     * 处理选项选择
     */
    fun handleOptionSelected(index: Int) {
        val type = ComposeImagePickerUtils.getImagePickerType(index, config)
        onSetPendingType(type)

        ComposeImagePickerUtils.handleOptionSelectedWithPermissionContinuation(
            index = index,
            context = context,
            cameraLauncher = cameraLauncher,
            galleryLauncher = galleryLauncher,
            photoPickerLauncher = photoPickerLauncher,
            permissionLauncher = permissionLauncher,
            config = config,
            onUriCreated = onSetCurrentUri,
            onPermissionGranted = {},
            onPermissionDenied = {})
    }

    /**
     * 获取选项列表
     */
    fun getOptions(): List<String> {
        return ComposeImagePickerUtils.getImagePickerOptions(config)
    }
}
