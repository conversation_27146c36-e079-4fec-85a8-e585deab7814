package com.mobile.anchor.app.ui.screens.followfans

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.mobile.anchor.app.data.model.CallHistoryItemBean
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.anchor.AnchorService
import com.mobile.anchor.app.ui.viewmodels.BaseViewModel

class FollowFansViewModel : BaseViewModel() {

    companion object {
        const val TYPE_FOLLOW = "anchor_follow_user" //关注列表
        const val TYPE_FANS = "user_follow_anchor" //粉丝列表
        const val TYPE_BLOCK = "anchor_block_user" //拉黑列表
        const val TYPE_LOVER = "anchor_lover_user" //主播查看付费用户列表
        const val TYPE_FOR_ME = "anchor_for_me_recharge" //主播为我充值用户列表
    }

    private val service: AnchorService by NetDelegates()
    val userList = mutableStateListOf<UserBean>()
    var responseCursor = mutableStateOf("")

    // 刷新和加载状态
    var isRefreshing = mutableStateOf(false)
    var isLoadingMore = mutableStateOf(false)
    var hasMoreData = mutableStateOf(true)

    // 当前列表类型
    var currentType = mutableStateOf(TYPE_FOLLOW)

    /**
     * 刷新数据
     */
    fun refresh(type: String = currentType.value) {
        if (isRefreshing.value) return

        isRefreshing.value = true
        currentType.value = type

        ktHttpRequest {
            try {
                val response = service.getUserList(type, responseCursor.value, 20).await()
                response?.let {
                    userList.clear()
                    responseCursor.value = it.cursor
                    it.records?.let { resList ->
                        userList.addAll(resList)
                        hasMoreData.value = resList.size >= 20
                    }
                }
            } finally {
                isRefreshing.value = false
            }
        }
    }

    /**
     * 加载更多数据
     */
    fun loadMore() {
        if (isLoadingMore.value || !hasMoreData.value) return

        isLoadingMore.value = true

        ktHttpRequest {
            try {
                val response =
                    service.getUserList(currentType.value, responseCursor.value, 20).await()
                response?.let {
                    responseCursor.value = it.cursor
                    it.records?.let { users ->
                        userList.addAll(users)
                        hasMoreData.value = users.size >= 20
                    }
                }
            } finally {
                isLoadingMore.value = false
            }
        }
    }
}