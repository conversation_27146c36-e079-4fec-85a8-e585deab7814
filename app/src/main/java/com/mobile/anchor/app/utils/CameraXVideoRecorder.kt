package com.mobile.anchor.app.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.net.Uri
import android.util.Log
import androidx.camera.core.CameraSelector
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.video.FileOutputOptions
import androidx.camera.video.Quality
import androidx.camera.video.QualitySelector
import androidx.camera.video.Recorder
import androidx.camera.video.Recording
import androidx.camera.video.VideoCapture
import androidx.camera.video.VideoRecordEvent
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.mobile.anchor.app.logger.LogX
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.Timer
import java.util.TimerTask
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * CameraX视频录制工具类
 * 支持前置摄像头录制、暂停/恢复、录制时长统计等功能
 */
class CameraXVideoRecorder(
    private val context: Context, private val lifecycleOwner: LifecycleOwner
) {

    private val TAG = "CameraXVideoRecorder"

    private var cameraProvider: ProcessCameraProvider? = null
    private var preview: Preview? = null
    private var videoCapture: VideoCapture<Recorder>? = null
    private var recording: Recording? = null
    private var cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()

    // 录制状态
    private val _isRecording = MutableStateFlow(false)
    val isRecording: StateFlow<Boolean> = _isRecording.asStateFlow()

    private val _isPaused = MutableStateFlow(false)
    val isPaused: StateFlow<Boolean> = _isPaused.asStateFlow()

    private val _recordingDuration = MutableStateFlow(0)
    val recordingDuration: StateFlow<Int> = _recordingDuration.asStateFlow()

    // 录制时长计时器
    private var recordingTimer: Timer? = null

    /**
     * 检查相机权限
     */
    fun hasPermissions(): Boolean {
        return ContextCompat.checkSelfPermission(
            context, Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED && ContextCompat.checkSelfPermission(
            context, Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 获取所需权限列表
     */
    fun getRequiredPermissions(): Array<String> {
        return arrayOf(
            Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO
        )
    }

    /**
     * 初始化相机预览
     */
    suspend fun setupCamera(previewView: PreviewView) {
        try {
            cameraProvider = ProcessCameraProvider.getInstance(context).get()

            // 预览配置
            preview = Preview.Builder().build().also {
                it.surfaceProvider = previewView.surfaceProvider
            }

//            val qualitySelector = QualitySelector.from(desiredQuality)
            val qualitySelector = QualitySelector.from(Quality.LOWEST)
            // 视频录制配置
            val recorder = Recorder.Builder().setQualitySelector(qualitySelector).build()
            videoCapture = VideoCapture.withOutput(recorder)

            // 选择前置摄像头
            val cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA

            // 绑定用例到相机
            cameraProvider?.unbindAll()
            cameraProvider?.bindToLifecycle(
                lifecycleOwner, cameraSelector, preview, videoCapture
            )

            LogX.d(TAG, "CameraX初始化成功")
        } catch (e: Exception) {
            LogX.e(TAG, Log.getStackTraceString(e))
            throw e
        }
    }

    /**
     * 开始录制
     */
    fun startRecording(
        onRecordingStarted: (Uri) -> Unit,
        onRecordingFinished: (Uri) -> Unit,
        onError: (String) -> Unit
    ) {
        val videoCapture = this.videoCapture ?: run {
            onError("Camera not initialized")
            return
        }

        if (_isRecording.value) {
            LogX.w(TAG, "已在录制中")
            return
        }

        try {
            // 创建视频文件
            val videoFile = createVideoFile()
            val outputOptions = FileOutputOptions.Builder(videoFile).build()

            // 开始录制
            recording = videoCapture.output.prepareRecording(context, outputOptions).apply {
                if (ContextCompat.checkSelfPermission(
                        context, Manifest.permission.RECORD_AUDIO
                    ) == PackageManager.PERMISSION_GRANTED
                ) {
                    withAudioEnabled()
                }
            }.start(ContextCompat.getMainExecutor(context)) { recordEvent ->
                when (recordEvent) {
                    is VideoRecordEvent.Start -> {
                        _isRecording.value = true
                        _recordingDuration.value = 0
                        startTimer()
                        onRecordingStarted(Uri.fromFile(videoFile))
                        LogX.d(TAG, "录制开始")
                    }

                    is VideoRecordEvent.Finalize -> {
                        _isRecording.value = false
                        _isPaused.value = false
                        stopTimer()
                        if (!recordEvent.hasError()) {
                            LogX.d(TAG, "录制完成: ${recordEvent.outputResults.outputUri}")
                            onRecordingFinished(recordEvent.outputResults.outputUri)
                        } else {
                            LogX.e(TAG, "录制失败: ${recordEvent.error} ${recordEvent.cause}")
                            onError("Recording failed: ${recordEvent.error}")
                        }
                    }

                    is VideoRecordEvent.Pause -> {
                        _isPaused.value = true
                        pauseTimer()
                        LogX.d(TAG, "录制暂停")
                    }

                    is VideoRecordEvent.Resume -> {
                        _isPaused.value = false
                        resumeTimer()
                        LogX.d(TAG, "录制恢复")
                    }
                }
            }
        } catch (e: Exception) {
            LogX.e(TAG, Log.getStackTraceString(e))
            onError("Failed to start recording: ${e.message}")
        }
    }

    /**
     * 停止录制
     */
    fun stopRecording() {
        recording?.stop()
        recording = null
        stopTimer()
    }

    /**
     * 暂停录制
     */
    fun pauseRecording() {
        if (_isRecording.value && !_isPaused.value) {
            recording?.pause()
        }
    }

    /**
     * 恢复录制
     */
    fun resumeRecording() {
        if (_isRecording.value && _isPaused.value) {
            recording?.resume()
        }
    }

    /**
     * 创建视频文件
     */
    private fun createVideoFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "FACE_RECORD_${timeStamp}.mp4"
        return File(context.getExternalFilesDir(null), fileName)
    }

    /**
     * 开始计时器
     */
    private fun startTimer() {
        stopTimer()
        recordingTimer = Timer().apply {
            schedule(object : TimerTask() {
                override fun run() {
                    if (_isRecording.value && !_isPaused.value) {
                        _recordingDuration.value = _recordingDuration.value + 1
                    }
                }
            }, 1000, 1000)
        }
    }

    /**
     * 暂停计时器
     */
    private fun pauseTimer() {
        // 计时器继续运行，但不更新时长
    }

    /**
     * 恢复计时器
     */
    private fun resumeTimer() {
        // 计时器继续更新时长
    }

    /**
     * 停止计时器
     */
    private fun stopTimer() {
        recordingTimer?.cancel()
        recordingTimer = null
    }

    /**
     * 释放资源
     */
    fun release() {
        stopRecording()
        cameraProvider?.unbindAll()
        cameraExecutor.shutdown()
    }
}
