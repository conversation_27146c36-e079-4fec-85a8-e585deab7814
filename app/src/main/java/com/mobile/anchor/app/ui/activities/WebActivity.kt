package com.mobile.anchor.app.ui.activities

import androidx.compose.runtime.Composable
import com.mobile.anchor.app.ui.screens.webview.WebViewScreen

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON>cha<PERSON>
 * @date: 2025/6/17 16:31
 * @description :
 */
class WebActivity : ComposeWrapperActivity() {

    private val title: String by lazy {
        intent.getStringExtra("title") ?: ""
    }

    private val url: String by lazy {
        intent.getStringExtra("url") ?: ""
    }

    @Composable
    override fun BuildContent() = WebViewScreen(
        title = title, url = url, onNavigateBack = { finish() })
}