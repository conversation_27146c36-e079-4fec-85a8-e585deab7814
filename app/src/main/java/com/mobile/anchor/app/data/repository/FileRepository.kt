package com.mobile.anchor.app.data.repository

import com.mobile.anchor.app.data.network.ApiResult
import com.mobile.anchor.app.data.network.NetworkClient
import com.mobile.anchor.app.data.service.ApiService
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.FileUploadManager
import com.mobile.anchor.app.manager.FileUploadManager.UploadCredentialBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 文件相关数据仓库
 * 处理文件上传相关的网络请求
 */
class FileRepository {

    private val apiService = NetworkClient.createService(ApiService::class.java)

    /**
     * 获取AWS S3上传链接
     */
    suspend fun getUploadUrl(
        bizType: FileUploadManager.BizType = FileUploadManager.BizType.NORMAL_UPLOAD,
        uploadFileType: FileUploadManager.FileType
    ): ApiResult<UploadCredentialBean> = withContext(Dispatchers.IO) {
        try {
            LogX.d("获取上传链接: bizType=$bizType, uploadFileType=$uploadFileType")
            val response = if (uploadFileType == FileUploadManager.FileType.LOG) {
                apiService.getUploadLogUrl(mapOf("ext" to uploadFileType.extension))
            } else {
                apiService.getUploadUrl(
                    bizType = bizType.value,
                    extension = uploadFileType.extension
                )
            }

            if (response.isSuccess) {
                if (response.data != null) {
                    LogX.d("获取上传链接成功: ${response.data.uploadURL}")
                    ApiResult.Success(response.data)
                } else {
                    LogX.e("获取上传链接失败: ${response.message}")
                    ApiResult.Error(message = response.message)
                }
            } else {
                LogX.e("获取上传链接网络请求失败: ${response.code}")
                ApiResult.Error(message = "Failed to obtain upload link: ${response.code}")
            }
        } catch (e: Exception) {
            LogX.e("获取上传链接异常", e.message)
            ApiResult.Error(exception = e, message = "Abnormal retrieval of upload link: ${e.message}")
        }
    }
}
