package com.mobile.anchor.app.data.model

import androidx.compose.ui.graphics.Color
import com.mobile.anchor.app.ui.theme.BusyColor
import com.mobile.anchor.app.ui.theme.OfflineColor
import com.mobile.anchor.app.ui.theme.OnlineColor

/**
 * 用户在线状态枚举
 */
enum class OnlineStatus(val value: String, val displayName: String, val color: Color) {
    ONLINE("1", "在线", OnlineColor),
    BUSY("2", "忙碌", BusyColor),
    OFFLINE("3", "离线", OfflineColor),
    SILENT("4", "勿扰", BusyColor);
    companion object {
        fun fromValue(value: String?): OnlineStatus {
            return values().find { it.value == value } ?: ONLINE
        }
    }
}