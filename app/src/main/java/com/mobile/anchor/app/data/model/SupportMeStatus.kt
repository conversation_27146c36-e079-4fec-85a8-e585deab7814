package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/8/6 13:47
 * @description :
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class SupportMeStatus(val loverShow: Item) : Parcelable {

    @Parcelize
    @JsonClass(generateAdapter = true)
    data class Item(val isLover: Int) : Parcelable
}
