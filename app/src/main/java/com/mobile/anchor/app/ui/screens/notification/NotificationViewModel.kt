package com.mobile.anchor.app.ui.screens.notification

import androidx.lifecycle.viewModelScope
import com.mobile.anchor.app.data.model.NotificationBean
import com.mobile.anchor.app.data.service.ApiService
import com.mobile.anchor.app.ui.viewmodels.BaseViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 通知页面ViewModel
 */
class NotificationViewModel : BaseViewModel() {

    companion object {
        private const val PAGE_SIZE = 20
    }

    // 通知列表
    private val _notificationList = MutableStateFlow<List<NotificationBean>>(emptyList())
    val notificationList: StateFlow<List<NotificationBean>> = _notificationList.asStateFlow()

    // 刷新状态
    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing.asStateFlow()

    // 加载更多状态
    private val _isLoadingMore = MutableStateFlow(false)
    val isLoadingMore: StateFlow<Boolean> = _isLoadingMore.asStateFlow()

    // 分页相关
    private var cursor: String = ""
    private var hasMore: Boolean = true

    /**
     * 加载通知列表
     */
    fun loadNotificationList(isRefresh: Boolean = false) {
        if (isRefresh) {
            _isRefreshing.value = true
            cursor = ""
            hasMore = true
        } else {
            if (!hasMore || _isLoadingMore.value) return
            _isLoadingMore.value = true
        }


        ktHttpRequest {
            // 模拟API调用 - 实际项目中替换为真实的API调用
            delay(1000) // 模拟网络延迟

            // 模拟数据
            val mockData = generateMockNotifications(isRefresh)

            val newList = if (isRefresh) {
                mockData
            } else {
                _notificationList.value + mockData
            }

            _notificationList.value = newList

            // 更新分页信息
            cursor = if (mockData.isNotEmpty()) mockData.last().id else cursor
            hasMore = mockData.size >= PAGE_SIZE

            if (isRefresh) {
                _isRefreshing.value = false
            } else {
                _isLoadingMore.value = false
            }

            // 检查空状态
            if (newList.isEmpty() && isRefresh) {
                setupEmptyState()
            }
        }

        // 在请求完成后检查是否为空状态
        viewModelScope.launch {
            delay(500) // 等待状态更新
            val currentList = _notificationList.value
            if (currentList.isEmpty() && isRefresh) {
                setupEmptyState()
            }
        }
    }

    /**
     * 刷新通知列表
     */
    fun refreshNotificationList() {
        loadNotificationList(isRefresh = true)
    }

    /**
     * 加载更多通知
     */
    fun loadMoreNotifications() {
        if (hasMore && !_isLoadingMore.value) {
            loadNotificationList(isRefresh = false)
        }
    }

    /**
     * 是否可以加载更多
     */
    fun canLoadMore(): Boolean = hasMore

    /**
     * 生成模拟通知数据
     */
    private fun generateMockNotifications(isRefresh: Boolean): List<NotificationBean> {
        val startIndex = if (isRefresh) 0 else _notificationList.value.size

        return (startIndex until startIndex + PAGE_SIZE).map { index ->
            NotificationBean(
                id = "notification_$index",
                title = "Notification ${index + 1}",
                content = "This is notification content ${index + 1}",
                avatar = "https://api.dicebear.com/7.x/micah/png?seed=notification$index",
                nickname = if (index % 3 == 0) "System" else "User ${index + 1}",
                createTime = "2024-01-${String.format("%02d", (index % 28) + 1)} 10:${
                    String.format(
                        "%02d",
                        index % 60
                    )
                }",
                isRead = index % 4 != 0, // 每4个有一个未读
                type = if (index % 3 == 0) 0 else 1
            )
        }
    }

    /**
     * 标记通知为已读
     */
    fun markAsRead(notificationId: String) {
        val currentList = _notificationList.value.toMutableList()
        val index = currentList.indexOfFirst { it.id == notificationId }
        if (index != -1) {
            currentList[index] = currentList[index].copy(isRead = true)
            _notificationList.value = currentList
        }
    }

    /**
     * 标记所有通知为已读
     */
    fun markAllAsRead() {
        val currentList = _notificationList.value.map { it.copy(isRead = true) }
        _notificationList.value = currentList
    }
}
