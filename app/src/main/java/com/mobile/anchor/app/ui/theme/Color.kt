package com.mobile.anchor.app.ui.theme

import androidx.compose.ui.graphics.Color


// 主题颜色
val Primary = Color(0xFF9F2AF8)
val PrimaryDark = Color(0xFF7A1FBF)
val PrimaryLight = Color(0xFFB855FA)
val Accent = Color(0xFF9F2AF8)
val Background = Color(0xFF10081F)
val Surface = Color(0xFF201831)
val OnPrimary = Color(0xFFFFFFFF)
val OnBackground = Color(0xFF201831)
val OnSurface = Color(0xFFFFFFFF)

val Red = Color(0XFFF53D3D)

val Transparent = Color(0x00000000)

// 在线状态颜色
val OnlineColor = Color(0xFF21C76E)  // 在线 - 绿色
val BusyColor = Color(0xFFFE4918)    // 忙碌 - 红色
val OfflineColor = Color(0xFF666666) // 离线 - 灰色

// 其他功能颜色
val ErrorColor = Color(0xFFFF3B30)   // 错误/警告颜色
val SuccessColor = Color(0xFF4CD964) // 成功颜色
val WarningColor = Color(0xFFFF9500) // 警告颜色
val DisabledColor = Color(0xFF666666) // 禁用颜色