package com.mobile.anchor.app.utils

import android.util.Log

/**
 * 日志工具类
 * 统一管理应用的日志输出，支持不同级别的日志记录
 */
object LogUtils {
    
    private const val DEFAULT_TAG = "Anchor"
    private val isDebug = true // BuildConfig.DEBUG
    
    /**
     * Verbose 级别日志
     */
    fun v(message: String, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.v(tag, message)
        }
    }
    
    fun v(message: String, throwable: Throwable, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.v(tag, message, throwable)
        }
    }
    
    /**
     * Debug 级别日志
     */
    fun d(message: String, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.d(tag, message)
        }
    }
    
    fun d(message: String, throwable: Throwable, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.d(tag, message, throwable)
        }
    }
    
    /**
     * Info 级别日志
     */
    fun i(message: String, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.i(tag, message)
        }
    }
    
    fun i(message: String, throwable: Throwable, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.i(tag, message, throwable)
        }
    }
    
    /**
     * Warning 级别日志
     */
    fun w(message: String, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.w(tag, message)
        }
    }
    
    fun w(message: String, throwable: Throwable, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.w(tag, message, throwable)
        }
    }
    
    fun w(throwable: Throwable, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.w(tag, throwable)
        }
    }
    
    /**
     * Error 级别日志
     */
    fun e(message: String, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.e(tag, message)
        }
    }
    
    fun e(message: String, throwable: Throwable?, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.e(tag, message, throwable)
        }
    }
    
    fun e(throwable: Throwable?, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            Log.e(tag, "Error occurred", throwable)
        }
    }
    
    /**
     * 网络请求日志
     */
    fun network(message: String) {
        d(message, "Network")
    }
    
    /**
     * 数据库操作日志
     */
    fun database(message: String) {
        d(message, "Database")
    }
    
    /**
     * UI 操作日志
     */
    fun ui(message: String) {
        d(message, "UI")
    }
    
    /**
     * 性能监控日志
     */
    fun performance(message: String) {
        d(message, "Performance")
    }
    
    /**
     * 打印方法调用栈
     */
    fun printStackTrace(tag: String = DEFAULT_TAG) {
        if (isDebug) {
            val stackTrace = Thread.currentThread().stackTrace
            val sb = StringBuilder()
            sb.append("Stack trace:\n")
            for (i in 3 until stackTrace.size.coerceAtMost(10)) {
                val element = stackTrace[i]
                sb.append("  at ${element.className}.${element.methodName}(${element.fileName}:${element.lineNumber})\n")
            }
            d(sb.toString(), tag)
        }
    }
    
    /**
     * 格式化 JSON 日志输出
     */
    fun json(json: String, tag: String = DEFAULT_TAG) {
        if (isDebug) {
            try {
                // 这里可以添加 JSON 格式化逻辑
                d("JSON: $json", tag)
            } catch (e: Exception) {
                e("Invalid JSON: $json", e, tag)
            }
        }
    }
}
