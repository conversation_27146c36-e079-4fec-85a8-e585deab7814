package com.mobile.anchor.app.data.model

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/6/14 11:37
 * @description :
 */
data class VersionBean(
    val show: Boolean = false,
    val content: String = "",
    val dialog_id: String = "",
    val update_type: Int = 0/*0不更新 1提示更新 2强制更新*/,
    val update_url: String = ""/*更新地址*/,
    val update_tips: String = ""/*更新信息*/,
    val version_name: String = "",/*新版本号*/
    val version_code: Int = 0,/*新版本代码*/
    val file_size: Long = 0/*文件大小，字节*/
) {
    val isUpdateRequired: <PERSON>olean get() = update_type == 2

    val isUpdateSuggested: Boolean get() = update_type == 1

    val hasUpdate: Boolean get() = isUpdateSuggested || isUpdateRequired

    val fileSizeFormatted: String get() {
        return when {
            file_size < 1024 -> "${file_size}B"
            file_size < 1024 * 1024 -> "${file_size / 1024}KB"
            else -> String.format("%.1fMB", file_size / (1024.0 * 1024.0))
        }
    }
}
