package com.mobile.anchor.app.ui.conversation

import android.os.Bundle
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.bdc.android.library.mvi.observeEvent
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.viewmodels.UserRequestEvent
import com.mobile.anchor.app.ui.viewmodels.UserViewModel
import io.rong.imkit.conversationlist.ConversationListFragment
import io.rong.imkit.conversationlist.viewmodel.ConversationListViewModel
import io.rong.imkit.widget.refresh.constant.RefreshState
import kotlin.collections.toSet
import androidx.core.view.isGone


/**
 * Author:Lxf
 * Create on:2024/8/5
 * Description:
 */
class MyConversionListFragment : ConversationListFragment() {
    companion object {
        private const val TAG = "MyConversionListFragment"
    }

   private lateinit var userViewModel: UserViewModel
   private val cachedAnchorIds = mutableSetOf<String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun subscribeUi() {
        super.subscribeUi()
        mConversationListViewModel =
            (ViewModelProvider(this))[ConversationListViewModel::class.java]
        userViewModel = (ViewModelProvider(this))[UserViewModel::class.java]
        mConversationListViewModel.getConversationList(false, false, 0L)
        mConversationListViewModel.conversationListLiveData.observe(
            viewLifecycleOwner
        ) { conversationList ->
            if (mNewState == 0) {
                mAdapter.setDataCollection(conversationList)
                val anchorIdsToFetch = mutableListOf<String>()
                conversationList.forEach { item ->
                    val targetId = item.mCore.targetId
                    if (!cachedAnchorIds.contains(targetId)) {
                        anchorIdsToFetch.add(targetId)
                        cachedAnchorIds.add(targetId) // Add to cache immediately to avoid duplicate fetches
                    }
                }
                if (anchorIdsToFetch.isNotEmpty()) {
                    LogX.e("Fetching anchor info for IDs: $anchorIdsToFetch")
                    userViewModel.fetchUserList(anchorIdsToFetch)
                }
            } else {
                delayRefresh = true
            }
        }
        mConversationListViewModel.noticeContentLiveData.observe(
            viewLifecycleOwner
        ) {
            if (mNoticeContainerView.isGone) {
                mHandler.postDelayed(Runnable {
                    updateNoticeContent(mConversationListViewModel.noticeContentLiveData.value)
                }, 4000L)
            } else {
                updateNoticeContent(it)
            }
        }
        mConversationListViewModel.refreshEventLiveData.observe(
            viewLifecycleOwner
        ) {
            if (it.state == RefreshState.LoadFinish) {
                if (mRefreshLayout != null) {
                    mRefreshLayout.finishLoadMore()
                } else {
                    LogX.e("onChanged finishLoadMore error")
                }
            } else if (it.state == RefreshState.RefreshFinish) {
                if (mRefreshLayout != null) {
                    mRefreshLayout.finishRefresh()
                } else {
                    LogX.e("onChanged finishRefresh error")
                }
            }
        }

        userViewModel.pageEvents.observeEvent(viewLifecycleOwner) { event ->
            when (event) {
                is UserRequestEvent.FetchUserListFailed -> {
                    LogX.e("FetchAnchorListFailed: ${event.msg}, IDs: ${event.anchorIds}")
                    // If fetching fails, remove these IDs from cache so they can be retried
                    cachedAnchorIds.removeAll(event.anchorIds.toSet())
                }
                else -> {
                }
            }
        }
    }
}