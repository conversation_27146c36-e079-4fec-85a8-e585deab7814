package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/6/13 11:13
 * @description :
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class BankBean(
    val id: Int = 0,
    val name: String = "",
    val code: String = "",
    val bank_id: Int = 0,
    val real_name: String = "",
    val card_num: String = "",
    val enable: Boolean = true
) :
    Parcelable
