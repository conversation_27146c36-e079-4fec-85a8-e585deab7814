package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.mobile.anchor.app.R

/**
 * 异步图片加载组件
 * 基于 Coil 库的封装，支持加载状态、错误处理、占位图等功能
 */
@Composable
fun AsyncImageComponent(
    imageUrl: String?,
    modifier: Modifier = Modifier,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Crop,
    shape: Shape? = null,
    placeholder: Int? = null,
    error: Int? = null,
    showLoading: Boolean = true,
    showError: Boolean = true,
    crossfade: Boolean = true,
    onLoading: (() -> Unit)? = null,
    onSuccess: (() -> Unit)? = null,
    onError: (() -> Unit)? = null
) {
    val context = LocalContext.current

    AsyncImage(
        model = ImageRequest.Builder(context).data(imageUrl).crossfade(crossfade).crossfade(200)
            .diskCachePolicy(CachePolicy.ENABLED).build(),
        contentDescription = contentDescription,
        modifier = modifier.let { mod ->
            shape?.let { mod.clip(it) } ?: mod
        },
        contentScale = contentScale,
        placeholder = placeholder?.let { painterResource(it) },
        error = error?.let { painterResource(it) },
        onLoading = onLoading?.let { callback -> { callback() } },
        onSuccess = onSuccess?.let { callback -> { callback() } },
        onError = onError?.let { callback -> { callback() } })
}

/**
 * 圆形头像组件
 */
@Composable
fun CircleAvatar(
    imageUrl: String?,
    size: Dp = 40.dp,
    modifier: Modifier = Modifier,
    contentDescription: String? = "头像",
    placeholder: Int? = R.mipmap.ic_default_avatar,
    error: Int? = R.mipmap.ic_default_avatar
) {
    RoundedImage(
        imageUrl = imageUrl,
        modifier = modifier.size(size),
        contentDescription = contentDescription,
        cornerRadius = 50.dp,
        placeholder = placeholder,
        error = error
    )
}

/**
 * 圆角图片组件
 */
@Composable
fun RoundedImage(
    imageUrl: String?,
    modifier: Modifier = Modifier,
    cornerRadius: Dp = 8.dp,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Crop,
    placeholder: Int? = R.mipmap.ic_default_avatar,
    error: Int? = R.mipmap.ic_default_avatar
) {
    AsyncImageComponent(
        imageUrl = imageUrl,
        modifier = modifier,
        contentDescription = contentDescription,
        contentScale = contentScale,
        showLoading = true,
        showError = false,
        shape = RoundedCornerShape(cornerRadius),
        placeholder = placeholder,
        error = error
    )
}

/**
 * 带加载状态的图片组件
 */
@Composable
fun ImageWithLoading(
    imageUrl: String?,
    modifier: Modifier = Modifier,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Crop,
    shape: Shape? = null,
    placeholder: Int? = R.mipmap.ic_default_avatar,
    error: Int? = R.mipmap.ic_default_avatar
) {
    var isLoading by remember { mutableStateOf(true) }
    var isError by remember { mutableStateOf(false) }

    Box(modifier = modifier) {
        AsyncImageComponent(
            imageUrl = imageUrl,
            modifier = Modifier.fillMaxSize(),
            contentDescription = contentDescription,
            contentScale = contentScale,
            shape = shape,
            placeholder = placeholder,
            error = error,
            onLoading = {
                isLoading = true
                isError = false
            },
            onSuccess = {
                isLoading = false
                isError = false
            },
            onError = {
                isLoading = false
                isError = true
            })

        // 加载指示器
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Gray.copy(alpha = 0.3f)),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp), strokeWidth = 2.dp
                )
            }
        }

        // 错误状态
        if (isError) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Gray.copy(alpha = 0.3f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(android.R.drawable.ic_menu_report_image),
                    contentDescription = "加载失败",
                    tint = Color.Gray
                )
            }
        }
    }
}
