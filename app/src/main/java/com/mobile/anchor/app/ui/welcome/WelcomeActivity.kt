package com.mobile.anchor.app.ui.welcome

import android.Manifest
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.splashscreen.SplashScreen
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.fragment.app.FragmentActivity
import com.bdc.android.library.extension.jumpThenFinish
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.XXPermissions
import com.mobile.anchor.app.MainActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.network.AuthManager
import com.mobile.anchor.app.lifecycle.AppLifecycleObserver
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.activities.LoginActivity
import com.mobile.anchor.app.ui.popup.ComposePopup

/**
 * 作者：Lxf
 * 创建日期：2024/8/12 14:58
 * 描述：
 */
class WelcomeActivity : FragmentActivity() {
    var splashScreen: SplashScreen? = null
    private var keepSplashScreen = true

    override fun onCreate(savedInstanceState: Bundle?) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            splashScreen = installSplashScreen()
            splashScreen?.setKeepOnScreenCondition { keepSplashScreen }
        }
        super.onCreate(savedInstanceState)

        requestPermission(onGrantedCallBack = {
            gotoActivity()
        }) {
            finish()
        }
    }

    private fun gotoActivity() {
        checkFloatingWindowPermission {
            if (AuthManager.isLoggedIn()) {
                jumpThenFinish(MainActivity::class.java)
            } else {
                jumpThenFinish(LoginActivity::class.java)
            }
        }
    }

    private fun requestPermission(
        onGrantedCallBack: () -> Unit, onDenied: () -> Unit
    ) {
        val permissionList = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(
                Manifest.permission.READ_PHONE_STATE,
                Manifest.permission.CAMERA,
//                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.POST_NOTIFICATIONS,
            )
        } else {
            arrayOf(
                Manifest.permission.READ_PHONE_STATE,
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.CAMERA,
            )
        }

        XXPermissions.with(this).permission(permissionList).request(object : OnPermissionCallback {
            override fun onGranted(permissions: MutableList<String>, all: Boolean) {
                if (all) {
                    onGrantedCallBack.invoke()
                } else {
//                        ToastUtil.show(getString(R.string.tip_video_permission_request))
                }
            }

            override fun onDenied(permissions: MutableList<String>, never: Boolean) {
//                    ToastUtil.show(getString(R.string.tip_video_permission_denied))
                if (never) {
                    // 如果是被永久拒绝就跳转到应用权限系统设置页面
                    XXPermissions.startPermissionActivity(this@WelcomeActivity, permissions)
                } else {
                    onDenied.invoke()
                }
            }
        })
    }


    /**
     * 检查悬浮窗权限
     */
    private fun checkFloatingWindowPermission(block: () -> Unit) {
        if (!AppLifecycleObserver.hasOverlayPermission(this)) {

            // 释放SplashScreen以便显示弹框
            keepSplashScreen = false

            // 确保Activity没有被销毁
            if (!isFinishing && !isDestroyed) {
                ComposePopup.showConfirmDialog(
                    this,
                    title = getString(R.string.reminder),
                    content = getString(R.string.please_grant_floating_window_permission),
                    showCancelButton = false,
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    onConfirm = {
                        LogX.d("用户确认授权悬浮窗权限")
                        requestOverlayPermissionLauncher.launch(Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION))
                    })
            } else {
                LogX.e("Activity已销毁，无法显示弹框")
            }
        } else {
            LogX.d("已有悬浮窗权限")
            keepSplashScreen = false
            block.invoke()
        }
    }

    var requestOverlayPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result: ActivityResult? ->
        val b = Settings.canDrawOverlays(this)
        if (b) {
            gotoActivity()
        } else {
            LogX.d("悬浮窗权限被拒绝")
            checkFloatingWindowPermission {
                gotoActivity()
            }
        }
    }
}
