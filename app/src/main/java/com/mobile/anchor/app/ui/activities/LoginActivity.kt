package com.mobile.anchor.app.ui.activities

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.bdc.android.library.extension.jump
import com.bdc.android.library.extension.jumpThenFinish
import com.mobile.anchor.app.MainActivity
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.navigation.Screen
import com.mobile.anchor.app.ui.screens.login.EmailLoginScreen
import com.mobile.anchor.app.ui.screens.login.EmailVerificationScreen
import com.mobile.anchor.app.ui.screens.login.LoginScreen
import com.mobile.anchor.app.ui.screens.login.LoginViewModel
import com.mobile.anchor.app.ui.theme.AnchorTheme

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/13 17:03
 * @description :
 */
class LoginActivity : ComposeWrapperActivity() {

    private val viewModel: LoginViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            AnchorTheme {
                val navController = rememberNavController()
                val loginUiState by viewModel.loginUiState.collectAsStateWithLifecycle()

                // 监听 Google 登录成功
                LaunchedEffect(loginUiState.googleLoginSuccess) {
                    if (loginUiState.googleLoginSuccess) {
                        LogX.d("MainActivity: 检测到 Google 登录成功")
                    }
                }

                NavHost(
                    navController = navController,
                    startDestination = Screen.Login.route,
                ) {
                    // 登录选择页面
                    composable(Screen.Login.route) {
                        LoginScreen(
                            viewModel = viewModel,
                            navController = navController,
                        )
                    }

                    // 邮箱登录页面
                    composable(Screen.EmailLogin.route) {
                        EmailLoginScreen(
                            viewModel = viewModel, // 传递共享的ViewModel
                            onNavigateToVerification = {
                                val email = viewModel.loginUiState.value.email
                                navController.navigate(Screen.EmailVerification.createRoute(email))
                            }, onNavigateBack = {
                                navController.popBackStack()
                            })
                    }

                    // 邮箱验证页面
                    composable(Screen.EmailVerification.route) { backStackEntry ->
                        val email = backStackEntry.arguments?.getString("email") ?: ""
                        // 设置邮箱到ViewModel，确保邮箱正确传递
                        LaunchedEffect(email) {
                            if (email.isNotEmpty()) {
                                viewModel.setEmailForVerification(email)
                            }
                        }

                        EmailVerificationScreen(viewModel = viewModel, onLoginSuccess = {
                            if (DataStoreManager.getUserObject()?.isRegistration == true) {
                                jumpThenFinish(ProfileBuildActivity::class.java)
                            } else {
                                jumpThenFinish(MainActivity::class.java)
                            }
                        }, onNavigateBack = {
                            navController.popBackStack()
                        })
                    }
                }
            }
        }
    }
}