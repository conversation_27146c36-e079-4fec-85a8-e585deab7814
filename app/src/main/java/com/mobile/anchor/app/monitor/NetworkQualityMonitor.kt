package com.mobile.anchor.app.monitor

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.graphics.Color
import android.media.RingtoneManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.telephony.TelephonyManager
import android.util.Log
import androidx.core.app.NotificationCompat
import com.mobile.anchor.app.BuildConfig
import com.mobile.anchor.app.R
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.monitor.NotificationManager.showNotification
import io.rong.imkit.RongIM
import io.rong.imlib.RongIMClient
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/5/22 15:40
 * @description: 增强版网络环境质量监控
 */
class NetworkQualityMonitor private constructor(private val context: Context) {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var monitorJob: Job? = null
    private var checkIntervalMillis: Long = 60_000L // 默认每 60 秒检测一次

    private val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private val telephonyManager =
        context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

    // 网络质量检测配置
    private val networkConfig = NetworkQualityConfig()

    // 网络状态历史记录
    private val networkHistory = mutableListOf<NetworkQualityResult>()
    private val maxHistorySize = 10

    // 上一次的网络类型，用于检测网络切换
    private var lastNetworkType: NetworkType? = null

    // 用户偏好设置
    private var notifyOnNetworkTypeChange = true  // 网络类型变化时通知
    private var notifyOnVpnWithoutWifi = true     // VPN但无WiFi时通知

    companion object {
        private const val TAG = "NetworkQualityMonitor"

        @Volatile
        private var INSTANCE: NetworkQualityMonitor? = null

        fun getInstance(context: Context): NetworkQualityMonitor {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NetworkQualityMonitor(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    /**
     * 网络质量配置类
     */
    data class NetworkQualityConfig(
        val pingTimeout: Int = 3000,
        val httpTimeout: Int = 5000,
        val poorNetworkThreshold: Long = 500L,  // 500ms - 差网络
        val weakNetworkThreshold: Long = 1000L, // 1秒 - 很差网络
        val testUrls: List<String> = listOf(
            "https://www.google.com",
            BuildConfig.HOST,
            "http://www.msftconnecttest.com/connecttest.txt"
        ),
        val pingHosts: List<String> = listOf(
            "*******", "***************", "google.com", BuildConfig.HOST
        )
    )

    /**
     * 网络质量检测结果
     */
    data class NetworkQualityResult(
        val timestamp: Long,
        val networkType: NetworkType,
        val isConnected: Boolean,
        val pingLatency: Long, // -1表示ping失败
        val httpLatency: Long, // -1表示HTTP测试失败
        val qualityLevel: NetworkQuality,
        val signalStrength: Int = -1, // 移动网络信号强度
        val linkSpeed: Int = -1 // WiFi连接速度 Mbps
    )

    enum class NetworkType {
        WIFI, CELLULAR_5G, CELLULAR_4G, CELLULAR_3G, CELLULAR_2G, ETHERNET, VPN, UNKNOWN, NONE
    }

    enum class NetworkQuality {
        EXCELLENT, GOOD, FAIR, POOR, VERY_POOR, NO_CONNECTION
    }

    private val connectionStatusListener = RongIMClient.ConnectionStatusListener { status ->
        when (status) {
            RongIMClient.ConnectionStatusListener.ConnectionStatus.NETWORK_UNAVAILABLE, RongIMClient.ConnectionStatusListener.ConnectionStatus.UNCONNECTED, RongIMClient.ConnectionStatusListener.ConnectionStatus.SUSPEND, RongIMClient.ConnectionStatusListener.ConnectionStatus.TIMEOUT -> {
                LogX.w(TAG, "RongIM connection issue: $status")
                // 检查网络是否可用，如果网络可用但融云未连接，则尝试重连
                if (isNetworkAvailable()) {
                    reconnectRongIM()
                }
            }

            else -> {
                LogX.d(TAG, "RongIM connection status: $status")
            }
        }
    }

    fun startMonitoring(intervalMillis: Long = checkIntervalMillis) {
        checkIntervalMillis = intervalMillis
        if (monitorJob?.isActive == true) {
            monitorJob?.cancel()
        }

        RongIM.setConnectionStatusListener(connectionStatusListener)
//        createNotificationChannel()

        monitorJob = scope.launch {
            // 立即执行第一次检测
            LogX.i(TAG, "Performing initial network check...")
            try {
                val initialResult = performNetworkQualityCheck()
                handleNetworkQualityResult(initialResult)

                // 检查融云连接状态
                if (initialResult.isConnected) {
                    checkRongIMConnection()
                }
            } catch (e: Exception) {
                LogX.e(TAG, "Error in initial network check: ${Log.getStackTraceString(e)}")
            }

            // 然后开始定期检测
            while (isActive) {
                try {
                    delay(checkIntervalMillis)

                    val result = performNetworkQualityCheck()
                    handleNetworkQualityResult(result)

                    // 检查融云连接状态
                    if (result.isConnected) {
                        checkRongIMConnection()
                    }
                } catch (e: Exception) {
                    LogX.e(TAG, "Error in periodic network check: ${Log.getStackTraceString(e)}")
                }
            }
        }
    }

    fun stopMonitoring() {
        monitorJob?.cancel()
    }

    /**
     * 执行综合网络质量检测
     */
    private suspend fun performNetworkQualityCheck(): NetworkQualityResult {
        val timestamp = System.currentTimeMillis()
        val networkType = getDetailedNetworkType()
        val isConnected = isNetworkAvailable()

        if (!isConnected) {
            return NetworkQualityResult(
                timestamp = timestamp,
                networkType = NetworkType.NONE,
                isConnected = false,
                pingLatency = -1,
                httpLatency = -1,
                qualityLevel = NetworkQuality.NO_CONNECTION
            )
        }

        // 并行执行ping和HTTP测试
        val pingLatency = measureBestPingLatency()
        val httpLatency = measureBestHttpLatency()
        val qualityLevel = calculateNetworkQuality(networkType, pingLatency, httpLatency)

        // 增强日志记录，便于调试
        LogX.d(
            TAG,
            "Network check - Type: $networkType, Connected: $isConnected, " + "Ping: ${pingLatency}ms, HTTP: ${httpLatency}ms, Quality: $qualityLevel"
        )

        return NetworkQualityResult(
            timestamp = timestamp,
            networkType = networkType,
            isConnected = true,
            pingLatency = pingLatency,
            httpLatency = httpLatency,
            qualityLevel = qualityLevel,
            signalStrength = getSignalStrength(),
            linkSpeed = getLinkSpeed()
        )
    }

    /**
     * 获取详细的网络类型
     */
    private fun getDetailedNetworkType(): NetworkType {
        val network = connectivityManager.activeNetwork ?: return NetworkType.NONE
        val capabilities =
            connectivityManager.getNetworkCapabilities(network) ?: return NetworkType.NONE

        // 详细记录所有网络传输类型
        val transports = mutableListOf<String>()
        if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) transports.add("WIFI")
        if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) transports.add("CELLULAR")
        if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)) transports.add("ETHERNET")
        if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN)) transports.add("VPN")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (capabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH)) transports.add("BLUETOOTH")
        }

        LogX.d(TAG, "Network transports detected: ${transports.joinToString(", ")}")

        // 增强VPN检测逻辑
        val hasVpn = capabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN)
        val hasWifi = capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        val hasCellular = capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)

        // 检测VPN连接 - 优先检测VPN
        if (hasVpn) {
            LogX.i(TAG, "VPN connection detected via TRANSPORT_VPN")
            return NetworkType.VPN
        }

        // 额外的VPN检测方法 - 检查是否同时有多种传输类型（可能是VPN）
        if ((hasWifi && hasCellular) || transports.size > 1) {
            LogX.d(TAG, "Multiple transports detected, might be VPN: $transports")
            // 这里可以进一步检测，但先按原逻辑处理
        }

        return when {
            hasWifi -> {
                LogX.d(TAG, "WiFi connection detected")
                NetworkType.WIFI
            }

            hasCellular -> {
                LogX.d(TAG, "Cellular connection detected")
                getCellularNetworkType()
            }

            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> {
                LogX.d(TAG, "Ethernet connection detected")
                NetworkType.ETHERNET
            }

            else -> {
                LogX.d(TAG, "Unknown network type detected")
                NetworkType.UNKNOWN
            }
        }
    }

    /**
     * 获取移动网络类型
     */
    private fun getCellularNetworkType(): NetworkType {
        return try {
            when (telephonyManager.networkType) {
                TelephonyManager.NETWORK_TYPE_GPRS, TelephonyManager.NETWORK_TYPE_EDGE, TelephonyManager.NETWORK_TYPE_CDMA, TelephonyManager.NETWORK_TYPE_1xRTT, TelephonyManager.NETWORK_TYPE_IDEN -> NetworkType.CELLULAR_2G

                TelephonyManager.NETWORK_TYPE_UMTS, TelephonyManager.NETWORK_TYPE_EVDO_0, TelephonyManager.NETWORK_TYPE_EVDO_A, TelephonyManager.NETWORK_TYPE_HSDPA, TelephonyManager.NETWORK_TYPE_HSUPA, TelephonyManager.NETWORK_TYPE_HSPA, TelephonyManager.NETWORK_TYPE_EVDO_B, TelephonyManager.NETWORK_TYPE_EHRPD, TelephonyManager.NETWORK_TYPE_HSPAP -> NetworkType.CELLULAR_3G

                TelephonyManager.NETWORK_TYPE_LTE -> NetworkType.CELLULAR_4G
                TelephonyManager.NETWORK_TYPE_NR -> NetworkType.CELLULAR_5G
                else -> NetworkType.CELLULAR_4G // 默认4G
            }
        } catch (e: Exception) {
            LogX.e(TAG, "Failed to get cellular network type: ${e.message}")
            NetworkType.CELLULAR_4G
        }
    }

    /**
     * 测试多个主机的ping延迟，返回最佳结果
     */
    private suspend fun measureBestPingLatency(): Long = withContext(Dispatchers.IO) {
        var bestLatency = Long.MAX_VALUE

        networkConfig.pingHosts.forEach { host ->
            val latency = ping(host)
            if (latency != null && latency < bestLatency) {
                bestLatency = latency
            }
        }

        if (bestLatency == Long.MAX_VALUE) -1L else bestLatency
    }

    /**
     * 测试多个URL的HTTP延迟，返回最佳结果
     */
    private suspend fun measureBestHttpLatency(): Long = withContext(Dispatchers.IO) {
        var bestLatency = Long.MAX_VALUE

        networkConfig.testUrls.forEach { url ->
            val latency = httpConnectTest(url)
            if (latency != null && latency < bestLatency) {
                bestLatency = latency
            }
        }

        if (bestLatency == Long.MAX_VALUE) -1L else bestLatency
    }

    /**
     * HTTP连接测试
     */
    private suspend fun httpConnectTest(urlString: String): Long? = withContext(Dispatchers.IO) {
        try {
            val startTime = System.currentTimeMillis()
            val url = URL(urlString)
            val connection = url.openConnection() as HttpURLConnection
            connection.connectTimeout = networkConfig.httpTimeout
            connection.readTimeout = networkConfig.httpTimeout
            connection.requestMethod = "HEAD"
            connection.useCaches = false

            val responseCode = connection.responseCode
            val endTime = System.currentTimeMillis()
            connection.disconnect()

            if (responseCode in 200..299) {
                endTime - startTime
            } else {
                null
            }
        } catch (e: IOException) {
            LogX.d(TAG, "HTTP test failed for $urlString: ${e.message}")
            null
        }
    }

    /**
     * 计算网络质量等级
     */
    private fun calculateNetworkQuality(
        networkType: NetworkType, pingLatency: Long, httpLatency: Long
    ): NetworkQuality {
        // 基于网络类型的基础判断
        val baseQuality = when (networkType) {
            NetworkType.CELLULAR_2G -> NetworkQuality.POOR
            NetworkType.CELLULAR_3G -> NetworkQuality.FAIR
            NetworkType.NONE -> NetworkQuality.NO_CONNECTION
            else -> NetworkQuality.GOOD
        }

        // 如果ping和HTTP都失败，网络质量很差
        if (pingLatency == -1L && httpLatency == -1L) {
            return NetworkQuality.VERY_POOR
        }

        // 使用最好的延迟结果进行判断
        val bestLatency = when {
            pingLatency != -1L && httpLatency != -1L -> minOf(pingLatency, httpLatency)
            pingLatency != -1L -> pingLatency
            httpLatency != -1L -> httpLatency
            else -> Long.MAX_VALUE
        }

        val latencyQuality = when {
            bestLatency <= 100 -> NetworkQuality.EXCELLENT
            bestLatency <= 300 -> NetworkQuality.GOOD
            bestLatency <= networkConfig.poorNetworkThreshold -> NetworkQuality.FAIR  // <= 500ms
            bestLatency <= networkConfig.weakNetworkThreshold -> NetworkQuality.POOR  // <= 1000ms
            else -> NetworkQuality.VERY_POOR  // > 1000ms
        }

        // 返回基础质量和延迟质量中较差的一个
        return if (baseQuality.ordinal > latencyQuality.ordinal) baseQuality else latencyQuality
    }

    /**
     * 获取信号强度（移动网络）
     */
    private fun getSignalStrength(): Int {
        return try {
            // 这里需要根据实际需求实现信号强度获取
            // 可能需要额外的权限和API调用
            -1
        } catch (e: Exception) {
            -1
        }
    }

    /**
     * 获取WiFi连接速度
     */
    private fun getLinkSpeed(): Int {
        return try {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            if (capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    capabilities.linkUpstreamBandwidthKbps / 1000 // 转换为Mbps
                } else {
                    -1
                }
            } else {
                -1
            }
        } catch (e: Exception) {
            -1
        }
    }

    /**
     * 处理网络质量检测结果
     */
    private fun handleNetworkQualityResult(result: NetworkQualityResult) {
        // 添加到历史记录
        addToHistory(result)

        // 检查网络类型变化
        checkNetworkTypeChange(result.networkType)

        // 检查特殊网络情况
        checkSpecialNetworkConditions(result)

        // 移除冷却时间限制，只要检测到网络问题就立即通知
        when (result.qualityLevel) {
            NetworkQuality.NO_CONNECTION -> {
                showNotification(
                    context.getString(R.string.network_unavailable),
                    context.getString(R.string.please_check_your_network_connection),
                    NotificationCompat.PRIORITY_HIGH
                )
                LogX.i(TAG, "Notification shown: NO_CONNECTION")
            }

            NetworkQuality.VERY_POOR -> {
                val message = buildNetworkQualityMessage(result)
                showNotification(
                    context.getString(R.string.poor_network_quality),
                    context.getString(R.string.network_quality_very_poor_message, message),
                    NotificationCompat.PRIORITY_HIGH
                )
                LogX.i(TAG, "Notification shown: VERY_POOR")
            }

            NetworkQuality.POOR -> {
                val message = buildNetworkQualityMessage(result)
                showNotification(
                    context.getString(R.string.poor_network_quality), context.getString(
                        R.string.network_quality_poor_message, message
                    ), NotificationCompat.PRIORITY_HIGH
                )
                LogX.i(TAG, "Notification shown: POOR")
            }

            NetworkQuality.FAIR -> {
                val message = buildNetworkQualityMessage(result)
                showNotification(
                    context.getString(R.string.network_quality_fair), context.getString(
                        R.string.network_quality_fair_message, message
                    ), NotificationCompat.PRIORITY_DEFAULT
                )
                LogX.i(TAG, "Notification shown: FAIR")
            }

            else -> {
                LogX.d(
                    TAG, "Network quality is good, no notification needed: ${result.qualityLevel}"
                )
            }
        }
    }

    /**
     * 构建网络质量消息
     */
    private fun buildNetworkQualityMessage(result: NetworkQualityResult): String {
        val sb = StringBuilder()
        sb.append(
            context.getString(
                R.string.network_type_label, getNetworkTypeDescription(result.networkType)
            )
        )

        // 显示延迟信息，包括失败的情况
        if (result.pingLatency > 0) {
            sb.append(
                "\n${
                    context.getString(
                        R.string.ping_latency_label, result.pingLatency
                    )
                }"
            )
        } else if (result.pingLatency == -1L) {
            sb.append("\n${context.getString(R.string.ping_failed)}")
        }

        if (result.httpLatency > 0) {
            sb.append(
                "\n${
                    context.getString(
                        R.string.http_latency_label, result.httpLatency
                    )
                }"
            )
        } else if (result.httpLatency == -1L) {
            sb.append("\n${context.getString(R.string.http_failed)}")
        }

        // 如果ping和HTTP都失败，特别说明
        if (result.pingLatency == -1L && result.httpLatency == -1L) {
            sb.append("\n${context.getString(R.string.all_network_tests_failed)}")
        }

        sb.append(
            "\n${
                context.getString(
                    R.string.suggestion_label, getNetworkSuggestion(result.qualityLevel)
                )
            }"
        )

        return sb.toString()
    }

    /**
     * 检查网络类型变化
     */
    private fun checkNetworkTypeChange(currentNetworkType: NetworkType) {
        if (lastNetworkType != null && lastNetworkType != currentNetworkType && notifyOnNetworkTypeChange) {
            val message = context.getString(
                R.string.network_type_changed_message,
                getNetworkTypeDescription(lastNetworkType!!),
                getNetworkTypeDescription(currentNetworkType)
            )
            showNotification(
                context.getString(R.string.network_type_changed_title),
                message,
                NotificationCompat.PRIORITY_DEFAULT
            )
            LogX.i(TAG, "Network type changed from $lastNetworkType to $currentNetworkType")
        }
        lastNetworkType = currentNetworkType
    }

    /**
     * 检查特殊网络条件
     */
    private fun checkSpecialNetworkConditions(result: NetworkQualityResult) {
        // 检查VPN但无WiFi的情况
        if (notifyOnVpnWithoutWifi) {
            val isVpnActive = result.networkType == NetworkType.VPN || isVpnActiveAlternative()
            if (isVpnActive && !isWifiAvailable()) {
                val message = context.getString(
                    R.string.vpn_without_wifi_message, buildNetworkQualityMessage(result)
                )
                showNotification(
                    context.getString(R.string.network_connection_reminder),
                    message,
                    NotificationCompat.PRIORITY_DEFAULT
                )
                LogX.i(TAG, "VPN without WiFi detected")
            }
        }
    }

    /**
     * 替代的VPN检测方法
     */
    private fun isVpnActiveAlternative(): Boolean {
        return try {
            // 方法1: 检查网络接口名称
            val networkInterfaces = java.net.NetworkInterface.getNetworkInterfaces()
            for (networkInterface in networkInterfaces) {
                val name = networkInterface.name.lowercase()
                if (name.contains("tun") || name.contains("ppp") || name.contains("vpn")) {
                    LogX.d(TAG, "VPN detected via network interface: ${networkInterface.name}")
                    return true
                }
            }

            // 方法2: 检查路由表（简单检测）
            val process = Runtime.getRuntime().exec("ip route")
            val reader = java.io.BufferedReader(java.io.InputStreamReader(process.inputStream))
            val output = reader.readText()
            reader.close()

            if (output.contains("tun") || output.contains("ppp")) {
                LogX.d(TAG, "VPN detected via routing table")
                return true
            }

            false
        } catch (e: Exception) {
            LogX.d(TAG, "Alternative VPN detection failed: ${e.message}")
            false
        }
    }

    /**
     * 检查WiFi是否可用
     */
    private fun isWifiAvailable(): Boolean {
        return try {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        } catch (e: Exception) {
            LogX.e(TAG, "Error checking WiFi availability: ${e.message}")
            false
        }
    }

    private fun getNetworkTypeDescription(type: NetworkType): String {
        return when (type) {
            NetworkType.WIFI -> context.getString(R.string.wifi)
            NetworkType.CELLULAR_5G -> context.getString(R.string._5g_mobile_network)
            NetworkType.CELLULAR_4G -> context.getString(R.string._4g_mobile_network)
            NetworkType.CELLULAR_3G -> context.getString(R.string._3g_mobile_network)
            NetworkType.CELLULAR_2G -> context.getString(R.string._2g_mobile_network)
            NetworkType.ETHERNET -> context.getString(R.string.ethernet)
            NetworkType.VPN -> context.getString(R.string.vpn)
            NetworkType.UNKNOWN -> context.getString(R.string.unknown_network)
            NetworkType.NONE -> context.getString(R.string.no_network_connection)
        }
    }

    private fun getNetworkSuggestion(quality: NetworkQuality): String {
        return when (quality) {
            NetworkQuality.NO_CONNECTION -> context.getString(R.string.please_check_your_network_connection)
            NetworkQuality.VERY_POOR -> context.getString(R.string.it_is_recommended_to_switch_to_wifi_or_a_location_with_better_signal)
            NetworkQuality.POOR -> context.getString(R.string.the_current_network_is_slow_and_may_affect_your_experience)
            NetworkQuality.FAIR -> context.getString(R.string.the_network_is_average_consider_optimizing_your_network_environment)
            NetworkQuality.GOOD -> context.getString(R.string.the_network_is_good)
            NetworkQuality.EXCELLENT -> context.getString(R.string.the_network_is_excellent)
        }
    }

    /**
     * 添加到历史记录
     */
    private fun addToHistory(result: NetworkQualityResult) {
        networkHistory.add(result)
        if (networkHistory.size > maxHistorySize) {
            networkHistory.removeAt(0)
        }
    }

    /**
     * 获取网络质量趋势
     */
    fun getNetworkQualityTrend(): String {
        if (networkHistory.isEmpty()) return "暂无数据"

        val recent = networkHistory.takeLast(5)
        val avgPing = recent.filter { it.pingLatency > 0 }.map { it.pingLatency }.average()
        val avgHttp = recent.filter { it.httpLatency > 0 }.map { it.httpLatency }.average()

        return "近期平均Ping: ${avgPing.toInt()}ms, HTTP: ${avgHttp.toInt()}ms"
    }

    /**
     * 手动触发网络质量检测（用于测试）
     */
    suspend fun performManualNetworkCheck(): NetworkQualityResult {
        return performNetworkQualityCheck()
    }


    /**
     * 设置是否在网络类型变化时通知
     */
    fun setNotifyOnNetworkTypeChange(enabled: Boolean) {
        notifyOnNetworkTypeChange = enabled
        LogX.i(TAG, "Notify on network type change: $enabled")
    }

    /**
     * 设置是否在VPN但无WiFi时通知
     */
    fun setNotifyOnVpnWithoutWifi(enabled: Boolean) {
        notifyOnVpnWithoutWifi = enabled
        LogX.i(TAG, "Notify on VPN without WiFi: $enabled")
    }

    /**
     * 强制检测当前网络状态（用于调试）
     */
    fun debugCurrentNetworkState() {
        LogX.i(TAG, "=== Debug Current Network State ===")

        val networkType = getDetailedNetworkType()
        val isNetworkAvailable = isNetworkAvailable()
        val isWifiAvailable = isWifiAvailable()
        val isVpnAlternative = isVpnActiveAlternative()

        LogX.i(TAG, "Network Type: $networkType")
        LogX.i(TAG, "Network Available: $isNetworkAvailable")
        LogX.i(TAG, "WiFi Available: $isWifiAvailable")
        LogX.i(TAG, "VPN Alternative Detection: $isVpnAlternative")
        LogX.i(TAG, "Notify on Network Type Change: $notifyOnNetworkTypeChange")
        LogX.i(TAG, "Notify on VPN without WiFi: $notifyOnVpnWithoutWifi")
        LogX.i(TAG, "Last Network Type: $lastNetworkType")
        LogX.i(TAG, "Monitoring Active: ${monitorJob?.isActive}")

        // 强制触发特殊网络条件检查
        val testResult = NetworkQualityResult(
            timestamp = System.currentTimeMillis(),
            networkType = networkType,
            isConnected = isNetworkAvailable,
            pingLatency = 20,
            httpLatency = 25,
            qualityLevel = NetworkQuality.GOOD
        )

        LogX.i(TAG, "Testing special network conditions...")
        checkSpecialNetworkConditions(testResult)

        LogX.i(TAG, "=== End Debug ===")
    }

    /**
     * 立即触发一次网络检测（不等待定时器）
     */
    fun triggerImmediateCheck() {
        scope.launch {
            LogX.i(TAG, "Triggering immediate network check...")
            val result = performNetworkQualityCheck()
            handleNetworkQualityResult(result)
        }
    }

    /**
     * 获取监控状态
     */
    fun isMonitoringActive(): Boolean {
        return monitorJob?.isActive == true
    }

    /**
     * 获取监控状态信息（用于调试）
     */
    fun getMonitoringStatus(): String {
        return """
            Monitoring Active: ${isMonitoringActive()}
            Check Interval: ${checkIntervalMillis}ms
            Notify on Network Type Change: $notifyOnNetworkTypeChange
            Notify on VPN without WiFi: $notifyOnVpnWithoutWifi
            Last Network Type: $lastNetworkType
            History Size: ${networkHistory.size}
        """.trimIndent()
    }

    private fun checkRongIMConnection() {
        val currentStatus = RongIM.getInstance().currentConnectionStatus
        LogX.d(TAG, "checkRongIMConnection current status: $currentStatus")

        if (currentStatus != RongIMClient.ConnectionStatusListener.ConnectionStatus.CONNECTED) {
            LogX.e(TAG, "RongIMClient is not connected, try to reconnect.")
            reconnectRongIM()
        } else {
            LogX.d(TAG, "RongIMClient is connected")
        }
    }

    private fun reconnectRongIM() {
        val currentStatus = RongIM.getInstance().currentConnectionStatus
        if (currentStatus == RongIMClient.ConnectionStatusListener.ConnectionStatus.CONNECTED) {
            LogX.d(TAG, "RongIMClient 已连接，无需重连。")
            return
        }

        val token = DataStoreManager.getStringSync(DataStoreManager.KEY_RONG_YUN_TOKEN)
        if (!DataStoreManager.isUserLoggedInSync() || token.isNullOrEmpty()) {
            LogX.e(TAG, "RongIMClient Token 无效，用户未登录或token为空。")
            return
        }

        LogX.d(TAG, "开始重新连接 RongIMClient...")
        RongIM.connect(token, object : RongIMClient.ConnectCallback() {
            override fun onDatabaseOpened(status: RongIMClient.DatabaseOpenStatus) {
                LogX.d(TAG, "RongIMClient onDatabaseOpened: $status")
            }

            override fun onSuccess(userId: String) {
                LogX.d(TAG, "RongIMClient 融云连接已恢复。userId: $userId")
            }

            override fun onError(errorCode: RongIMClient.ConnectionErrorCode) {
                when (errorCode) {
                    RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_INCORRECT -> {
                        LogX.e(TAG, "RongIMClient Token 已过期，请重新登录。")
                    }

                    RongIMClient.ConnectionErrorCode.RC_CONNECT_TIMEOUT -> {
                        LogX.e(TAG, "RongIMClient 连接超时，请检查网络设置。")
                    }

                    else -> {
                        LogX.e(TAG, "RongIMClient 连接失败：${errorCode.name}")
                    }
                }
            }
        })
    }

    private fun isNetworkAvailable(): Boolean {
        return try {
            val network = connectivityManager.activeNetwork
            if (network == null) {
                LogX.d(TAG, "isNetworkAvailable: activeNetwork is null")
                return false
            }

            val capabilities = connectivityManager.getNetworkCapabilities(network)
            if (capabilities == null) {
                LogX.d(TAG, "isNetworkAvailable: networkCapabilities is null")
                return false
            }

            val hasInternet =
                capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            val hasValidated =
                capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)

            LogX.d(
                TAG, "isNetworkAvailable: hasInternet=$hasInternet, hasValidated=$hasValidated"
            )

            // 移除 NET_CAPABILITY_VALIDATED 检查，因为弱网时可能还未验证完成
            // 只检查是否有网络连接能力即可
            hasInternet
        } catch (e: Exception) {
            LogX.e(TAG, "Error checking network availability: ${e.message}")
            false
        }
    }

    private fun ping(host: String): Long? {
        return try {
            val start = System.currentTimeMillis()
            val process =
                Runtime.getRuntime().exec("ping -c 1 -W ${networkConfig.pingTimeout / 1000} $host")

            val reader = BufferedReader(InputStreamReader(process.inputStream))
            val output = reader.readText()
            reader.close()

            val exitCode = process.waitFor()

            LogX.d(TAG, "Ping $host - Exit code: $exitCode")

            if (exitCode == 0 && (output.contains("1 received") || output.contains("64 bytes from") || output.contains(
                    "1 packets transmitted, 1 received"
                ))
            ) {
                val latency = System.currentTimeMillis() - start
                LogX.d(TAG, "Ping $host successful: ${latency}ms")
                latency
            } else {
                LogX.d(TAG, "Ping $host failed")
                null
            }
        } catch (e: Exception) {
            LogX.e(TAG, "Ping $host error: ${e.message}")
            null
        }
    }


}