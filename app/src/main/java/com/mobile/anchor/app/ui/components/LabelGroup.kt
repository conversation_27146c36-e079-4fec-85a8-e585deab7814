package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

/**
 * 标签组组件
 * 用于显示多个标签，如语言、兴趣等
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun LabelGroup(
    labels: List<String>?,
    modifier: Modifier = Modifier,
    backgroundColor: Color = MaterialTheme.colorScheme.primaryContainer,
    contentColor: Color = MaterialTheme.colorScheme.onPrimaryContainer
) {
    if (labels.isNullOrEmpty()) return
    
    FlowRow(
        modifier = modifier
    ) {
        labels.forEach { label ->
            Label(
                text = label,
                backgroundColor = backgroundColor,
                contentColor = contentColor,
                modifier = Modifier.padding(end = 8.dp, bottom = 8.dp)
            )
        }
    }
}

/**
 * 单个标签组件
 */
@Composable
fun Label(
    text: String,
    modifier: Modifier = Modifier,
    backgroundColor: Color = MaterialTheme.colorScheme.primaryContainer,
    contentColor: Color = MaterialTheme.colorScheme.onPrimaryContainer
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .background(backgroundColor)
            .padding(horizontal = 12.dp, vertical = 4.dp)
    ) {
        Text(
            text = text,
            color = contentColor,
            style = MaterialTheme.typography.bodySmall
        )
    }
}