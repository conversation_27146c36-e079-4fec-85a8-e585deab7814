package com.mobile.anchor.app.ui.conversation

import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.bdc.android.library.extension.jump
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.Logger
import com.bdc.android.library.utils.ToastUtil
import com.gyf.immersionbar.ImmersionBar
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.GiftItemBean
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.activities.UserDetailActivity
import com.mobile.anchor.app.ui.fragments.MyConversationFragment
import com.mobile.anchor.app.ui.viewmodels.GiftRequestEvent
import com.mobile.anchor.app.ui.viewmodels.GiftViewModel
import com.mobile.anchor.app.utils.Constants
import com.mobile.anchor.app.utils.RongYunUtil
import io.rong.imkit.IMCenter
import io.rong.imkit.MessageItemLongClickActionManager
import io.rong.imkit.RongIM
import io.rong.imkit.activity.RongBaseActivity
import io.rong.imkit.config.ConversationClickListener
import io.rong.imkit.conversation.ConversationFragment
import io.rong.imkit.conversation.ConversationViewModel
import io.rong.imkit.model.TypingInfo.TypingUserInfo
import io.rong.imkit.picture.entity.LocalMedia
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.userinfo.RongUserInfoManager.UserDataObserver
import io.rong.imkit.userinfo.model.GroupUserInfo
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Group
import io.rong.imlib.model.Message
import io.rong.imlib.model.UserInfo
import java.lang.ref.WeakReference

/**
 * Author:Lxf
 * Create on:2024/8/6
 * Description:
 */
open class MyRongConversationActivity : RongBaseActivity() {
    private val viewModel by viewModels<GiftViewModel>()
    protected var mTargetId: String? = null
    private var mConversationType: Conversation.ConversationType? = null
    private var mConversationFragment: MyConversationFragment? = null
    private var conversationViewModel: ConversationViewModel? = null
    private var selectImageCallBack: (List<LocalMedia>) -> Unit = {}

    // 使用静态内部类和弱引用持有回调，避免内存泄漏
    private var rongCoreCallback: IRongCoreCallback.ResultCallback<Boolean>? = null
    private var rongIMClientCallback: RongIMClient.ResultCallback<Boolean>? = null


    private val pickMedia =
        registerForActivityResult(ActivityResultContracts.PickMultipleVisualMedia(9)) {
            val localMediaList = kotlin.collections.ArrayList<LocalMedia>()
            it.forEach { selMedia ->
                localMediaList.add(LocalMedia().apply {
                    path = RongYunUtil.getPathFromUri(this@MyRongConversationActivity, selMedia)
                    LogX.e("selPath $path")
                })
            }
            selectImageCallBack.invoke(localMediaList)
        }

    fun pickImages(callBack: (List<LocalMedia>) -> Unit) {
        pickMedia.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
        selectImageCallBack = callBack
    }

    private val mUserDataObserver: UserDataObserver = object : UserDataObserver {
        override fun onUserUpdate(info: UserInfo) {
            if (TextUtils.equals(mTargetId, info.userId)) {
                runOnUiThread { setTitle() }
            }
        }

        override fun onGroupUpdate(group: Group) {
            if (TextUtils.equals(mTargetId, group.id)) {
                runOnUiThread { setTitle() }
            }
        }

        override fun onGroupUserInfoUpdate(groupUserInfo: GroupUserInfo) {
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (this.intent != null) {
            this.mTargetId = this.intent.getStringExtra("targetId")
            val type = this.intent.getStringExtra("ConversationType")
            if (TextUtils.isEmpty(type)) {
                return
            }

            this.mConversationType = Conversation.ConversationType.valueOf(type!!.uppercase())
        }

        this.setContentView(R.layout.rc_conversation_activity)
        this.setTitle()
        this.mConversationFragment =
            this.supportFragmentManager.findFragmentById(R.id.conversation) as MyConversationFragment?
        mTitleBar.setOnBackClickListener {
            if (mConversationFragment?.onBackPressed() == false) {
                finish()
            }
        }
        mTitleBar.rightView.visibility = View.GONE
        this.initViewModel()
        this.observeUserInfoChange()
        ImmersionBar.with(this).statusBarDarkFont(false)
            .navigationBarColor(R.color.black).init()
        mTitleBar.setBackgroundColor(
            ContextCompat.getColor(
                this, com.bdc.android.library.R.color.transparent
            )
        )
        setupPortraitClickEvent()
        deleteLongClickItem()
        initViewEvents()
        if (mTargetId == Constants.RONG_YUN_ID_SYSTEM) {
            RongYunUtil.cacheSystemNoticeInfo()
        }
    }

    private fun observeUserInfoChange() {
        if (!TextUtils.isEmpty(this.mTargetId)) {
            RongUserInfoManager.getInstance().addUserDataObserver(this.mUserDataObserver)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (!TextUtils.isEmpty(this.mTargetId)) {
            RongUserInfoManager.getInstance().removeUserDataObserver(this.mUserDataObserver)
            // 清空回调对象
            rongCoreCallback = null
            rongIMClientCallback = null

            // 尝试释放资源以避免内存泄漏
            try {
                // 融云SDK没有直接的取消请求方法
                // 这里我们使用其他方式清理资源
                System.gc()
            } catch (e: Exception) {
                LogX.e("Error cleaning up resources: ${e.message}")
            }
        }
        // 移除ViewModel观察者
        conversationViewModel?.let { viewModel ->
            viewModel.typingStatusInfo.removeObservers(this)
            viewModel.typingStatusInfo.value = null
        }

        // 清除会话监听器
        RongIM.setConversationClickListener(null)
        IMCenter.setConversationClickListener(null)

        // 取消所有融云相关的回调和监听器
        try {
            // 尝试取消所有可能的监听器
            // 根据融云SDK的实际API调整这里的代码
            try {
                // 如果有消息监听器，尝试取消注册
                val method =
                    RongCoreClient::class.java.getDeclaredMethod("removeOnReceiveMessageListener")
                method.invoke(RongCoreClient.getInstance())
            } catch (e: Exception) {
                // 如果方法不存在或调用失败，忽略异常
                LogX.e("Cannot remove message listener: ${e.message}")
            }

            // 清除任何可能的内部回调引用
            System.gc()
        } catch (e: Exception) {
            LogX.e("Error cleaning up Rong callbacks: ${e.message}")
        }

        // 取消ActivityResultLauncher注册
        try {
            pickMedia.unregister()
        } catch (e: Exception) {
            LogX.e("Error unregistering pickMedia: ${e.message}")
        }

        // 清除回调引用
        selectImageCallBack = {}

        // 清除Fragment引用
        mConversationFragment?.onDestroy()
        mConversationFragment = null

        mTargetId = null
        mConversationType = null
        conversationViewModel = null
    }

    private fun setTitle() {
        if (!TextUtils.isEmpty(this.mTargetId) && this.mConversationType == Conversation.ConversationType.GROUP) {
            val group = RongUserInfoManager.getInstance().getGroupInfo(this.mTargetId)
            mTitleBar.setTitle(if (group == null) this.mTargetId else group.name)
        } else {
            val userInfo = RongUserInfoManager.getInstance().getUserInfo(this.mTargetId)
            mTitleBar.setTitle(if (userInfo == null) this.mTargetId else userInfo.name)
        }

        if (this.mConversationType == Conversation.ConversationType.CUSTOMER_SERVICE || (this.mConversationType == Conversation.ConversationType.CHATROOM)) {
            mTitleBar.setRightVisible(false)
        }
    }

    private fun initViewModel() {
        this.conversationViewModel = ViewModelProvider(this)[ConversationViewModel::class.java]
        conversationViewModel?.typingStatusInfo?.observe(this) { typingInfo ->
            if (typingInfo != null) {
                if ((typingInfo.conversationType == mConversationType) && mTargetId == typingInfo.targetId) {
                    if (typingInfo.typingList == null) {
                        mTitleBar.middleView.visibility = View.VISIBLE
                        mTitleBar.typingView.visibility = View.GONE
                    } else {
                        mTitleBar.middleView.visibility = View.GONE
                        mTitleBar.typingView.visibility = View.VISIBLE
                        val typing =
                            typingInfo.typingList[typingInfo.typingList.size - 1] as TypingUserInfo
                        if (typing.type == TypingUserInfo.Type.text) {
                            mTitleBar.setTyping(R.string.rc_conversation_remote_side_is_typing)
                        } else if (typing.type == TypingUserInfo.Type.voice) {
                            mTitleBar.setTyping(R.string.rc_conversation_remote_side_speaking)
                        }
                    }
                }
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (4 == event.keyCode && this.mConversationFragment != null && !mConversationFragment!!.onBackPressed()) {
            this.finish()
        }

        return false
    }

    private fun gotoDetail() {
        Logger.e("MyRongConversationActivity, gotoDetail $mTargetId")
        jump(UserDetailActivity::class.java, Bundle().apply {
            putString("id", mTargetId)
        })
    }

    // 静态内部类实现ConversationClickListener避免内存泄漏
    private class StaticConversationClickListener(activity: MyRongConversationActivity) :
        ConversationClickListener {
        private val weakActivity = WeakReference(activity)

        override fun onUserPortraitClick(
            context: Context,
            conversationType: Conversation.ConversationType,
            user: UserInfo,
            targetId: String
        ): Boolean {
            val activity = weakActivity.get() ?: return false
            if (user.userId == targetId) {
                activity.gotoDetail()
                return true
            }
            return false
        }

        override fun onUserPortraitLongClick(
            context: Context?,
            conversationType: Conversation.ConversationType?,
            user: UserInfo?,
            targetId: String?
        ): Boolean {
            return false
        }

        override fun onMessageClick(
            context: Context?, view: View?, message: Message?
        ): Boolean {
            return false
        }

        override fun onMessageLongClick(
            context: Context?, view: View?, message: Message?
        ): Boolean {
            return false
        }

        override fun onMessageLinkClick(
            context: Context?, link: String?, message: Message?
        ): Boolean {
            return false
        }

        override fun onReadReceiptStateClick(context: Context?, message: Message?): Boolean {
            return false
        }
    }

    private fun setupPortraitClickEvent() {
        RongIM.setConversationClickListener(StaticConversationClickListener(this))
    }

    private fun deleteLongClickItem() {
        val clickActions =
            MessageItemLongClickActionManager.getInstance().messageItemLongClickActions
        val iterator = clickActions.iterator()
        val delActionTitle = getString(R.string.rc_dialog_item_message_reference)
        while (iterator.hasNext()) {
            val clickAction = iterator.next()
            val isDelAction = delActionTitle == clickAction.getTitle(this)
            if (isDelAction) {
                iterator.remove()
                break
            }
        }
    }

    fun sendGift(gift: GiftItemBean) {
        viewModel.giftGive(mTargetId ?: "", giftBean = gift)
    }

    private fun initViewEvents() {
        viewModel.pageEvents.observeEvent(this) { event ->
            when (event) {
                is GiftRequestEvent.GiftSendSuc -> {
//                    RongYunUtil.sendAskGiftMessage(mTargetId ?: "", event.giftBean)
                }

                is GiftRequestEvent.GiftSendFailed -> {
                    LogX.i("sendGiftFailed ${event.msg}")
                    ToastUtil.show(event.msg)
                }

                else -> {}
            }
        }
    }


    // 静态内部类实现IMCenter的ConversationClickListener
    private class StaticIMCenterClickListener : ConversationClickListener {
        override fun onUserPortraitClick(
            context: Context,
            conversationType: Conversation.ConversationType?,
            user: UserInfo?,
            targetId: String?
        ): Boolean {
            return false
        }

        override fun onUserPortraitLongClick(
            context: Context,
            conversationType: Conversation.ConversationType?,
            user: UserInfo?,
            targetId: String?
        ): Boolean {
            return false
        }

        override fun onMessageClick(
            context: Context, view: View, message: Message
        ): Boolean {
            return false
        }

        override fun onMessageLongClick(
            context: Context, view: View, message: Message
        ): Boolean {
            return true
        }

        override fun onMessageLinkClick(
            context: Context?, link: String?, message: Message?
        ): Boolean {
            return false
        }

        override fun onReadReceiptStateClick(
            context: Context?, message: Message?
        ): Boolean {
            return false
        }
    }

    private fun setupMessageLongClick() {
        IMCenter.setConversationClickListener(StaticIMCenterClickListener())
    }

    fun getTargetId(): String? {
        return mTargetId
    }
}