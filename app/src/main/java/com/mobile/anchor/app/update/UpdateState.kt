package com.mobile.anchor.app.update

import com.mobile.anchor.app.data.model.VersionBean

/**
 * 应用更新状态管理
 */
sealed class UpdateState {
    object Idle : UpdateState()
    object CheckingUpdate : UpdateState()
    data class UpdateAvailable(val versionInfo: VersionBean) : UpdateState()
    object NoUpdateAvailable : UpdateState()
    data class CheckUpdateError(val error: String) : UpdateState()
}

/**
 * 下载状态
 */
sealed class DownloadState {
    object Idle : DownloadState()
    object Preparing : DownloadState()
    data class Downloading(
        val progress: Int,
        val downloadedBytes: Long,
        val totalBytes: Long,
        val speed: String = ""
    ) : DownloadState()
    data class Downloaded(val filePath: String) : DownloadState()
    data class DownloadError(val error: String) : DownloadState()
    object DownloadCancelled : DownloadState()
}

/**
 * 安装状态
 */
sealed class InstallState {
    object Idle : InstallState()
    object Installing : InstallState()
    object InstallSuccess : InstallState()
    data class InstallError(val error: String) : InstallState()
}

/**
 * 弹框状态
 */
sealed class DialogState {
    object None : DialogState()
    data class ShowUpdateDialog(val versionInfo: VersionBean) : DialogState()
    data class ShowProgressDialog(val downloadState: DownloadState) : DialogState()
}

/**
 * 下载进度信息
 */
data class DownloadProgress(
    val progress: Int,
    val downloadedBytes: Long,
    val totalBytes: Long,
    val speed: String,
    val remainingTime: String
) {
    val progressText: String get() = "$progress%"
    val sizeText: String get() = "${formatBytes(downloadedBytes)}/${formatBytes(totalBytes)}"

    private fun formatBytes(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            else -> String.format("%.1fMB", bytes / (1024.0 * 1024.0))
        }
    }
}
