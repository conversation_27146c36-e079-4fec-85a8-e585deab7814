package com.mobile.anchor.app.ui.call

import android.media.MediaPlayer
import android.os.Vibrator
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.SurfaceView
import android.view.View
import android.view.View.OnTouchListener
import androidx.activity.addCallback
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.lxj.xpopup.XPopup
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.ChatTipBean
import com.mobile.anchor.app.data.model.RecordTimeBean
import com.mobile.anchor.app.data.model.RelationAction
import com.mobile.anchor.app.data.model.RemoteCallInviteExtend
import com.mobile.anchor.app.data.model.VideoMessageBean
import com.mobile.anchor.app.data.network.onError
import com.mobile.anchor.app.data.network.onSuccess
import com.mobile.anchor.app.databinding.ActivityVideoChatCallBinding
import com.mobile.anchor.app.extension.loadAnchorImage
import com.mobile.anchor.app.extension.loadAvatar
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.CallStateManager
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.manager.FileUploadManager
import com.mobile.anchor.app.popup.showGiftPopup
import com.mobile.anchor.app.socket.WebSocketManager
import com.mobile.anchor.app.socket.WebSocketMessageSender
import com.mobile.anchor.app.ui.popup.NormalNewPopup
import com.mobile.anchor.app.ui.popup.showNormalNewPopup
import com.mobile.anchor.app.ui.viewmodels.GiftRequestEvent
import com.mobile.anchor.app.ui.viewmodels.GiftViewModel
import com.mobile.anchor.app.utils.Constants
import com.mobile.anchor.app.utils.ContextHolder
import com.mobile.anchor.app.utils.SoftInputUtil
import com.mobile.anchor.app.utils.VideoUtil
import com.mobile.anchor.app.utils.fromJson
import io.agora.rtc2.AgoraMediaRecorder
import io.agora.rtc2.AgoraMediaRecorder.MediaRecorderConfiguration
import io.agora.rtc2.ChannelMediaOptions
import io.agora.rtc2.Constants.REMOTE_VIDEO_STATE_STOPPED
import io.agora.rtc2.IMediaRecorderCallback
import io.agora.rtc2.IRtcEngineEventHandler
import io.agora.rtc2.RecorderInfo
import io.agora.rtc2.RecorderStreamInfo
import io.agora.rtc2.RtcEngine
import io.agora.rtc2.RtcEngineConfig
import io.agora.rtc2.video.BeautyOptions
import io.agora.rtc2.video.FaceShapeBeautyOptions
import io.agora.rtc2.video.VideoCanvas
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum
import io.rong.imlib.RongCoreClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.message.TextMessage
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class CallVideoChatActivity : BaseCoreActivity<ActivityVideoChatCallBinding, VideoCallViewModel>() {
    companion object {
        const val VIDEO_DEFAULT_EXIT_TIME = 30 //默认视频无接听超时时间
    }

    private var followed: Boolean = false //是否关注

    override fun getLayoutId(): Int {
        return R.layout.activity_video_chat_call
    }

    private var mRtcEngine: RtcEngine? = null
    private var agoraMediaRecorder: AgoraMediaRecorder? = null
    private var channelID = 0
    private var rtcToken = ""
    private var peerUID = 0
    private var localSelVideoCanvas: VideoCanvas? = null
    private var remoteVideoCanvas: VideoCanvas? = null
    private var mediaPlayer: MediaPlayer? = null
    private var vibrator: Vibrator? = null
    private var remoteCallInviteExtend: RemoteCallInviteExtend? = null // 添加成员变量
    private var exitTipPopupView: NormalNewPopup? = null
    private var answerCountDownJob: Job? = null //接听倒计时
    private var videoTimeJob: Job? = null //视频时长 每秒更新
    private val timeString: StringBuilder = StringBuilder()
    private var seconds = 0
    private val giftViewModel by viewModels<GiftViewModel>()
    private val recordTimes: MutableList<RecordTimeBean> = mutableListOf()
    private var joinChannelCountDownJob: Job? = null //加入房间倒计时
    private var waitOtherCountDownJob: Job? = null //等待其他人进入房间倒计时
    private var joinChannelSuc: Boolean = false //joinChannel 是否成功

    override fun initView() {
        super.initView()
        VideoUtil.screenSecure(window)
        val softInputUtil = SoftInputUtil()
        softInputUtil.attachSoftInput(mBinding.bottomSendMsg) { isSoftInputShow, _, viewOffset ->
            if (isSoftInputShow) {
                mBinding.bottomSendMsg.translationY -= viewOffset
            } else {
                mBinding.bottomSendMsg.translationY = 0f
                mBinding.bottomSendMsg.requestLayout()
                mBinding.bottomSendMsg.visibility = View.GONE
            }
        }

        onBackPressedDispatcher.addCallback(this) {
            backEvent()
        }

        handleClick()

        mBinding.RLRemoteVideo.apply {
            setOnTouchListener(object : OnTouchListener {
                private var lastX = 0f
                private var lastY = 0f
                private var dX = 0f
                private var dY = 0f
                private var isDragging = false
                private val screenWidth = resources.displayMetrics.widthPixels
                private val screenHeight = resources.displayMetrics.heightPixels

                override fun onTouch(v: View, event: MotionEvent): Boolean {
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            // 记录手指按下时的坐标
                            lastX = event.rawX
                            lastY = event.rawY
                            dX = v.x - lastX
                            dY = v.y - lastY
                            isDragging = false
                        }

                        MotionEvent.ACTION_MOVE -> {
                            // 计算手指的移动距离并更新视图的位置
                            var newX = event.rawX + dX
                            var newY = event.rawY + dY

                            // 限制视图的移动范围
                            val viewWidth = v.width
                            val viewHeight = v.height

                            // 防止移动到屏幕外
                            newX = newX.coerceIn(0f, (screenWidth - viewWidth).toFloat())
                            newY = newY.coerceIn(0f, (screenHeight - viewHeight).toFloat())

                            // 更新View的位置
                            v.x = newX
                            v.y = newY

                            // 标记拖动动作
                            isDragging = true
                        }

                        MotionEvent.ACTION_UP -> {
                            // 手指抬起时，判断是否为拖动，若不是，则是点击
                            if (!isDragging) {
                                // 处理点击
                                v.performClick()
                            }
                        }
                    }
                    return true
                }
            })
        }

        mBinding.giftEffectView.apply {
            setGiftAnimation("loveheart.svga")
        }
    }

    override fun initData() {
        intent.getStringExtra("remoteCallInviteExtend")?.let { jsonString ->
            runCatching {
                jsonString.fromJson<RemoteCallInviteExtend>()
            }.onSuccess { parsedExtend ->
                this.remoteCallInviteExtend = parsedExtend // 赋值给成员变量
                parsedExtend?.let {
                    LogX.d("$TAG, 成功解析 RemoteCallInviteExtend: $it")
                    handleCallInviteData(it)
                } ?: run {
                    LogX.e("$TAG, 解析 RemoteCallInviteExtend 为 null")
                }
            }.onFailure { e ->
                LogX.e(
                    "$TAG, 解析 RemoteCallInviteExtend JSON 失败: ${e.message}, JSON: $jsonString"
                )
            }
        } ?: run {
            LogX.d("$TAG, Intent 中没有找到 remoteCallInviteExtend 参数")
        }
        DataStoreManager.getVideoConfig()?.let {
            recordTimes.clear()
            recordTimes.addAll(it)
        }
        LogX.d("$TAG, 录制视频配置：$recordTimes")
        setupFlowListener()
        mediaPlayer = VideoUtil.startRing(this)
        setupVibrator()
        countDownAnswer()
    }

    override fun isImmerse(): Boolean {
        return true
    }

    private fun setupFlowListener() {
        FlowBus.with<Int>(Constants.PushCmd.PUSH_CMD_REMOTE_CALL_INVITE_CANCEL).register(this) {
            if (channelID == it) {
                finishActivity(false)
            }
        }

        FlowBus.with<ChatTipBean>(Constants.PUSH_TYPE_TEXT_MESSAGE).register(this) {
            if (it.id == remoteCallInviteExtend?.peerUID.toString()) {
                appendList(
                    mutableListOf(
                        VideoMessageBean(
                            name = remoteCallInviteExtend?.callerNickname ?: "",
                            message = it.content
                        )
                    )
                )
            }
        }

        FlowBus.with<ChatTipBean>(Constants.PUSH_TYPE_REQUEST_GIFT).register(this) {
            if (it.id == remoteCallInviteExtend?.peerUID.toString()) {
                appendList(
                    mutableListOf(
                        VideoMessageBean(
                            name = remoteCallInviteExtend?.callerNickname ?: "",
                            giftImage = it.headFileName,
                            giftId = it.id,
                        )
                    )
                )
            }
        }

        FlowBus.with<Int>(Constants.PushCmd.PUSH_CMD_REMOTE_SMALL_GIFT).register(this) {
            mBinding.giftEffectView.play(coin = it)
        }
    }

    private fun setupVibrator() {
        vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator
        val pattern = longArrayOf(1000L, 1000L) // 关 1000 毫秒，开 1000 毫秒
        vibrator?.vibrate(pattern, 0)
    }

    private fun countDownAnswer() {
        //30s倒计时 没有接通的话 自动退出
        val serverPushWaitTime = remoteCallInviteExtend?.anchorCallTimeoutRefuseDuration ?: 0
        val waitTime = if (serverPushWaitTime > 0) serverPushWaitTime else VIDEO_DEFAULT_EXIT_TIME
        var count = waitTime
        answerCountDownJob = mViewModel.countDownCoroutines(waitTime, onTick = { time ->
            count--
        }, onFinish = {
            if (count <= 0) {
                LogX.i("$TAG, 总倒计时结束， 发送socket ，关闭页面，channelID: $channelID")
                sendCallRefuseMessage(
                    Constants.WebSocketParamValue.CALL_REFUSE_REASON_ANCHOR_TIMEOUT_REFUSE,
                    "anchor timeout"
                )
                finish()
            }
        })
    }

    private fun cancelAnswerCountDown() {
        answerCountDownJob?.cancel()
        answerCountDownJob = null
    }


    private fun handleClick() {
        mBinding.apply {
            anchorListen.click {
                CLLeave.makeGone()
                initializeAndJoinChannel()
                releaseRing()
            }
            leave.click {
                showExitPopup()
            }
            sendGift.click {
                showGiftPopup(this@CallVideoChatActivity, peerUID.toString()) {
                    giftViewModel.giftGive(peerUID.toString(), it)
                }
            }
            hungUp.click {
                showExitPopup()
            }
            inputPanelSendBtn.click {
                if (mBinding.editBtn.text?.isNotEmpty() == true) {
                    remoteCallInviteExtend?.peerUID.let {
                        val content = mBinding.editBtn.text.toString().trim()
                        mBinding.editBtn.setText("")
                        val conversationType: Conversation.ConversationType =
                            Conversation.ConversationType.PRIVATE
                        val messageContent = TextMessage.obtain(content)
                        val message =
                            Message.obtain(it.toString(), conversationType, messageContent)
                        message.isCanIncludeExpansion = true
                        RongCoreClient.getInstance().sendMessage(
                            message, null, null, object : IRongCoreCallback.ISendMessageCallback {
                                override fun onAttached(message: Message?) {
                                }

                                override fun onSuccess(message: Message?) {
                                    appendList(
                                        mutableListOf(
                                            VideoMessageBean(
                                                name = getString(R.string.label_me),
                                                message = content
                                            )
                                        )
                                    )
                                    hideSendMsg()
                                }

                                override fun onError(
                                    message: Message?, coreErrorCode: IRongCoreEnum.CoreErrorCode?
                                ) {
                                    ToastUtil.show(getString(R.string.message_send_failed))
                                }
                            })
                    }
                }
            }
            llInputText.click {
                mBinding.bottomSendMsg.visibility = View.VISIBLE
                mBinding.editBtn.setText("")
                mBinding.editBtn.requestFocus()
                SoftInputUtil.showSoftInput(mBinding.editBtn)
            }

            mBinding.llFollowTop.click {
                followAction()
            }
            mBinding.llFollow.click {
                followAction()
            }
        }
    }

    private fun hideSendMsg() {
        mBinding.bottomSendMsg.visibility = View.GONE
        SoftInputUtil.hideSoftInput(mBinding.editBtn)
    }

    private fun followAction() {
        if (followed) {
            followed = false
            setFollowView()
            remoteCallInviteExtend?.peerUID?.let {
                mViewModel.relationOperation(RelationAction.UNFOLLOW, it.toString())
            }
        } else {
            followed = true
            setFollowView()
            remoteCallInviteExtend?.peerUID?.let {
                mViewModel.relationOperation(RelationAction.FOLLOW, it.toString())
            }
        }
    }

    private fun releaseRing() {
        vibrator?.cancel()
        vibrator = null
        mediaPlayer?.release()
        mediaPlayer = null
    }

    private fun handleCallInviteData(info: RemoteCallInviteExtend) {
        peerUID = info.peerUID
        channelID = info.callID
        CallStateManager.startCall(channelID)
        rtcToken = info.rtcToken
        mBinding.apply {
            head.loadAvatar(info.callerAvatar)
            head02.loadAnchorImage(info.callerAvatar)
            ivRemoteCloseVideoHeader.loadAvatar(info.callerAvatar)
            anchorName.text = info.callerNickname
            anchorName02.text = info.callerNickname
            ivVip.visibility = if (info.isVip) View.VISIBLE else View.GONE
            levelLabelView.current = info.level
            tvAge1.text = info.callerAge.toString()
            tvAge2.text = info.callerAge.toString()
            tvCountry1.text = info.userCountry?.title ?: ""
            tvCountry2.text = info.userCountry?.title ?: ""
            followed =
                (info.showRelation == Constants.FOLLOW_FLAG_FOLLOWED || info.showRelation == Constants.FOLLOW_FLAG_FOLLOW_EACH_OTHER)
            setFollowView()
        }
    }

    private fun setFollowView() {
        if (followed) {
            val labelCancelFollow = getString(R.string.label_cancel_follow)
            mBinding.tvFollow.text = labelCancelFollow
            mBinding.tvFollowTop.text = labelCancelFollow
            mBinding.ivFollow.setImageResource(R.mipmap.ic_call_unfollow)
            mBinding.ivFollowTop.setImageResource(R.mipmap.ic_call_top_unfollow)
            mBinding.llFollowTop.setBackgroundResource(R.drawable.shape_call_top_unfollow_btn_bg)
            mBinding.llFollow.setBackgroundResource(R.drawable.shape_call_top_unfollow_btn_bg)
        } else {
            val labelFollow = getString(R.string.off_attention)
            mBinding.tvFollow.text = labelFollow
            mBinding.tvFollowTop.text = labelFollow
            mBinding.ivFollow.setImageResource(R.mipmap.ic_follow)
            mBinding.ivFollowTop.setImageResource(R.mipmap.ic_call_top_follow)
            mBinding.llFollowTop.setBackgroundResource(R.drawable.shape_follow_btn_bg)
            mBinding.llFollow.setBackgroundResource(R.drawable.shape_follow_btn_bg)
        }
    }


    private fun initializeAndJoinChannel() {
        try {
            //log日志
            val logConfig = RtcEngineConfig.LogConfig()
            logConfig.level =
                io.agora.rtc2.Constants.LogLevel.getValue(io.agora.rtc2.Constants.LogLevel.LOG_LEVEL_INFO)
            logConfig.fileSizeInKB = 2048

            val config = RtcEngineConfig()
            config.mContext = baseContext
            LogX.d("$TAG, initializeAndJoinChannel agoraConfig.appID: ${DataStoreManager.getAnchorConfigBeanSync()?.agoraConfig?.appID}， channelID: $channelID")
            config.mAppId = DataStoreManager.getAnchorConfigBeanSync()?.agoraConfig?.appID ?: ""
            config.mEventHandler = mRtcEventHandler
            config.mLogConfig = logConfig
            mRtcEngine = RtcEngine.create(config)
        } catch (e: Exception) {
            LogX.d("$TAG, initializeAndJoinChannel mRtcEngine create failed: ${e.message}， channelID: $channelID")
            throw RuntimeException("Check the error.")
        }
        mRtcEngine?.apply {
            val recorderStreamInfo = RecorderStreamInfo(
                channelID.toString(), (DataStoreManager.getUserIdSync() ?: "0").toInt(), 0
            )
            agoraMediaRecorder = createMediaRecorder(recorderStreamInfo)
            agoraMediaRecorder?.setMediaRecorderObserver(object : IMediaRecorderCallback {
                override fun onRecorderStateChanged(
                    channelId: String?, uid: Int, state: Int, reason: Int
                ) {
                    LogX.d("$TAG, onRecorderStateChanged $channelId  $uid $state $reason")
                }

                override fun onRecorderInfoUpdated(
                    channelId: String?, uid: Int, info: RecorderInfo?
                ) {
                    LogX.d("$TAG, onRecorderInfoUpdated $channelId  $uid $info ")
                }

            })
            enableVideo()
            val uid = (DataStoreManager.getUserIdSync() ?: "0").toInt()
            val options = ChannelMediaOptions().apply {
                channelProfile = io.agora.rtc2.Constants.CHANNEL_PROFILE_LIVE_BROADCASTING
                clientRoleType = io.agora.rtc2.Constants.CLIENT_ROLE_BROADCASTER
                // 发布麦克风采集的音频
                publishMicrophoneTrack = true
                // 发布摄像头采集的视频
                publishCameraTrack = true
                // 自动订阅所有音频流
                autoSubscribeAudio = true
                // 自动订阅所有视频流
                autoSubscribeVideo = true
            }
            //加入房间倒计时
            val joinWaitTime = remoteCallInviteExtend?.joinTimeout?.takeIf { it > 0 } ?: 10
            countDownJoinChannel(joinWaitTime)

            //开始加入房间
            val code = joinChannel(
                rtcToken, channelID.toString(), uid, options
            )

            if (code != 0) { //主播加入房间失败
                LogX.i("$TAG, anchor join Channel failed: $code, channelID: $channelID")
                sendCallHangUpMessage(Constants.WebSocketParamValue.CALL_HANG_UP_REASON_ANCHOR_JOIN_FAILED)
            } else {
                //加入频道发送socket 上报
                remoteCallInviteExtend?.let { info ->
                    // 调用封装的方法发送消息
                    WebSocketMessageSender.sendCallAcceptMessage(info)
                } ?: run {
                    LogX.e("$TAG, 无法获取 RemoteCallInviteExtend 对象，未能发送 call_accept 消息, channelID: $channelID")
                }
            }
            // 创建一个 SurfaceView 对象，并将其作为 FrameLayout 的子对象
            val surfaceView = SurfaceView(baseContext)
            mBinding.localVideoViewContainer.addView(surfaceView)
            // 将 SurfaceView 对象传入声网实时互动 SDK，设置本地视图
            localSelVideoCanvas = VideoCanvas(surfaceView, VideoCanvas.RENDER_MODE_HIDDEN, 0)
            setupLocalVideo(localSelVideoCanvas)
            // 开启本地视频预览。
            startPreview()
            //声网基础美颜
            setBeautyEffectOptions(true, BeautyOptions())
            //声网美型效果
            val beautyOptions = FaceShapeBeautyOptions().apply {
                // 设置美型风格类型为男性
                shapeStyle = FaceShapeBeautyOptions.FACE_SHAPE_BEAUTY_STYLE_FEMALE
                //设置风格强度
                styleIntensity = 100
            }
            setFaceShapeBeautyOptions(true, beautyOptions)

            //TODO 没撒效果
//            val areaOptions = FaceShapeAreaOptions().apply {
//                // 设置区域类型为头部
//                shapeArea  = FaceShapeAreaOptions.FACE_SHAPE_AREA_HEADSCALE
//                // 设置区域强度
//                shapeIntensity  = 100
//            }
//            setFaceShapeAreaOptions(areaOptions)

            //开启声网人脸检测
            enableFaceDetection(true)
        }
    }

    private fun setJoinCountDownView(show: Boolean) {
        LogX.e("$TAG, setJoinCountDownView  $show")
        mBinding.tvJoinCountDownTime.makeVisible(show)
    }

    private fun countDownJoinChannel(maxTime: Int = 10) {
        //10s倒计时 没有加入房间的话 自动退出
        var count = maxTime
        joinChannelCountDownJob = mViewModel.countDownCoroutines(maxTime, onTick = { time ->
            count--
        }, onFinish = {
            if (count <= 0) {
                LogX.i("$TAG, join Channel failed channelID: $channelID")
                ToastUtil.show(getString(R.string.join_channel_failed))
                sendCallRefuseMessage(
                    Constants.WebSocketParamValue.CALL_REFUSE_REASON_ANCHOR_JOIN_CHANNEL_FAILED,
                    "anchor join channel failed"
                )
                finish()
            }
        })
    }

    private fun countDownWaitOtherJoin(maxTime: Int = 10) {
        //10s倒计时 对方没有加入房间 自动退出
        var count = maxTime
        waitOtherCountDownJob = mViewModel.countDownCoroutines(maxTime, onTick = { time ->
            if (count == 8) {
                setJoinCountDownView(true)
            }
            mBinding.tvJoinCountDownTime.text =
                getString(R.string.tip_wait_other_join_room_time, count)
            count--
        }, onFinish = {
            if (count <= 0) {
                LogX.i("$TAG, wait other join room time out ,channelID: $channelID")
                ToastUtil.show(getString(R.string.other_join_channel_failed))
                sendCallHangUpMessage(Constants.WebSocketParamValue.CALL_HANG_UP_REASON_USER_JOIN_TIMEOUT)
                finish()
            }
        })
    }

    private val mRtcEventHandler: IRtcEngineEventHandler = object : IRtcEngineEventHandler() {
        override fun onConnectionStateChanged(state: Int, reason: Int) {
            super.onConnectionStateChanged(state, reason)
            LogX.d("$TAG, onConnectionStateChanged: $state, $reason, channelID: $channelID")
            //TODO 出现一个现象 用户端秒挂的话 回调是 5用户离开频道， 3用户被服务器禁止
            if (state == 5 && reason == 3) {
                runOnUiThread {
                    finishActivity(true)
                }
                sendCallHangUpMessage(Constants.WebSocketParamValue.CALL_HANG_UP_REASON_BANNED_BY_SERVER)
            }
        }

        // 监听频道内的远端主播，获取主播的 uid 信息。
        // 每次主播进入的时候会回调这个监听
        override fun onJoinChannelSuccess(channel: String, uid: Int, elapsed: Int) {
            super.onJoinChannelSuccess(channel, uid, elapsed)
            LogX.d("$TAG,  onJoinChannelSuccess channelID:$channel")
            joinChannelCountDownJob?.cancel()
            joinChannelCountDownJob = null
            joinChannelSuc = true
            mRtcEngine?.adjustRecordingSignalVolume(200)
            mRtcEngine?.setAudioScenario(io.agora.rtc2.Constants.AUDIO_SCENARIO_GAME_STREAMING)
            cancelAnswerCountDown()
            countDownWaitOtherJoin()
            //上报加入房间socket
            WebSocketMessageSender.sendCallJoinRoomMessage(channel.toInt())
            // 更新WebSocketManager中的心跳数据
            WebSocketManager.getInstance().updateHeartbeatData(channel.toInt())
        }

        override fun onUserJoined(uid: Int, elapsed: Int) {
            LogX.d("$TAG, onUserJoined  uid:$uid, channelID: $channelID")
            waitOtherCountDownJob?.cancel()
            waitOtherCountDownJob = null
            runOnUiThread {
                if (uid != 0) {
                    setJoinCountDownView(false)
                    setupTimeView()
                    mRtcEngine?.apply {
                        val remoteSurfaceView = SurfaceView(baseContext)
                        mBinding.remoteVideoViewContainer.addView(remoteSurfaceView)
                        val videoCanvas =
                            VideoCanvas(remoteSurfaceView, VideoCanvas.RENDER_MODE_HIDDEN, uid)
                        videoCanvas.renderMode = 1
                        setupRemoteVideo(videoCanvas)
                    }
                }
            }
        }

        override fun onUserOffline(uid: Int, reason: Int) {
            super.onUserOffline(uid, reason)
            LogX.i("$TAG, onUserOffline uid : $uid, channelID: $channelID")
            sendCallHangUpMessage(Constants.WebSocketParamValue.CALL_HANG_UP_REASON_HUNG_UP_BY_OTHER)
            runOnUiThread {
                finishActivity(true)
            }
        }

        override fun onRemoteVideoStateChanged(
            uid: Int, state: Int, reason: Int, elapsed: Int
        ) {
            super.onRemoteVideoStateChanged(uid, state, reason, elapsed)
            LogX.i("$TAG, onRemoteVideoStateChanged : $state, channelID: $channelID")
            runOnUiThread { setVideoWindowVisi(state == REMOTE_VIDEO_STATE_STOPPED) }
        }

        override fun onFacePositionChanged(
            imageWidth: Int, imageHeight: Int, faceRectArr: Array<out AgoraFacePositionInfo?>
        ) {
            super.onFacePositionChanged(imageWidth, imageHeight, faceRectArr)
            if (faceRectArr.isEmpty()) { // 没有人脸
                runOnUiThread {
                    //前20s提示人脸检测
                    if (seconds < 20) {
                        ToastUtil.show(getString(R.string.tip_keep_face))
                    }
                }
            }
        }

        override fun onSnapshotTaken(
            uid: Int, filePath: String, width: Int, height: Int, errCode: Int
        ) {
            super.onSnapshotTaken(uid, filePath, width, height, errCode)
            if (errCode == 0) {
                lifecycleScope.launch {
                    val file = File(filePath)
                    if (file.exists()) {
                        FileUploadManager.upload(file).onSuccess {
                            if (channelID != 0 && it.accessUrl?.isNotEmpty() == true) {
                                mViewModel.screenUpload(channelID, it.accessUrl)
                            }
                        }.onError {
                            LogX.e("$TAG, onSnapshotTaken upload failed: $it, channelID: $channelID")
                        }
                    }
                }
            } else {
                LogX.e("$TAG, onSnapshotTaken failed: $errCode, channelID: $channelID")
            }
        }
    }

    private fun sendCallRefuseMessage(reasonType: Int, reason: String) {
        remoteCallInviteExtend?.let {
            WebSocketMessageSender.sendCallRefuseMessage(
                it.peerUID, it.callID, 0, reasonType, reason
            )
        }
    }

    private fun sendCallHangUpMessage(reasonType: Int) {
        WebSocketMessageSender.sendCallHangUpMessage(0, 0, channelID, reasonType)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        // 是否触发按键为back键
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            backEvent()
            true
        } else {
            // 如果不是back键正常响应
            super.onKeyDown(keyCode, event)
        }
    }

    private fun setVideoWindowVisi(isOpen: Boolean) {
        mBinding.ivRemoteCloseVideoHeader.makeVisible(isOpen)
        mBinding.viewHideRemote.makeVisible(isOpen)
    }

    private fun backEvent() {
        if (mBinding.bottomSendMsg.isVisible) {
            hideSendMsg()
        } else {
            showExitPopup()
        }
    }


    private fun showExitPopup() {
        if (exitTipPopupView == null) {
            exitTipPopupView = showNormalNewPopup(
                this,
                R.mipmap.ic_dialog_call,
                title = getString(R.string.btn_hangup),
                content = getString(R.string.dialog_content_hang_up),
                btnSure = getString(R.string.btn_connecting),
                btnCancel = getString(R.string.btn_hangup),
                mainColor = R.color.color_EC12E2,
                cancelBlock = {
                    if (joinChannelSuc) {
                        //通话过程中 主播主动挂断 上报挂断
                        sendCallHangUpMessage(Constants.WebSocketParamValue.CALL_HANG_UP_REASON_USER_HUNG_UP)
                    } else {
                        //没有通话 主播主动挂断 上报拒绝
                        sendCallRefuseMessage(
                            Constants.WebSocketParamValue.CALL_REFUSE_REASON_ANCHOR_REFUSE,
                            "anchor reject"
                        )

                    }
                    finishActivity(true, showToast = false)
                })
        } else {
            XPopup.Builder(this).asCustom(exitTipPopupView).show()
        }
    }

    private fun appendTimeStr(time: Int, always: Boolean) {
        if (time > 9) {
            timeString.append("$time:")
        } else if (time > 0) {
            timeString.append("0$time:")
        } else {
            if (always) {
                timeString.append("00:")
            }
        }
    }

    private val saveVideos: MutableMap<Int, File> = hashMapOf<Int, File>()

    private fun setupTimeView() {
        // 取消之前的任务
        cancelTimeViewJob()

        // 使用 lifecycleScope 确保与 Activity 生命周期绑定
        videoTimeJob = lifecycleScope.launch {
            try {
                while (isActive) {
                    // 更新时间显示
                    updateTimeDisplay()

                    // 处理截图上传（异步执行，避免阻塞主线程）
                    handleSnapshotUpload()

                    // 处理录制任务（异步执行，避免阻塞主线程）
                    handleRecordingTasks()

                    // 延迟1秒，使用 ensureActive 确保协程仍然活跃
                    delay(1000)
                    ensureActive()
                }
            } catch (e: CancellationException) {
                LogX.d("$TAG, setupTimeView 协程被取消")
                throw e // 重新抛出取消异常
            } catch (e: Exception) {
                LogX.e("$TAG, setupTimeView 发生异常: ${e.message}", e)
                // 异常情况下尝试重启时间更新（避免时间卡住）
                if (isActive) {
                    delay(1000)
                    setupTimeView()
                }
            }
        }
    }

    private fun cancelTimeViewJob() {
        videoTimeJob?.cancel()
        videoTimeJob = null
    }

    private suspend fun updateTimeDisplay() {
        // 确保在主线程更新UI
        withContext(Dispatchers.Main) {
            if (timeString.isNotEmpty()) {
                timeString.clear()
            }
            appendTimeStr(seconds / 3600, false)
            appendTimeStr((seconds % 3600) / 60, true)
            val secShow = seconds % 60
            val secStr = if (secShow > 9) secShow % 60 else "0$secShow"
            timeString.append(secStr)
            mBinding.timer.text = timeString
            seconds++
        }
    }

    private fun handleSnapshotUpload() {
        if (seconds == 5 || seconds % 10 == 0) {
            // 在IO线程执行截图操作，避免阻塞主线程
            lifecycleScope.launch(Dispatchers.IO) {
                try {
                    takeSnapshotScreen()
                } catch (e: Exception) {
                    LogX.e("$TAG, takeSnapshotScreen 异常: ${e.message}", e)
                }
            }
        }
    }

    private fun handleRecordingTasks() {
        // 在IO线程处理录制任务，避免阻塞主线程
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                handleRecordingStart()
                handleRecordingEnd()
            } catch (e: Exception) {
                LogX.e("$TAG, handleRecordingTasks 异常: ${e.message}", e)
            }
        }
    }

    private suspend fun handleRecordingStart() {
        val recordToStart = recordTimes.find { it.end > 0 && it.begin == seconds }
        recordToStart?.let {
            try {
                // 使用应用的隐藏目录保存视频，避免被相册扫描
                val videoDir = File(ContextHolder.context.filesDir, "video")
                if (!videoDir.exists()) {
                    videoDir.mkdirs()
                }
                val fileName = "VID_${System.currentTimeMillis()}.mp4"
                val saveFile = File(videoDir, fileName)

                // 将文件与它的结束时间关联起来，以便结束时能找到它
                saveVideos[it.end] = saveFile

                val mediaRecorderConfiguration = MediaRecorderConfiguration(
                    saveFile.absolutePath, // 存储路径
                    AgoraMediaRecorder.CONTAINER_MP4, AgoraMediaRecorder.STREAM_TYPE_BOTH, 120000, 0
                )

                LogX.i("VideoRecording, 开始录制，秒数: $seconds, 结束于: ${it.end}, 文件: ${saveFile.name}, channelID: $channelID")

                // 在主线程执行录制操作
                withContext(Dispatchers.Main) {
                    agoraMediaRecorder?.startRecording(mediaRecorderConfiguration)
                }
            } catch (e: Exception) {
                LogX.e("$TAG, handleRecordingStart 异常: ${e.message}", e)
            }
        }
    }

    private suspend fun handleRecordingEnd() {
        val recordToEnd = recordTimes.find { it.end > 0 && it.end == seconds }
        recordToEnd?.let {
            try {
                LogX.i("VideoRecording, 停止录制，秒数: $seconds, channelID: $channelID")

                // 在主线程停止录制
                withContext(Dispatchers.Main) {
                    agoraMediaRecorder?.stopRecording()
                }

                // 通过结束时间找到之前保存的文件，并将其从map中移除
                val saveFile = saveVideos.remove(it.end)
                if (saveFile != null && saveFile.exists()) {
                    LogX.i("VideoRecording, 准备上传文件: ${saveFile.name}, channelID: $channelID")

                    // 异步上传文件，避免阻塞
                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            FileUploadManager.upload(saveFile).onSuccess { uploadResult ->
                                deleteFile(saveFile)
                                if (channelID != 0 && uploadResult.objectKey?.isNotEmpty() == true) {
                                    mViewModel.videoUpload(channelID, uploadResult.objectKey)
                                }
                            }.onError { error ->
                                deleteFile(saveFile)
                                LogX.e("$TAG, video upload failed: $error")
                            }
                        } catch (e: Exception) {
                            deleteFile(saveFile)
                            LogX.e("$TAG, video upload exception: ${e.message}", e)
                        }
                    }
                } else {
                    LogX.e(
                        "VideoRecording",
                        "未找到需要上传的录制文件，结束时间: ${it.end}, channelID: $channelID"
                    )
                }
            } catch (e: Exception) {
                LogX.e("$TAG, handleRecordingEnd 异常: ${e.message}", e)
            }
        }
    }

    private fun deleteFile(file: File?) {
        if (file != null && file.exists()) {
            file.delete()
        }
    }

    private fun takeSnapshotScreen() {
        val imageDir = File(ContextHolder.context.filesDir, "img")
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        }
        val fileName = "img_${System.currentTimeMillis()}.png"
        val saveFile = File(imageDir, fileName)
        mRtcEngine?.takeSnapshot(0, saveFile.absolutePath)
    }

    private fun appendList(messages: MutableList<VideoMessageBean>) {
        mBinding.messageList.append<VideoMessageItem>(messages) { data ->
            videoMessageBean = data as VideoMessageBean
        }
        mBinding.messageList.smoothScrollToPosition(mBinding.messageList.dslAdapter.itemCount - 1)
    }

    override fun initViewEvents() {
        giftViewModel.pageEvents.observeEvent(this) { event ->
            when (event) {
                is GiftRequestEvent.GiftSendSuc -> {
                    appendList(
                        mutableListOf(
                            VideoMessageBean(
                                name = getString(R.string.label_me),
                                giftImage = event.giftBean.icon,
                                giftId = event.giftBean.id,
                                isAsk = true
                            )
                        )
                    )
                }

                is GiftRequestEvent.GiftSendFailed -> {
                    LogX.i("sendGiftFailed ${event.msg}, channelID: $channelID")
                    ToastUtil.show(event.msg)
                }

                else -> {}
            }
        }
    }


    fun getCallId(): Int {
        return channelID
    }

    fun finishActivity(isToResult: Boolean, showToast: Boolean = true) {
        if (showToast) {
            ToastUtil.show(getString(R.string.other_party_hungup))
        }
        // 清除通话状态
        CallStateManager.endCall()
        if (isToResult) {
//            bean?.duration = bindingView?.timer?.text.toString()
//            bean?.channelID = channelID
//            jump(VideoResultActivity::class.java, Bundle().apply {
//                val tmpBean = GsonUtil.GsonString(bean)
//                putString("bean", tmpBean)
//                putString("userRole", bean?.userRole)
//            })
        }
//        if (uid > 0) {
//            val text = bindingView?.timer?.text.toString()
//            if (text.isNotEmpty()) {
//                sendVideoMsg(false, text)
//            }
//        }
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清除通话状态
        CallStateManager.endCall()
        // 取消所有协程任务
        cancelAllJobs()
        agoraMediaRecorder?.release()
        releaseRing()
        // 在Activity销毁时重置WebSocketManager中的心跳数据
        WebSocketManager.getInstance().updateHeartbeatData(0)
        mRtcEngine?.apply {
            stopPreview() // 停止本地视频预览
            leaveChannel()// 离开频道
        }
        mRtcEngine = null
        RtcEngine.destroy()// 销毁引擎
    }

    private fun cancelAllJobs() {
        cancelAnswerCountDown()
        cancelTimeViewJob()
        joinChannelCountDownJob?.cancel()
        joinChannelCountDownJob = null
        waitOtherCountDownJob?.cancel()
        waitOtherCountDownJob = null
    }
}