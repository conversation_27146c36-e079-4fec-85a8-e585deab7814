package com.mobile.anchor.app.data.network.ktnet.error

import com.bdc.android.library.ktnet.exception.ParseException
import com.mobile.anchor.app.data.network.ktnet.NetConstant
import kotlinx.coroutines.TimeoutCancellationException
import retrofit2.HttpException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.util.concurrent.TimeoutException

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 3:09 下午
 * @description： 异常扩展
 */
val Throwable.code: Int
    get() {
        val errorCode = when (this) {
            is HttpException -> this.code() // Http状态码异常
            is ParseException -> this.errorCode     // 业务code异常
            else -> NetConstant.DEFAULT_ERROR_CODE
        }
        return try {
            errorCode
        } catch (e: Exception) {
            NetConstant.DEFAULT_ERROR_CODE
        }
    }

val Throwable.msg: String
    get() {
        return if (this is HttpException) {
            "Network error please try again"
        } else if (this is UnknownHostException) {
            "Unknown host please try again"
        } else if (this is SocketTimeoutException  //okHttp全局设置超时
            || this is TimeoutException     //方法超时
            || this is TimeoutCancellationException  //协程超时
        ) {
            "Network connection timeout please try again"
        } else if (this is ConnectException) {
            "Network connection error please try again"
        } else if (this is ParseException) {
            // ParseException异常表明请求成功，但是数据不正确 msg为空，显示code
            message ?: "$errorCode"
        } else {
            "Network unknown error please try again"
        }
    }

