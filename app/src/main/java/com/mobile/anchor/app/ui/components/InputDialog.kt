package com.mobile.anchor.app.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.theme.Surface

/**
 * 带输入框的弹框组件
 *
 * @param visible 是否显示弹框
 * @param title 弹框标题
 * @param content 弹框内容描述
 * @param inputValue 输入框的值
 * @param inputPlaceholder 输入框占位符
 * @param inputLabel 输入框标签
 * @param maxLength 最大输入长度，0表示无限制
 * @param keyboardType 键盘类型
 * @param confirmText 确认按钮文字，默认"确定"
 * @param cancelText 取消按钮文字，默认"取消"
 * @param showCancelButton 是否显示取消按钮，默认true
 * @param onDismiss 关闭弹框回调
 * @param onInputChange 输入内容变化回调
 * @param onConfirm 确认按钮回调，参数为输入的内容
 * @param onCancel 取消按钮回调，默认与onDismiss相同
 * @param inputValidator 输入验证器，返回错误信息，null表示验证通过
 */
@Composable
fun InputDialog(
    visible: Boolean,
    title: String = "",
    content: String = "",
    inputValue: String = "",
    inputPlaceholder: String = "",
    inputLabel: String = "",
    maxLength: Int = 0,
    keyboardType: KeyboardType = KeyboardType.Text,
    confirmText: String = "Confirm",
    cancelText: String = "Cancel",
    showCancelButton: Boolean = true,
    showCharacterCount: Boolean = false,
    onDismiss: () -> Unit,
    onInputChange: (String) -> Unit,
    onConfirm: (String) -> Unit,
    onCancel: () -> Unit = onDismiss,
    inputValidator: ((String) -> String?)? = null
) {
    AnimatedVisibility(visible,enter = fadeIn(), exit = fadeOut()) {
        var currentInput by remember(inputValue) { mutableStateOf(inputValue) }
        var errorMessage by remember { mutableStateOf<String?>(null) }
        val focusRequester = remember { FocusRequester() }
        val keyboardController = LocalSoftwareKeyboardController.current

        // 验证输入
        LaunchedEffect(currentInput) {
            errorMessage = inputValidator?.invoke(currentInput)
            onInputChange(currentInput)
        }

        // 自动聚焦
        LaunchedEffect(visible) {
            if (visible) {
                // 延迟一下确保组件完全初始化
                kotlinx.coroutines.delay(100)
                try {
                    focusRequester.requestFocus()
                } catch (e: Exception) {
                    // 如果聚焦失败就忽略，用户可以手动点击
                    println("InputDialog: 自动聚焦失败，用户可以手动点击输入")
                }
            }
        }

        Box(
            modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
        ) {
            // 背景遮罩
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable { onDismiss() })

            Dialog(
                onDismissRequest = onDismiss, properties = DialogProperties(
                    dismissOnBackPress = true,
                    dismissOnClickOutside = true,
                    usePlatformDefaultWidth = false,
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
                ) {

                    // 弹框内容
                    Card(
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                            .padding(horizontal = 16.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Surface)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 24.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // 标题
                            if (title.isNotEmpty()) {
                                Text(
                                    text = title,
                                    color = Color.White,
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Bold,
                                    textAlign = TextAlign.Center,
                                    modifier = Modifier.fillMaxWidth()
                                )

                                Spacer(modifier = Modifier.height(16.dp))
                            }

                            // 内容描述
                            if (content.isNotEmpty()) {
                                Text(
                                    text = content,
                                    color = Color.White.copy(alpha = 0.8f),
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Normal,
                                    textAlign = TextAlign.Center,
                                    lineHeight = 24.sp,
                                    modifier = Modifier.fillMaxWidth()
                                )

                                Spacer(modifier = Modifier.height(20.dp))
                            }

                            OutlinedTextField(
                                value = currentInput,
                                onValueChange = { currentInput = it },
                                label = { Text(inputPlaceholder, color = Color.Gray) },
                                colors = OutlinedTextFieldDefaults.colors(
                                    focusedTextColor = Color.White,
                                    unfocusedTextColor = Color.Gray,
                                    focusedBorderColor = Primary,
                                    unfocusedBorderColor = Color.Gray,
                                    focusedContainerColor = Color.Transparent,
                                    unfocusedContainerColor = Color.Transparent,
                                    cursorColor = Primary
                                ),
                                textStyle = TextStyle(
                                    fontSize = 16.sp, fontWeight = FontWeight.Normal
                                ),
                                shape = RoundedCornerShape(12.dp),
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 16.dp)
                            )

                            // 字符计数和错误信息
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 8.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // 错误信息
                                if (errorMessage != null) {
                                    Text(
                                        text = errorMessage!!,
                                        color = Color.Red,
                                        fontSize = 12.sp,
                                        modifier = Modifier.weight(1f)
                                    )
                                } else {
                                    Spacer(modifier = Modifier.weight(1f))
                                }

                                // 字符计数
                                if (maxLength > 0 && showCharacterCount) {
                                    Text(
                                        text = "${currentInput.length}/$maxLength",
                                        color = Color.White.copy(alpha = 0.6f),
                                        fontSize = 12.sp
                                    )

                                    Spacer(modifier = Modifier.height(24.dp))
                                }
                            }

                            // 按钮区域
                            if (showCancelButton) {
                                // 两个按钮横向排列
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                                ) {

                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.End
                                    ) {
                                        // 取消按钮
                                        TextButton(onClick = {
                                            keyboardController?.hide()
                                            onCancel()
                                        }) {
                                            Text(
                                                text = cancelText,
                                                fontSize = 16.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }

                                        // 确认按钮
                                        TextButton(onClick = {
                                            keyboardController?.hide()
                                            if (errorMessage == null) {
                                                onConfirm(currentInput)
                                            }
                                        }) {
                                            Text(
                                                text = confirmText,
                                                color = Primary,
                                                fontSize = 16.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }
                                    }
                                }
                            } else {
                                // 只显示确认按钮
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.End
                                ) {
                                    TextButton(onClick = {
                                        keyboardController?.hide()
                                        if (errorMessage == null) {
                                            onConfirm(currentInput)
                                        }
                                    }) {
                                        Text(
                                            text = confirmText,
                                            color = Primary,
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun InputDialogPreview() {
    AnchorTheme {
        InputDialog(
            visible = true,
            title = "输入名称",
            content = "请输入新的名称",
            inputPlaceholder = "请输入...",
            inputLabel = "名称",
            maxLength = 20,
            onDismiss = {},
            onInputChange = {},
            onConfirm = {})
    }
}

@Preview
@Composable
fun InputDialogNoCancelButtonPreview() {
    AnchorTheme {
        InputDialog(
            visible = true,
            title = "输入名称",
            content = "请输入新的名称",
            inputPlaceholder = "请输入...",
            inputLabel = "名称",
            maxLength = 20,
            showCancelButton = false,
            onDismiss = {},
            onInputChange = {},
            onConfirm = {})
    }
}
