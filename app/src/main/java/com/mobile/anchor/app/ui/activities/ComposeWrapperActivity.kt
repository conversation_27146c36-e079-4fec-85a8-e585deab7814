package com.mobile.anchor.app.ui.activities

import android.graphics.Color
import android.os.Bundle
import androidx.activity.SystemBarStyle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.Composable
import androidx.core.view.WindowCompat
import androidx.lifecycle.ViewModel
import androidx.viewbinding.ViewBinding
import com.bdc.android.library.base.activity.BaseCoreActivity

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/14 20:30
 * @description :
 */
open class ComposeWrapperActivity : BaseCoreActivity<ViewBinding, ViewModel>() {
    override fun getLayoutId(): Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge(
            SystemBarStyle.dark(Color.TRANSPARENT), SystemBarStyle.dark(Color.TRANSPARENT)
        )
        WindowCompat.setDecorFitsSystemWindows(window, false)
        setContent {
            BuildContent()
        }
    }

    override fun isImmerse(): Boolean = true

    @Composable
    open fun BuildContent() = Unit
}