package com.mobile.anchor.app.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import androidx.activity.ComponentActivity
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 图片选择工具类
 * 支持相机拍照和相册选择，兼容Android最新API
 */
class ImagePickerUtils(
    private val onImageSelected: (Uri?) -> Unit, private val onPermissionDenied: () -> Unit = {}
) {
    private val activity: ComponentActivity = ActivityUtils.getCurrent() as? ComponentActivity
        ?: throw IllegalStateException("ImagePickerUtils 必须在 ComponentActivity 中使用")
    private var currentPhotoUri: Uri? = null

    // 权限请求启动器
    private val permissionLauncher = activity.registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            when (pendingAction) {
                PendingAction.CAMERA -> launchCamera()
                PendingAction.GALLERY -> launchGallery()
                else -> {}
            }
        } else {
            onPermissionDenied()
        }
        pendingAction = PendingAction.NONE
    }

    // 相机启动器
    private val cameraLauncher = activity.registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        if (success) {
            onImageSelected(currentPhotoUri)
        } else {
            onImageSelected(null)
        }
    }

    // 相册启动器
    private val galleryLauncher = activity.registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        onImageSelected(uri)
    }

    // Google Photos启动器（使用ACTION_PICK）
    private val photoPickerLauncher = activity.registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val uri = result.data?.data
            onImageSelected(uri)
        } else {
            onImageSelected(null)
        }
    }

    private var pendingAction = PendingAction.NONE

    private enum class PendingAction {
        NONE, CAMERA, GALLERY
    }

    /**
     * 从相机拍照
     */
    fun pickFromCamera() {
        if (checkCameraPermissions()) {
            launchCamera()
        } else {
            pendingAction = PendingAction.CAMERA
            requestCameraPermissions()
        }
    }

    /**
     * 从相册选择
     */
    fun pickFromGallery() {
        if (checkStoragePermissions()) {
            launchGallery()
        } else {
            pendingAction = PendingAction.GALLERY
            requestStoragePermissions()
        }
    }

    /**
     * 从Google Photos选择
     */
    fun pickFromGooglePhotos() {
        if (checkStoragePermissions()) {
            launchGooglePhotos()
        } else {
            pendingAction = PendingAction.GALLERY
            requestStoragePermissions()
        }
    }

    private fun launchCamera() {
        try {
            currentPhotoUri = createImageUri()
            currentPhotoUri?.let { uri ->
                cameraLauncher.launch(uri)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            onImageSelected(null)
        }
    }

    private fun launchGallery() {
        try {
            galleryLauncher.launch("image/*")
        } catch (e: Exception) {
            e.printStackTrace()
            onImageSelected(null)
        }
    }

    private fun launchGooglePhotos() {
        try {
            val intent =
                Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI).apply {
                    type = "image/*"
                    // 尝试指定Google Photos
                    setPackage("com.google.android.apps.photos")
                }

            // 如果Google Photos不可用，回退到系统相册
            if (intent.resolveActivity(activity.packageManager) == null) {
                intent.setPackage(null)
            }

            photoPickerLauncher.launch(intent)
        } catch (e: Exception) {
            e.printStackTrace()
            // 回退到普通相册选择
            launchGallery()
        }
    }

    private fun createImageUri(): Uri {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val imageFileName = "JPEG_${timeStamp}_"
        val storageDir = File(activity.cacheDir, "images")

        if (!storageDir.exists()) {
            storageDir.mkdirs()
        }

        val imageFile = File.createTempFile(imageFileName, ".jpg", storageDir)

        return FileProvider.getUriForFile(
            activity, "${activity.packageName}.fileprovider", imageFile
        )
    }

    private fun checkCameraPermissions(): Boolean {
        return ContextCompat.checkSelfPermission(
            activity, Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun checkStoragePermissions(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用新的媒体权限
            ContextCompat.checkSelfPermission(
                activity, Manifest.permission.READ_MEDIA_IMAGES
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 12 及以下使用存储权限
            ContextCompat.checkSelfPermission(
                activity, Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }

    private fun requestCameraPermissions() {
        permissionLauncher.launch(arrayOf(Manifest.permission.CAMERA))
    }

    private fun requestStoragePermissions() {
        val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(Manifest.permission.READ_MEDIA_IMAGES)
        } else {
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
        }
        permissionLauncher.launch(permissions)
    }

    companion object {
        /**
         * 检查是否有相机权限
         */
        fun hasCameraPermission(context: Context): Boolean {
            return ContextCompat.checkSelfPermission(
                context, Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED
        }

        /**
         * 检查是否有存储权限
         */
        fun hasStoragePermission(context: Context): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                ContextCompat.checkSelfPermission(
                    context, Manifest.permission.READ_MEDIA_IMAGES
                ) == PackageManager.PERMISSION_GRANTED
            } else {
                ContextCompat.checkSelfPermission(
                    context, Manifest.permission.READ_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED
            }
        }
    }
}
