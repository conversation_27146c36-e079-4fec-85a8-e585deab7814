package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.mobile.anchor.app.ui.theme.OnBackground

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/6/6 15:50
 * @description :
 */
@Composable
fun CardView(
    modifier: Modifier = Modifier.fillMaxWidth(), content: @Composable () -> Unit
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = OnBackground),
        shape = RoundedCornerShape(16.dp),
    ) {
        content()
    }
}