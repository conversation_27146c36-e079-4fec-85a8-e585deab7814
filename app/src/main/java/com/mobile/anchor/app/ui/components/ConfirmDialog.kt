package com.mobile.anchor.app.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.DisabledColor
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.theme.Surface

/**
 * 确认弹框组件
 *
 * @param visible 是否显示弹框
 * @param title 弹框标题
 * @param content 弹框内容
 * @param confirmText 确认按钮文字，默认"确定"
 * @param cancelText 取消按钮文字，默认"取消"
 * @param showConfirmButton 是否显示确认按钮，默认true
 * @param showCancelButton 是否显示取消按钮，默认true
 * @param onDismiss 关闭弹框回调
 * @param onConfirm 确认按钮回调
 * @param onCancel 取消按钮回调，默认与onDismiss相同
 */
@Composable
fun ConfirmDialog(
    visible: Boolean,
    title: String = "Remind",
    content: String = "",
    confirmText: String = "Confirm",
    cancelText: String = "Cancel",
    showConfirmButton: Boolean = true,
    showCancelButton: Boolean = true,
    dismissOnBackPress: Boolean = true,
    dismissOnClickOutside: Boolean = true,
    onDismiss: () -> Unit = {},
    onConfirm: () -> Unit = {},
    onCancel: () -> Unit = onDismiss
) {
    AnimatedVisibility(visible, enter = fadeIn(), exit = fadeOut()) {
        Box(
            modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
        ) {
            // 背景遮罩
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable { onDismiss() })

            Dialog(
                onDismissRequest = onDismiss, properties = DialogProperties(
                    dismissOnBackPress = dismissOnBackPress,
                    dismissOnClickOutside = dismissOnClickOutside,
                    usePlatformDefaultWidth = false,
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
                ) {
                    // 弹框内容
                    Card(
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                            .padding(horizontal = 16.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Surface)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 24.dp, start = 24.dp, end = 24.dp, bottom = 10.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // 标题
                            if (title.isNotEmpty()) {
                                Text(
                                    text = title,
                                    color = Color.White,
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Bold,
                                    textAlign = TextAlign.Center,
                                    modifier = Modifier.fillMaxWidth()
                                )

                                if (content.isNotEmpty()) {
                                    Spacer(modifier = Modifier.height(16.dp))
                                }
                            }

                            // 内容
                            if (content.isNotEmpty()) {
                                Text(
                                    text = content,
                                    color = Color.White.copy(alpha = 0.8f),
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Normal,
                                    textAlign = TextAlign.Center,
                                    lineHeight = 24.sp,
                                    modifier = Modifier.fillMaxWidth()
                                )
                            }

                            Spacer(modifier = Modifier.height(24.dp))

                            // 按钮区域
                            if (showConfirmButton || showCancelButton) {
                                when {
                                    showConfirmButton && showCancelButton -> {
                                        // 两个按钮横向排列
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.End
                                        ) {
                                            // 取消按钮
                                            TextButton(onClick = onCancel) {
                                                Text(
                                                    text = cancelText,
                                                    color = DisabledColor,
                                                    fontSize = 16.sp,
                                                    fontWeight = FontWeight.Medium
                                                )
                                            }

                                            // 确认按钮
                                            TextButton(onClick = onConfirm) {
                                                Text(
                                                    text = confirmText,
                                                    color = Primary,
                                                    fontSize = 16.sp,
                                                    fontWeight = FontWeight.Medium
                                                )
                                            }
                                        }
                                    }

                                    showConfirmButton -> {
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.End
                                        ) {
                                            // 只显示确认按钮
                                            TextButton(onClick = onConfirm) {
                                                Text(
                                                    text = confirmText,
                                                    color = Primary,
                                                    fontSize = 16.sp,
                                                    fontWeight = FontWeight.Medium,
                                                )
                                            }
                                        }
                                    }

                                    showCancelButton -> {
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.End
                                        ) {
                                            // 只显示取消按钮
                                            TextButton(onClick = onCancel) {
                                                Text(
                                                    text = cancelText,
                                                    fontSize = 16.sp,
                                                    fontWeight = FontWeight.Medium
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun ConfirmDialogPreview() {
    AnchorTheme {
        ConfirmDialog(
            visible = true,
            title = "确认删除",
            content = "确定要删除这个项目吗？删除后无法恢复。",
            onDismiss = {},
            onConfirm = {})
    }
}

@Preview
@Composable
fun ConfirmDialogSingleButtonPreview() {
    AnchorTheme {
        ConfirmDialog(
            visible = true,
            title = "提示",
            content = "操作已完成",
            showCancelButton = false,
            onDismiss = {},
            onConfirm = {})
    }
}

@Preview
@Composable
fun ConfirmDialogSingle2ButtonPreview() {
    AnchorTheme {
        ConfirmDialog(
            visible = true,
            title = "提示",
            content = "操作已完成",
            showConfirmButton = false,
            showCancelButton = true,
            onDismiss = {},
            onConfirm = {})
    }
}

@Preview
@Composable
fun ConfirmDialogNoButtonPreview() {
    AnchorTheme {
        ConfirmDialog(
            visible = true,
            title = "加载中",
            content = "请稍候...",
            showConfirmButton = false,
            showCancelButton = false,
            onDismiss = {},
            onConfirm = {})
    }
}
