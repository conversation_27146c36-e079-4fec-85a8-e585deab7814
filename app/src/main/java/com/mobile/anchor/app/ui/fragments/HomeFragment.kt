package com.mobile.anchor.app.ui.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.navigation.compose.rememberNavController
import com.mobile.anchor.app.navigation.Screen
import com.mobile.anchor.app.ui.screens.home.HomeScreen
import com.mobile.anchor.app.ui.theme.AnchorTheme

/**
 * 首页Fragment
 * 使用Compose实现UI，支持内部导航到二级页面
 */
class HomeFragment : Fragment() {

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                AnchorTheme {
                    HomeFragmentContent()
                }
            }
        }
    }

    @Composable
    private fun HomeFragmentContent() {
        HomeScreen()
    }

    companion object {
        fun newInstance(): HomeFragment {
            return HomeFragment()
        }
    }
}
