package com.mobile.anchor.app.data.service.anchor

import com.bdc.android.library.ktnet.coroutines.Await
import com.mobile.anchor.app.data.model.CallHistoryBean
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.data.service.PageResponse
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface AnchorService {
    @GET("/api/v1/anchor/call/list")
    suspend fun getCallList(@Query("cursor") cursor: String, @Query("size") size: Int, @Query("type") type: Int): Await<CallHistoryBean?> //通话记录

    @GET("/api/v1/anchor/relation/{name}/list")
    suspend fun getUserList(@Path("name") name: String, @Query("cursor") cursor: String, @Query("size") size: Int): Await<PageResponse<UserBean>?> //关注列表/粉丝列表/拉黑列表
}