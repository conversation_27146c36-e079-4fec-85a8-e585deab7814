package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.DisabledColor
import com.mobile.anchor.app.ui.theme.ErrorColor
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.theme.SuccessColor

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/7 19:55
 * @description : 统一的按钮组件，支持加载动画、图标等功能
 */

// 渐变色定义
val PrimaryGradient = Brush.linearGradient(
    colors = listOf(
        Color(0xFFEC12E2), // 起始颜色
        Color(0xFF8531FF)  // 结束颜色
    )
)

val DisabledGradient = Brush.linearGradient(
    colors = listOf(
        DisabledColor, DisabledColor
    )
)

/**
 * 主要按钮 - 带加载动画和渐变背景
 */
@Composable
fun AnchorButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false,
    useGradient: Boolean = true,
    containerColor: Color = Primary,
    disabledContainerColor: Color = Color(0xFF666666),
    contentColor: Color = Color.White,
    fontSize: TextUnit = 16.sp,
    fontWeight: FontWeight = FontWeight.Medium,
    cornerRadius: Dp = 50.dp,
    height: Dp = 48.dp,
    icon: ImageVector? = null
) {
    val isClickable = enabled && !isLoading

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(height)
            .clip(RoundedCornerShape(cornerRadius))
            .background(
                brush = if (useGradient && isClickable) {
                    PrimaryGradient
                } else if (useGradient && !isClickable) {
                    DisabledGradient
                } else {
                    Brush.linearGradient(
                        colors = listOf(
                            if (isClickable) containerColor else disabledContainerColor,
                            if (isClickable) containerColor else disabledContainerColor
                        )
                    )
                }
            )
            .clickable(enabled = isClickable) { onClick() }, contentAlignment = Alignment.Center
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                color = contentColor, modifier = Modifier.size(20.dp)
            )
        } else {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                icon?.let {
                    Icon(
                        imageVector = it,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp),
                        tint = contentColor
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text(
                    text = text, color = contentColor, fontSize = fontSize, fontWeight = fontWeight
                )
            }
        }
    }
}

/**
 * 次要按钮 - 轮廓样式
 */
@Composable
fun AnchorOutlinedButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false,
    borderColor: Color = Color.White,
    contentColor: Color = Color.White,
    fontSize: TextUnit = 16.sp,
    fontWeight: FontWeight = FontWeight.Medium,
    cornerRadius: Dp = 28.dp,
    height: Dp = 49.dp,
    icon: ImageVector? = null
) {
    OutlinedButton(
        onClick = onClick,
        enabled = enabled && !isLoading,
        modifier = modifier
            .fillMaxWidth()
            .height(height),
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = contentColor, disabledContentColor = contentColor.copy(alpha = 0.5f)
        ),
        border = ButtonDefaults.outlinedButtonBorder.copy(
            brush = androidx.compose.ui.graphics.SolidColor(borderColor)
        ),
        shape = RoundedCornerShape(cornerRadius)
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                color = contentColor, modifier = Modifier.size(20.dp)
            )
        } else {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                icon?.let {
                    Icon(
                        imageVector = it,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp),
                        tint = contentColor
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text(
                    text = text, color = contentColor, fontSize = fontSize, fontWeight = fontWeight
                )
            }
        }
    }
}

/**
 * 小尺寸按钮
 */
@Composable
fun AnchorSmallButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false,
    useGradient: Boolean = true,
    containerColor: Color = Primary,
    disabledContainerColor: Color = Color(0xFF666666),
    contentColor: Color = Color.White
) {
    AnchorButton(
        text = text,
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        isLoading = isLoading,
        useGradient = useGradient,
        containerColor = containerColor,
        disabledContainerColor = disabledContainerColor,
        contentColor = contentColor,
        fontSize = 14.sp,
        cornerRadius = 25.dp,
        height = 40.dp
    )
}

/**
 * 危险按钮（红色）
 */
@Composable
fun AnchorDangerButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false
) {
    Button(
        onClick = onClick,
        enabled = enabled && !isLoading,
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = ErrorColor,
            disabledContainerColor = DisabledColor,
            contentColor = Color.White
        ),
        shape = RoundedCornerShape(50.dp)
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                color = Color.White, modifier = Modifier.size(20.dp)
            )
        } else {
            Text(
                text = text, color = Color.White, fontSize = 16.sp, fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * 成功按钮（绿色）
 */
@Composable
fun AnchorSuccessButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false
) {
    Button(
        onClick = onClick,
        enabled = enabled && !isLoading,
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = SuccessColor,
            disabledContainerColor = DisabledColor,
            contentColor = Color.White
        ),
        shape = RoundedCornerShape(50.dp)
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                color = Color.White, modifier = Modifier.size(20.dp)
            )
        } else {
            Text(
                text = text, color = Color.White, fontSize = 16.sp, fontWeight = FontWeight.Medium
            )
        }
    }
}

// 预览
@Preview(showBackground = true)
@Composable
fun AnchorButtonPreview() {
    AnchorTheme {
        androidx.compose.foundation.layout.Column(
            modifier = Modifier.padding(16.dp), verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            AnchorButton(
                text = "Primary Button", onClick = { })

            AnchorButton(
                text = "Loading Button", onClick = { }, isLoading = true
            )

            AnchorOutlinedButton(
                text = "Outlined Button", onClick = { })

            AnchorSmallButton(
                text = "Small Button", onClick = { })

            AnchorDangerButton(
                text = "Danger Button", onClick = { })

            AnchorSuccessButton(
                text = "Success Button", onClick = { })
        }
    }
}