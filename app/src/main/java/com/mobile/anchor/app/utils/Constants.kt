package com.mobile.anchor.app.utils

import com.mobile.anchor.app.manager.DataStoreManager
import java.net.HttpURLConnection.HTTP_UNAUTHORIZED

object Constants {
    val TOKEN = "Authorization"
    val USER = "User"
    const val SOURCE_PAGE_RECOMMEND = "1"
    const val SOURCE_PAGE_POPULAR = "2"
    const val SOURCE_PAGE_NEW = "3"
    const val SOURCE_PAGE_FOCUS = "4"
    const val SOURCE_PAGE_MESSAGE = "5"
    const val SOURCE_PAGE_DETAIL = "6"
    const val SOURCE_TYPE_VIDEO = "1"
    const val SOURCE_TYPE_MATCH = "2"
    const val GENDER_FEMALE = "1"
    const val GENDER_MALE = "0"
    const val INTENT_PARAM_KEY_ANCHOR_INFO = "anchorInfo"
    const val INTENT_PARAM_KEY_FROM_MATCH = "fromMatch"
    const val INTENT_PARAM_KEY_CHANNEL_ID = "channelId"
    const val INTENT_PARAM_KEY_ANCHOR_UID = "anchorUid"
    const val ROLE_USER = "1" //普通用户角色
    const val ROLE_ANCHOR = "2" //主播角色
    const val PUSH_TYPE_ANCHOR_REJECT = "5" //主播拒绝
    const val PUSH_TYPE_ANCHOR_CANCEL = "4" //取消拨打
    const val PUSH_TYPE_BLACK_ANCHOR = "black_anchor"//拉黑主播
    const val PUSH_TYPE_REQUEST_GIFT = "request_gift" //收到礼物

    const val PUSH_TYPE_REQUEST_QUICK_GIFT = "request_quick_gift" //收到快捷礼物
    const val PUSH_TYPE_NOTIFY_CALL = "14" //后台推送的视频
    const val PUSH_TYPE_TEXT_MESSAGE = "text_message" //普通文本
    const val PAYMENT_SUCCESS = "payment_success"//支付成功
    const val SUBSCRIBE_SUCCESS = "subscribe_success"//订阅成功
    const val ANCHOR_FILE_TYPE_PHOTO = "1" //查看主播文件图片
    const val ANCHOR_FILE_TYPE_VIDEO = "2"//查看主播文件视频
    const val INTENT_PARAM_USE_MATCH_CARD = "useMatchCard"
    const val INTENT_PARAM_VIDEO_SOURCE = "videoSource"
    const val INTENT_PARAM_IS_INCOMING_CALLING = "incomingCall"
    const val INTENT_PARAM_TAB_INDEX = "tab_index"
    const val INTENT_PARAM_VIDEO_URL = "video_url"

    const val INTENT_PARAM_RECORD_ID = "record_id"
    const val INTENT_PARAM_FROM_MATCH = "from_match"
    const val INTENT_PARAM_ISNEEDMUTE = "isNeedMute"
    const val INTENT_PARAM_ISNEEDBAL = "isNeedBal"
    const val INTENT_PARAM_VIDEO_PRICE = "videoPrice"


    const val VIDEO_SOURCE_NORMAL = "1" //普通通话
    const val VIDEO_SOURCE_MATCH = "2" //匹配通话
    const val VIDEO_SOURCE_INCOMING_AIB = "3"//模拟主播来电拨打

    const val FOLLOW_FLAG_FOLLOWED = 1  //关注
    const val FOLLOW_FLAG_UNFOLLOWED = 0
    const val FOLLOW_FLAG_FOLLOW_EACH_OTHER = 3 //互相关注

    const val VIDEO_DEFAULT_EXIT_TIME = 30 //默认视频无接听超时时间
    const val VIDEO_DEFAULT_PRICE = 60 //默认视频消耗的砖石
    const val RONG_YUN_ID_SYSTEM = "10000" //系统通知ID
    const val RONG_YUN_ID_CUSTOM_SERVICE = "666666" //客诉通知ID

    const val TOKEN_EXPIRED = "token_expired"

    const val MESSAGE_EXPEND_KEY_BLOCK = "isBlocked"

    const val GLOBAL_PAGE_SIZE = 20

    object ErrorCode {
        const val USER_NOT_EXIST = 1001
        const val USER_BLOCK = 1014
        const val TOKEN_EXPIRED = HTTP_UNAUTHORIZED
    }

    object Agreement {
        val H5_DOMAIN = "https://s3.yocovip.com/anchor/protocol/"

        //用户注册协议
        val REGISTRATION_URL = "${H5_DOMAIN}PrivacyPolicy.html"

        //隐私政策
        val PRIVACY_URL = "${H5_DOMAIN}Useragreement.html"
    }

    object PushCmd {
        val PUSH_CMD_REMOTE_CALL_INVITE = "remote_call_invite"
        val PUSH_CMD_REMOTE_CALL_INVITE_CANCEL = "remote_call_invite_cancel"
        val PUSH_CMD_REMOTE_PULL_LOG = "remote_pull_log"
        val PUSH_CMD_REMOTE_CALL_SETTLE = "remote_call_settle"
        val PUSH_CMD_REMOTE_SMALL_GIFT = "remote_small_gift"
    }

    object Currency {
        const val USD = "USD"
        const val EUR = "EUR"
        const val GBP = "GBP"
        const val JPY = "JPY"
        const val CNY = "CNY"
        const val KRW = "KRW"
        const val VND = "VND"
        const val THB = "THB"
        const val PHP = "PHP"
        const val SGD = "SGD"
        const val MYR = "MYR"
        const val IDR = "IDR"
        const val TWD = "TWD"
        const val HKD = "HKD"
        const val DEFAULT = USD

        // 货币符号映射
        val SYMBOLS = mapOf(
            USD to "$",
            EUR to "€",
            GBP to "£",
            JPY to "¥",
            CNY to "¥",
            KRW to "₩",
            VND to "₫",
            THB to "฿",
            PHP to "₱",
            SGD to "$",
            MYR to "RM",
            IDR to "Rp",
            TWD to "NT$",
            HKD to "$"
        )

        // 货币显示名称映射
        val DISPLAY_NAMES = mapOf(
            USD to "US Dollar",
            EUR to "Euro",
            GBP to "British Pound",
            JPY to "Japanese Yen",
            CNY to "Chinese Yuan",
            KRW to "Korean Won",
            VND to "Vietnamese Dong",
            THB to "Thai Baht",
            PHP to "Philippine Peso",
            SGD to "Singapore Dollar",
            MYR to "Malaysian Ringgit",
            IDR to "Indonesian Rupiah",
            TWD to "New Taiwan Dollar",
            HKD to "Hong Kong Dollar"
        )

        // 获取所有支持的货币列表
        fun getSupportedCurrencies(): List<String> =
            listOf(USD, EUR, GBP, JPY, CNY, KRW, VND, THB, PHP, SGD, MYR, IDR, TWD, HKD)

        // 获取货币符号
        fun getSymbol(currency: String): String = SYMBOLS[currency] ?: "$"

        // 获取货币显示名称
        fun getDisplayName(currency: String): String = DISPLAY_NAMES[currency] ?: currency
    }

    object WebSocketParamValue {
        const val CALL_HANG_UP_REASON_USER_HUNG_UP = 1 //接通后用户端或主播端主动挂断
        const val CALL_HANG_UP_REASON_HUNG_UP_BY_OTHER = -1 //对方挂断导致被动挂断(用户端/主播端)
        const val CALL_HANG_UP_REASON_BANNED_BY_SERVER = 3 //被声网踢出房间(用户端/主播端)
        const val CALL_HANG_UP_REASON_USER_JOIN_TIMEOUT = 4 //主播端15秒对方都没有进入房间被迫挂断
        const val CALL_HANG_UP_REASON_ANCHOR_JOINED_CANCEL_BY_OTHER_CANCEL = 5 //主播端进入房间收到对方取消的长链接
        const val CALL_HANG_UP_REASON_PERMISSION_CANCEL = 6 //权限挂断(主播端)
        const val CALL_HANG_UP_REASON_ANCHOR_JOIN_10_TIMEOUT = 7 //用户端监听对方10s没进入房间
        const val CALL_HANG_UP_REASON_ANCHOR_JOIN_FAILED = 8 //主播端加入房间失败
        const val CALL_REFUSE_REASON_ALREADY_IN_CALL =
            1 //当前在通话中或已有通话弹窗 （比如5个人同时给主播打， 主播收到其中一个，其他用户需要发送这个webSocket）
        const val CALL_REFUSE_REASON_ANCHOR_REFUSE = 2 //主播主动点击拒绝
        const val CALL_REFUSE_REASON_ANCHOR_TIMEOUT_REFUSE = 3 //主播超时不接，拒绝
        const val CALL_STATING_UPDATE_START_TYPE_BACKGROUND = 1 //通话过程中待机
        const val CALL_STATING_UPDATE_START_TYPE_FOREGROUND = 2 //通话过程中取消待机
        const val CALL_REFUSE_REASON_ANCHOR_JOIN_CHANNEL_FAILED = 8 //主播加入房间失败
    }

    object ImageSpec {
        var DOMAIN = ""
        var THUMBNAIL = ""
        var LIST = ""
        var HIGH_QUALITY = ""

        fun getDomain(): String? {
            return resolveSpec(DOMAIN, DataStoreManager.getAnchorConfigBeanSync()?.cdn) {
                DOMAIN = it
            }
        }

        fun get(
            thumbnail: Boolean = true, highQuality: Boolean = false, list: Boolean = false
        ): String? {
            return when {
                thumbnail -> resolveSpec(
                    THUMBNAIL, DataStoreManager.getAnchorConfigBeanSync()?.thumb_param
                ) { THUMBNAIL = it }

                highQuality -> resolveSpec(
                    HIGH_QUALITY, DataStoreManager.getAnchorConfigBeanSync()?.detail_photo_param
                ) { HIGH_QUALITY = it }

                list -> resolveSpec(
                    LIST, DataStoreManager.getAnchorConfigBeanSync()?.list_param
                ) { LIST = it }

                else -> null
            }
        }

        private fun resolveSpec(
            current: String?, fallback: String?, assign: (String) -> Unit
        ): String? {
            return if (current.isNullOrEmpty()) {
                fallback?.also { assign(it) }
            } else {
                current
            }
        }
    }
}