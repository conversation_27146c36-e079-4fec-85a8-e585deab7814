package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.mobile.anchor.app.data.model.OnlineStatus
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.BusyColor
import com.mobile.anchor.app.ui.theme.OfflineColor
import com.mobile.anchor.app.ui.theme.OnlineColor

/**
 * 在线状态指示器组件
 * 显示一个小圆点，在线显示绿色，其他状态显示灰色
 */
@Composable
fun OnlineStatusIndicator(
    status: OnlineStatus, modifier: Modifier = Modifier, size: Int = 12 // 圆点大小，单位dp
) {
    val indicatorColor = when (status) {
        OnlineStatus.ONLINE -> OnlineColor // 在线 - 绿色
        OnlineStatus.BUSY -> BusyColor // 在线 - 绿色
        OnlineStatus.OFFLINE -> OfflineColor // 在线 - 绿色
        OnlineStatus.SILENT -> OfflineColor // 在线 - 绿色
        // 其他状态 - 灰色
    }

    Box(
        modifier = modifier
            .size(size.dp)
            .clip(CircleShape)
            .background(indicatorColor)
            .border(
                width = 1.dp, color = Color.White, // 白色边框，与头像背景区分
                shape = CircleShape
            )
    )
}

@Composable
fun OnlineStatusText(
    statusValue: OnlineStatus, modifier: Modifier = Modifier
) {
    Text(
        text = when (statusValue) {
            OnlineStatus.ONLINE -> "Online"
            OnlineStatus.BUSY -> "Busy"
            OnlineStatus.OFFLINE -> "Offline"
            OnlineStatus.SILENT -> "Silent"
        }, style = MaterialTheme.typography.labelSmall, color = when (statusValue) {
            OnlineStatus.ONLINE -> OnlineColor // 在线 - 绿色
            OnlineStatus.BUSY -> BusyColor // 在线 - 绿色
            OnlineStatus.OFFLINE -> OfflineColor // 在线 - 绿色
            OnlineStatus.SILENT -> OfflineColor // 在线 - 绿色
        }, modifier = modifier
            .background(
                color = Color.Black.copy(alpha = 0.2F), shape = RoundedCornerShape(50)
            )
            .padding(horizontal = 6.dp, vertical = 3.dp)
    )
}

/**
 * 从字符串状态值创建OnlineStatusIndicator
 */
@Composable
fun OnlineStatusIndicator(
    statusValue: String?, modifier: Modifier = Modifier, size: Int = 12
) {
    val status = OnlineStatus.fromValue(statusValue)
    OnlineStatusIndicator(
        status = status, modifier = modifier, size = size
    )
}

@Preview
@Composable
fun OnlineStatusIndicatorPreview() {
    AnchorTheme {
        Column(
            modifier = Modifier.padding(16.dp), verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text("在线状态指示器:")

            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OnlineStatusIndicator(OnlineStatus.ONLINE)
                Text("在线")
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OnlineStatusIndicator(OnlineStatus.BUSY)
                Text("忙碌")
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OnlineStatusIndicator(OnlineStatus.OFFLINE)
                Text("离线")
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OnlineStatusIndicator(OnlineStatus.SILENT)
                Text("勿扰")
            }

            OnlineStatusText(OnlineStatus.ONLINE)
            OnlineStatusText(OnlineStatus.OFFLINE)
            OnlineStatusText(OnlineStatus.BUSY)
            OnlineStatusText(OnlineStatus.SILENT)
        }
    }
}
