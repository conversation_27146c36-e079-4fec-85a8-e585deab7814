package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@JsonClass(generateAdapter = true)
@Parcelize
data class GiftItemBean(
    val id: String = "", //礼物ID
    val showName: String = "", //礼物名称
    var coin: Int = 0, //用户显示金币
    val icon: String = "",  //礼物图标
    var checked: Boolean = false,
    val num: Int = 1, // 主播详情礼物数量
    val diamond: Int = 0,//主播显示金币
    val callId: String = "",//通话ID
    val resource_id: String = ""//资源id
) : Parcelable