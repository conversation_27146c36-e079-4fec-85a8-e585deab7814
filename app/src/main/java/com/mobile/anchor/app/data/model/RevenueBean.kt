package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * 收入记录数据模型
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class RevenueBean(
    @Json(name = "callId") val callId: String = "",
    @<PERSON><PERSON>(name = "userId") val userId: Int = 0,
    @<PERSON><PERSON>(name = "text") val text: String = "",
    @<PERSON><PERSON>(name = "anchor_id") val anchorId: String = "",
    @<PERSON><PERSON>(name = "userNickName") val userNickName: String = "",
    @<PERSON><PERSON>(name = "userAvatar") val userAvatar: String = "",
    @<PERSON><PERSON>(name = "connectAt") val connectAt: String = "",
    @<PERSON><PERSON>(name = "callDurationSecond") val callDurationSecond: Int = 0,
    @<PERSON><PERSON>(name = "call_stat") val callStat: Int = 0, //1未接通 2已接通
    @Json(name = "coin") val coin: Int = 0,
    @<PERSON><PERSON>(name = "effect") val effect: Boolean = false,
    @<PERSON><PERSON>(name = "actionAt") val actionAt: Long = 0L
) : Parcelable {

    /**
     * 获取收入类型描述
     */
    fun getRevenueTypeDesc(): String {
        return when {
            effect -> "Video Call"
            else -> "Call Attempt"
        }
    }

    /**
     * 是否为有效收入
     */
    val isValidRevenue: Boolean get() = effect && coin > 0

    /**
     * 格式化通话时长
     */
    fun getFormattedDuration(): String {
        return try {
            val seconds = callDurationSecond
            val minutes = seconds / 60
            val remainingSeconds = seconds % 60
            String.format("%02d:%02d", minutes, remainingSeconds)
        } catch (e: Exception) {
            "00:00"
        }
    }
}

/**
 * 结算记录数据模型
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class SettlementBean(
    @Json(name = "id") val id: String = "",
    @Json(name = "reason") val reason: String = "",
    @Json(name = "stat") val stat: Int,//1prcessing 2completed 3faild
    @Json(name = "coin") val coin: Int = 0,
    @Json(name = "createdAt") val createdAt: Long = 0,
    @Json(name = "currency") val currency: String = "",
    @Json(name = "currency_price") val currency_price: Int = 0
) : Parcelable {

    /**
     * 获取结算类型描述
     */
    fun getSettleTypeDesc(): String {
//        return when (settleType) {
//            1 -> "Daily Settlement"
//            2 -> "Weekly Settlement"
//            3 -> "Monthly Settlement"
//            else -> "Settlement"
//        }
        return "Settlement"
    }

    /**
     * 获取结算状态描述
     */
    fun getSettleStatusDesc(): String {
        return when (stat) {
            1 -> "Pending"
            2 -> "Completed"
            3 -> "Failed"
            else -> "Unknown"
        }
    }

    /**
     * 获取状态颜色
     */
    fun getStatusColor(): androidx.compose.ui.graphics.Color {
        return when (stat) {
            1 -> androidx.compose.ui.graphics.Color(0xFFFF9500) // 橙色 - 待结算
            2 -> androidx.compose.ui.graphics.Color(0xFF21C66E) // 绿色 - 已结算
            3 -> androidx.compose.ui.graphics.Color(0xFFFF6B6B) // 红色 - 结算失败
            else -> androidx.compose.ui.graphics.Color.Gray
        }
    }
}
