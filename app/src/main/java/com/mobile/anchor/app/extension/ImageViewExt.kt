package com.mobile.anchor.app.extension

import android.widget.ImageView
import coil.load
import coil.transform.CircleCropTransformation
import coil.transform.RoundedCornersTransformation
import com.mobile.anchor.app.R
import com.mobile.anchor.app.utils.ContextHolder

/**
 * Author:Lxf
 * Create on:2024/9/14
 * Description:
 */

fun ImageView.loadAvatar(url: String) {
    load(url) {
        crossfade(true)
        error(R.mipmap.ic_default_avatar)
        placeholder(R.mipmap.ic_default_avatar)
        transformations(CircleCropTransformation())
    }
}

fun ImageView.loadAnchorAvatar(url: String) {
    load(url) {
        crossfade(true)
        error(R.mipmap.ic_pic_default_oval)
        placeholder(R.mipmap.ic_pic_default_oval)
        transformations(CircleCropTransformation())
    }
}

fun ImageView.loadAnchorImage(
    url: String, radio: Float = ContextHolder.context.resources.getDimension(
        R.dimen.dp_16
    )
) {
    load(url) {
        crossfade(true)
        error(R.mipmap.ic_default_image)
        placeholder(R.mipmap.ic_default_image)
        transformations(RoundedCornersTransformation(radio))
    }
}
