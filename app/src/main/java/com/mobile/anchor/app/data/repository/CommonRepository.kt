package com.mobile.anchor.app.data.repository

import com.mobile.anchor.app.data.model.VersionBean
import com.mobile.anchor.app.data.network.NetworkClient
import com.mobile.anchor.app.data.service.CommonApiService
import com.mobile.anchor.app.i18n.I18nBean
import com.mobile.anchor.app.logger.LogX
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 设置相关数据仓库
 */
class CommonRepository {

    private val apiService: CommonApiService =
        NetworkClient.createService(CommonApiService::class.java)

    /**
     * 检查版本更新
     */
    suspend fun checkVersion(): Result<VersionBean> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.checkVersion()
            Result.success(response.data!!)
        } catch (e: Exception) {
            LogX.e("检查版本更新异常", e.message)
            Result.failure(e)
        }
    }

    suspend fun checkI18nInfo(md5: String): Result<I18nBean?> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getLangInfo(md5)
            Result.success(response.data)
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }

}
