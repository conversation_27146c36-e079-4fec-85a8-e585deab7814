package com.mobile.anchor.app.ui.screens.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bdc.android.library.extension.finish
import com.mobile.anchor.app.R
import com.mobile.anchor.app.extension.toShowDiamond
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorSlider
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.viewmodels.LevelViewModel
import kotlin.math.roundToInt

/**
 * 等级页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LevelScreen(
) {
    val context = LocalContext.current
    val userData = DataStoreManager.getUserObject()
    val viewModel = viewModel<LevelViewModel>()
    var progress by remember {
        mutableFloatStateOf(
            userData?.levelConfig?.call_price?.toShowDiamond()?.toFloat() ?: 0f
        )
    } // 当前进度
    val maxProgress by remember {
        mutableFloatStateOf(
            userData?.levelConfig?.max_price?.toShowDiamond()?.toFloat() ?: 50F
        )
    } // 最大进度
    val minProgress by remember {
        mutableFloatStateOf(
            userData?.levelConfig?.min_price?.toShowDiamond()?.toFloat() ?: 40f
        )
    } // 最小进度

    AnchorScaffold(topBar = {
        AnchorTopBar(
            context.getString(R.string.my_level))
    }) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(top = paddingValues.calculateTopPadding(), start = 16.dp, end = 16.dp)
        ) {
            // 当前等级卡片
            CurrentLevelCard(
                currentLevel = userData?.levelConfig?.title ?: ""
            )

            // 价格设置卡片
            PriceSettingCard(
                min = minProgress.toInt(),
                max = maxProgress.toInt(),
                progress = progress,
                onValueChange = {
                    progress = it
                    viewModel.updateCallPrice(it.toInt())
                })

            Spacer(modifier = Modifier.height(10.dp))

            // 等级规则按钮
            LevelRulesBanner()

            // 等级规则内容
            Spacer(modifier = Modifier.height(16.dp))
            LevelRulesContent()
        }
    }
}

/**
 * 当前等级卡片
 */
@Composable
private fun CurrentLevelCard(
    currentLevel: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(), colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ), elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(R.string.current_level),
                style = MaterialTheme.typography.bodyLarge,
                color = Color.White,
                modifier = Modifier.weight(1f)
            )

            // 等级显示
            Text(
                text = currentLevel,
                style = MaterialTheme.typography.titleLarge,
                color = Color.White,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

/**
 * 价格设置卡片
 */
@Composable
private fun PriceSettingCard(
    min: Int, max: Int, progress: Float, onValueChange: (Float) -> Unit = {}
) {
    Card(
        modifier = Modifier.fillMaxWidth(), colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ), elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 价格设置标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.price_setting),
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color.White
                )

                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = R.mipmap.ic_coin),
                        contentDescription = "钻石",
                        modifier = Modifier.size(20.dp)
                    )

                    Spacer(modifier = Modifier.width(4.dp))

                    Text(
                        text = progress.toInt().toString(),
                        style = MaterialTheme.typography.bodyLarge,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Icon(
                        imageVector = Icons.Default.KeyboardArrowRight,
                        contentDescription = "箭头",
                        tint = Color.White.copy(alpha = 0.7f),
                        modifier = Modifier.size(20.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // 等级进度
            Column {
                // 进度条
                val stepSize = 10f
                AnchorSlider(
                    value = progress,
                    onValueChange = { value ->
                        val aligned = ((value - min) / stepSize).roundToInt() * stepSize + min
                        onValueChange.invoke(aligned.coerceIn(min.toFloat(), max.toFloat()))
//                        onValueChange.invoke(it)
                    },
                    modifier = Modifier.fillMaxWidth(),
                    steps = ((max - min) / stepSize).roundToInt() - 1, // 4 segments, 3 steps
                    valueRange = min.toFloat()..max.toFloat(),
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 等级标签
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "$min",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White.copy(alpha = 0.8f)
                    )

                    Text(
                        text = "$max",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White.copy(alpha = 0.8f)
                    )
                }
            }
        }
    }
}

/**
 * 等级规则按钮
 */
@Composable
private fun LevelRulesBanner() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp)
    ) {
        Image(
            painter = painterResource(R.mipmap.bg_privilege_banner),
            contentDescription = "背景",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillWidth
        )

        Text(
            text = stringResource(R.string.level_rules),
            color = Color.White,
            fontSize = 18.sp,
            fontFamily = FontFamily(Font(R.font.rammettoone)),
            fontWeight = FontWeight.Bold,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

/**
 * 等级规则内容
 */
@Composable
private fun LevelRulesContent() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(
            text = stringResource(R.string.level_free_explain),
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.8f),
            lineHeight = 20.sp
        )
    }
}

@Preview
@Composable
fun LevelScreenPreview() {
    AnchorTheme {
        LevelScreen()
    }
}
