package com.mobile.anchor.app.utils

import com.mobile.anchor.app.logger.LogX
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.math.abs

/**
 * 日期时间工具类
 * 提供常用的日期时间格式化和计算方法
 */
object DateTimeUtils {
    
    // 常用日期格式
    const val FORMAT_YYYY_MM_DD = "yyyy-MM-dd"
    const val FORMAT_YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm"
    const val FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss"
    const val FORMAT_MM_DD_HH_MM = "MM-dd HH:mm"
    const val FORMAT_HH_MM = "HH:mm"
    const val FORMAT_YYYY_MM = "yyyy-MM"
    const val FORMAT_MM_DD = "MM-dd"
    const val FORMAT_ISO_8601 = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
    
    private val locale = Locale.getDefault()
    
    /**
     * 获取当前时间戳（毫秒）
     */
    fun getCurrentTimestamp(): Long = System.currentTimeMillis()
    
    /**
     * 获取当前时间戳（秒）
     */
    fun getCurrentTimestampSeconds(): Long = System.currentTimeMillis() / 1000
    
    /**
     * 格式化时间戳为指定格式的字符串
     */
    fun formatTimestamp(timestamp: Long, pattern: String = FORMAT_YYYY_MM_DD_HH_MM_SS): String {
        return try {
            val sdf = SimpleDateFormat(pattern, locale)
            sdf.format(Date(timestamp))
        } catch (e: Exception) {
            LogX.e("格式化时间戳失败", e)
            ""
        }
    }
    
    /**
     * 解析日期字符串为时间戳
     */
    fun parseToTimestamp(dateString: String, pattern: String = FORMAT_YYYY_MM_DD_HH_MM_SS): Long {
        return try {
            val sdf = SimpleDateFormat(pattern, locale)
            sdf.parse(dateString)?.time ?: 0L
        } catch (e: Exception) {
            LogX.e("解析日期字符串失败: $dateString", e)
            0L
        }
    }
    
    /**
     * 获取友好的时间显示（如：刚刚、1分钟前、1小时前等）
     */
    fun getFriendlyTime(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 0 -> "未来时间"
            diff < TimeUnit.MINUTES.toMillis(1) -> "刚刚"
            diff < TimeUnit.HOURS.toMillis(1) -> "${diff / TimeUnit.MINUTES.toMillis(1)}分钟前"
            diff < TimeUnit.DAYS.toMillis(1) -> "${diff / TimeUnit.HOURS.toMillis(1)}小时前"
            diff < TimeUnit.DAYS.toMillis(7) -> "${diff / TimeUnit.DAYS.toMillis(1)}天前"
            diff < TimeUnit.DAYS.toMillis(30) -> "${diff / TimeUnit.DAYS.toMillis(7)}周前"
            diff < TimeUnit.DAYS.toMillis(365) -> "${diff / TimeUnit.DAYS.toMillis(30)}个月前"
            else -> "${diff / TimeUnit.DAYS.toMillis(365)}年前"
        }
    }
    
    /**
     * 判断是否为今天
     */
    fun isToday(timestamp: Long): Boolean {
        val today = Calendar.getInstance()
        val target = Calendar.getInstance().apply { timeInMillis = timestamp }
        
        return today.get(Calendar.YEAR) == target.get(Calendar.YEAR) &&
                today.get(Calendar.DAY_OF_YEAR) == target.get(Calendar.DAY_OF_YEAR)
    }
    
    /**
     * 判断是否为昨天
     */
    fun isYesterday(timestamp: Long): Boolean {
        val yesterday = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_YEAR, -1)
        }
        val target = Calendar.getInstance().apply { timeInMillis = timestamp }
        
        return yesterday.get(Calendar.YEAR) == target.get(Calendar.YEAR) &&
                yesterday.get(Calendar.DAY_OF_YEAR) == target.get(Calendar.DAY_OF_YEAR)
    }
    
    /**
     * 判断是否为本周
     */
    fun isThisWeek(timestamp: Long): Boolean {
        val thisWeek = Calendar.getInstance()
        val target = Calendar.getInstance().apply { timeInMillis = timestamp }
        
        return thisWeek.get(Calendar.YEAR) == target.get(Calendar.YEAR) &&
                thisWeek.get(Calendar.WEEK_OF_YEAR) == target.get(Calendar.WEEK_OF_YEAR)
    }
    
    /**
     * 获取智能时间显示
     * 今天显示时间，昨天显示"昨天 时间"，其他显示日期
     */
    fun getSmartTimeDisplay(timestamp: Long): String {
        return when {
            isToday(timestamp) -> formatTimestamp(timestamp, FORMAT_HH_MM)
            isYesterday(timestamp) -> "昨天 ${formatTimestamp(timestamp, FORMAT_HH_MM)}"
            isThisWeek(timestamp) -> formatTimestamp(timestamp, FORMAT_MM_DD_HH_MM)
            else -> formatTimestamp(timestamp, FORMAT_YYYY_MM_DD)
        }
    }
    
    /**
     * 获取两个时间戳之间的天数差
     */
    fun getDaysBetween(startTimestamp: Long, endTimestamp: Long): Int {
        val diffInMillis = abs(endTimestamp - startTimestamp)
        return (diffInMillis / TimeUnit.DAYS.toMillis(1)).toInt()
    }
    
    /**
     * 获取两个时间戳之间的小时数差
     */
    fun getHoursBetween(startTimestamp: Long, endTimestamp: Long): Int {
        val diffInMillis = abs(endTimestamp - startTimestamp)
        return (diffInMillis / TimeUnit.HOURS.toMillis(1)).toInt()
    }
    
    /**
     * 获取两个时间戳之间的分钟数差
     */
    fun getMinutesBetween(startTimestamp: Long, endTimestamp: Long): Int {
        val diffInMillis = abs(endTimestamp - startTimestamp)
        return (diffInMillis / TimeUnit.MINUTES.toMillis(1)).toInt()
    }
    
    /**
     * 获取今天的开始时间戳（00:00:00）
     */
    fun getTodayStartTimestamp(): Long {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }
    
    /**
     * 获取今天的结束时间戳（23:59:59）
     */
    fun getTodayEndTimestamp(): Long {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        return calendar.timeInMillis
    }
    
    /**
     * 获取本周的开始时间戳（周一 00:00:00）
     */
    fun getThisWeekStartTimestamp(): Long {
        val calendar = Calendar.getInstance()
        calendar.firstDayOfWeek = Calendar.MONDAY
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }
    
    /**
     * 获取本月的开始时间戳（1号 00:00:00）
     */
    fun getThisMonthStartTimestamp(): Long {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }
}
