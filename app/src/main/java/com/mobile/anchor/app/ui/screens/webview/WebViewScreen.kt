package com.mobile.anchor.app.ui.screens.webview

import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.viewinterop.AndroidView
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.theme.Background

/**
 * WebView页面
 * 用于显示隐私政策、用户协议等网页内容
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WebViewScreen(
    title: String, url: String, onNavigateBack: () -> Unit
) {
    var isLoading by remember { mutableStateOf(true) }

    AnchorScaffold(topBar = { AnchorTopBar(title, onNavigationClick = onNavigateBack) }) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Background)
        ) {
            // WebView内容
            Box(modifier = Modifier.fillMaxSize()) {
                AndroidView(
                    factory = { context ->
                        WebView(context).apply {
                            webViewClient = object : WebViewClient() {
                                override fun onPageFinished(view: WebView?, url: String?) {
                                    super.onPageFinished(view, url)
                                    isLoading = false
                                }
                            }

                            settings.apply {
                                javaScriptEnabled = true
                                domStorageEnabled = true
                                loadWithOverviewMode = true
                                useWideViewPort = true
                                setSupportZoom(true)
                                builtInZoomControls = true
                                displayZoomControls = false
                            }

                            loadUrl(url)
                        }
                    }, modifier = Modifier.fillMaxSize()
                )

                // 加载指示器
                if (isLoading) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = androidx.compose.ui.Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFF9F2AF8)
                        )
                    }
                }
            }
        }
    }
}
