package com.mobile.anchor.app.ui.screens.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bdc.android.library.extension.finish
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.TaskBean
import com.mobile.anchor.app.extension.jumpWithType
import com.mobile.anchor.app.extension.toShowDiamond
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.AsyncImageComponent
import com.mobile.anchor.app.ui.components.StateView
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Primary

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/7 22:23
 * @description : 奖励任务页面
 */
@Composable
fun RewardTaskScreen(
    viewModel: RewardTaskViewModel = viewModel()
) {
    val context = LocalContext.current
    val taskList by viewModel.taskList.collectAsState()
    val pageState by viewModel.viewState.observeAsState()

    LaunchedEffect(Unit) {
        viewModel.loadTaskList()
    }

    AnchorScaffold(topBar = {
        AnchorTopBar(
            stringResource(R.string.reward_tasks), onNavigationClick = { context.finish() })
    }) { paddingValues ->
        val currentPageState = pageState
        when {
            currentPageState is com.mobile.anchor.app.ui.lce.PageState.Error && taskList.isEmpty() -> {
                StateView(
                    state = currentPageState,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                    onRetry = { viewModel.loadTaskList() })
            }

            taskList.isEmpty() && currentPageState !is com.mobile.anchor.app.ui.lce.PageState.Loading -> {
                StateView(
                    state = com.mobile.anchor.app.ui.lce.PageState.Empty,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                    emptyTitle = stringResource(R.string.no_tasks_available),
                    emptyMessage = stringResource(R.string.tasks_will_appear_here_when_available)
                )
            }

            currentPageState is com.mobile.anchor.app.ui.lce.PageState.Loading && taskList.isEmpty() -> {
                StateView(
                    state = currentPageState,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                    loadingMessage = stringResource(R.string.loading_tasks)
                )
            }

            else -> {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                        .verticalScroll(rememberScrollState())
                        .padding(horizontal = 16.dp)
                ) {
                    Spacer(modifier = Modifier.height(16.dp))

                    taskList.forEach { task ->
                        RewardTaskItem(
                            task = task, onGetReward = {
                                context.jumpWithType(task.jump_url)
                            })
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                }
            }
        }
    }
}

/**
 * 奖励任务项组件
 */
@Composable
private fun RewardTaskItem(
    task: TaskBean, onGetReward: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(), colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ), elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp, vertical = 10.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.Top
            ) {
                // 左侧钻石图标和奖励信息
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    task.icon.takeIf { it.isNotEmpty() }?.let {
                        AsyncImageComponent(
                            task.icon, error = R.mipmap.ic_coin, modifier = Modifier.size(25.dp)
                        )
                    } ?: run {
                        // 大钻石图标
                        Image(
                            painter = painterResource(id = R.mipmap.ic_coin),
                            contentDescription = "钻石",
                            modifier = Modifier.size(25.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // 奖励信息移到钻石下方
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = task.reward.diamond_num.toShowDiamond().toString(),
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color.White,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }

                Spacer(modifier = Modifier.width(10.dp))

                // 任务信息
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = task.title,
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White,
                        fontWeight = FontWeight.Medium
                    )

                    if (task.sub_title.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = task.sub_title,
                            style = MaterialTheme.typography.labelSmall,
                            color = Color.White.copy(alpha = 0.7f)
                        )
                    }
                }

                Spacer(modifier = Modifier.width(16.dp))

                // Get按钮
                Button(
                    onClick = onGetReward,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Primary,
                        disabledContainerColor = Color.Gray.copy(alpha = 0.5f)
                    ),
                    shape = RoundedCornerShape(20.dp),
                    modifier = Modifier.height(32.dp),
                    enabled = task.task_status != 2
                ) {
                    Text(
                        text = when (task.task_status) {
                            2 -> stringResource(R.string.claimed)
                            else -> stringResource(R.string.get)
                        },
                        style = MaterialTheme.typography.labelSmall,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }
            }

            // 进度条 - 根据process_type控制显示
            if (task.process_type == 1) {
                Spacer(modifier = Modifier.height(6.dp))

                val total = task.step.coerceAtLeast(task.complete_config_json.total)
                val progress = if (total > 0) task.step.toFloat() / total.toFloat() else 0f

                Column {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = androidx.compose.foundation.layout.Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = stringResource(R.string.progress, task.step, total),
                            style = MaterialTheme.typography.labelSmall,
                            color = Color.White.copy(alpha = 0.7f)
                        )
                        Text(
                            text = "${(progress * 100).toInt()}%",
                            style = MaterialTheme.typography.labelSmall,
                            color = Color.White.copy(alpha = 0.7f)
                        )
                    }

                    Spacer(modifier = Modifier.height(2.dp))

                    LinearProgressIndicator(
                        progress = { progress.coerceIn(0f, 1f) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(2.dp),
                        color = Primary,
                        trackColor = Color.White.copy(alpha = 0.2f)
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun RewardTaskScreenPreview() {
    AnchorTheme {
//        RewardTaskScreen()

        RewardTaskItem(
            TaskBean(
                task_id = "1",
                title = "Watch a video",
                sub_title = "Watch a 30-second video to earn 100 diamonds",
                icon = "",
                reward = TaskBean.RewardBean(100, 0),
                task_type = 1,
                jump_url = "",
                target = 1,
                task_status = 1,
                task_category = "",
                complete_config_json = TaskBean.CompleteConfigBean(1),
                step = 0,
                process_type = 1,
                round = 1,
                level = 1
            )
        ) { }
    }
}
