package com.mobile.anchor.app.ui.screens.mine

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.data.network.ApiResult
import com.mobile.anchor.app.extension.buildImageUrl
import com.mobile.anchor.app.extension.formatDate
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.BottomSelectDialog
import com.mobile.anchor.app.ui.components.CircleAvatar
import com.mobile.anchor.app.ui.components.DatePickerDialog
import com.mobile.anchor.app.ui.components.InputDialog
import com.mobile.anchor.app.ui.screens.language.LanguageViewModel
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.viewmodels.UserViewModel
import com.mobile.anchor.app.utils.rememberAvatarPickerState
import com.mobile.anchor.app.utils.rememberImagePickerConfig

/**
 * 编辑个人资料页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileEditScreen(
) {
    val viewModel: UserViewModel = viewModel()
    val userData by viewModel.userData.observeAsState(DataStoreManager.getUserObject())
    val updateProfileState by viewModel.updateProfileState.observeAsState()

    // 加载用户数据
    LaunchedEffect(key1 = true) {
        viewModel.fetchUserInfo()
    }

    // 监听更新状态
    LaunchedEffect(updateProfileState) {
        when (updateProfileState) {
            is ApiResult.Success -> {
                viewModel.clearUpdateProfileState()
            }

            is ApiResult.Error -> {
                // 更新失败，显示错误信息
                // 这里可以添加错误提示
                viewModel.clearUpdateProfileState()
            }

            else -> {
                // Loading 或 null 状态
            }
        }
    }

    ProfileEditScreenContent(userData, viewModel)
}

@Composable
fun ProfileEditScreenContent(
    userData: UserBean?, viewModel: UserViewModel = viewModel()
) {
    val context = LocalContext.current
    val languageViewModel: LanguageViewModel = viewModel()
    val reviewInfo by viewModel.reviewInfo.observeAsState()

    // 弹框状态
    var showAvatarDialog by remember { mutableStateOf(false) }
    var showNicknameDialog by remember { mutableStateOf(false) }
    var showDatePickerDialog by remember { mutableStateOf(false) }
    var showCountryDialog by remember { mutableStateOf(false) }
    var showLanguageDialog by remember { mutableStateOf(false) }
    var showReviewingDialog by remember { mutableStateOf(false) }
    var showReviewReason by remember { mutableStateOf("") }

    // 表单状态 - 使用 LaunchedEffect 来同步 userData 的变化
    var nickname by remember { mutableStateOf("") }
    var birthday by remember { mutableStateOf(0L) }
    var country by remember { mutableStateOf("") }
    var language by remember { mutableStateOf("") }
    var gender by remember { mutableStateOf("Female") }
    var email by remember { mutableStateOf("") }
    var avatarUrl by remember { mutableStateOf("") }

    // 更新状态
    var isUpdateBirthday by remember { mutableStateOf(false) }
    var isUpdatingField by remember { mutableStateOf(false) }
    var updateError by remember { mutableStateOf<String?>(null) }

    val languageList by languageViewModel.uiState.collectAsState()

    // 头像上传状态
    var isUploadingAvatar by remember { mutableStateOf(false) }
    var avatarErrorMessage by remember { mutableStateOf<String?>(null) }
    var nicknameErrorMessage by remember { mutableStateOf<String?>(null) }

    // 当 userData 更新时，同步更新本地状态
    LaunchedEffect(userData) {
        userData?.let { user ->
            nickname = reviewInfo?.nickname ?: user.nickname
            birthday = user.birthday_at.toLong()
            email = user.login_email
            country = user.country?.code ?: ""
            language = user.lang
            avatarUrl = reviewInfo?.avatar ?: user.avatar.takeIf { it.isNotEmpty() }
                    ?: "https://api.dicebear.com/7.x/micah/png?seed=test"
        }
    }

    LaunchedEffect(reviewInfo) {
        reviewInfo?.let { review ->
            nickname = reviewInfo?.nickname ?: ""
            avatarUrl = reviewInfo?.avatar.takeIf { it?.isNotEmpty() == true }
                ?: "https://api.dicebear.com/7.x/micah/png?seed=test"

            avatarErrorMessage = null
            avatarErrorMessage = null

            if (!isUpdateBirthday) {
                if (review.isReviewing) {
//                    showReviewingDialog = true
                    showReviewReason =
                        context.getString(R.string.information_is_currently_under_review)
                }
                if (review.isRejected) {
//                    showReviewingDialog = true
                    showReviewReason =
                        review.check_reason.takeIf { it.isNotEmpty() } ?: review.reason
                }


                if (review.isReviewingAvatar) {
                    avatarErrorMessage = context.getString(R.string.avatar_is_under_review)
                }

                if (review.isRejectedAvatar) {
                    avatarErrorMessage = context.getString(R.string.avatar_approval_failed_simple)
                }

                if (review.isReviewingNickname) {
                    nicknameErrorMessage = context.getString(R.string.nickname_is_under_review)
                }

                if (review.isRejectedNickname) {
                    nicknameErrorMessage = context.getString(R.string.nickname_approval_failed)
                }
            }
        }
    }

    AnchorScaffold(
        topBar = {
            AnchorTopBar(
                title = context.getString(R.string.edit_data)
            )
        }) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {
            // 头像
            ProfileListItem(
                label = stringResource(R.string.avatar),
                value = "",
                showAvatar = true,
                avatarUrl = avatarUrl,
                isUploading = isUploadingAvatar,
                onClick = {
                    if (!isUploadingAvatar) {
                        showAvatarDialog = true
                    }
                })

            // 头像上传错误信息
            avatarErrorMessage?.let { error ->
                Text(
                    text = error,
                    color = if (reviewInfo?.isReviewingAvatar == true) Color.Yellow
                    else Color.Red,
                    fontSize = 12.sp,
                    modifier = Modifier
                        .padding(horizontal = 16.dp, vertical = 4.dp)
                        .align(Alignment.Start)
                )
            }

            // 昵称
            ProfileListItem(
                label = stringResource(R.string.name),
                value = nickname,
                onClick = { showNicknameDialog = true })

            nicknameErrorMessage?.let { error ->
                Text(
                    text = error,
                    color = if (reviewInfo?.isReviewingNickname == true) Color.Yellow
                    else Color.Red,
                    fontSize = 12.sp,
                    modifier = Modifier
                        .padding(horizontal = 16.dp, vertical = 4.dp)
                        .align(Alignment.Start)
                )
            }

            // 生日
            ProfileListItem(
                label = stringResource(R.string.date_of_birth),
                value = birthday.formatDate(),
                onClick = { showDatePickerDialog = true })

            // 国家
            ProfileListItem(
                label = "Country", value = country, isReadOnly = true, onClick = { })

            // 语言
            ProfileListItem(
                label = stringResource(R.string.language), value = language, onClick = {
                    languageViewModel.loadLanguages()
                    showLanguageDialog = true
                })

            // 性别（只读）
            ProfileListItem(
                label = stringResource(R.string.gender),
                value = gender,
                isReadOnly = true,
                onClick = { })

            // 邮箱（只读）
            ProfileListItem(
                label = stringResource(R.string.email),
                value = email,
                isReadOnly = true,
                showDivider = false,
                onClick = { })

            // 更新状态和错误信息显示
            if (isUpdatingField) {
                Spacer(modifier = Modifier.height(16.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    androidx.compose.material3.CircularProgressIndicator(
                        color = Color(0xFF9F2AF8),
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = stringResource(R.string.updating),
                        color = Color(0xFF9E9E9E),
                        fontSize = 14.sp
                    )
                }
            }

            updateError?.let { error ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = error,
                    color = Color.Red,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
            }
        }
    }

    // 头像选择器状态
    val avatarPickerState = rememberAvatarPickerState(
        onAvatarSelected = { objectKey ->
            viewModel.checkImageStatus(objectKey.buildImageUrl()) {
                if (it.pass) {
                    // 上传成功
                    avatarUrl = objectKey
                    isUpdateBirthday = false
                    // 立即更新生日到服务器
                    viewModel.updateSingleField(
                        avatar = avatarUrl,
                        nickname = nickname,
                        onSuccess = { updatedUser ->
                            avatarUrl = objectKey
                            isUpdatingField = false
                            updateError = null
                            LogX.d("头像更新成功: $objectKey")
                        },
                        onError = { error ->
                            isUpdatingField = false
                            LogX.e("头像更新失败: $error")
                        })
                } else {
                    // 上传失败
                    avatarErrorMessage = it.reason
                }
            }
            showAvatarDialog = false
        }, onUploadStart = {
            showAvatarDialog = false
            isUploadingAvatar = true
            avatarErrorMessage = null
        }, onUploadEnd = {
            isUploadingAvatar = false
        }, onError = { error ->
            avatarErrorMessage = error
            showAvatarDialog = false
        }, config = rememberImagePickerConfig(
            showCamera = true, showGallery = true, showGooglePhotos = false
        )
    )

//    ConfirmDialog(
//        visible = showReviewingDialog,
//        title = stringResource(R.string.reminder),
//        content = showReviewReason.takeIf { it.isNotEmpty() }
//            ?: stringResource(R.string.information_is_currently_under_review),
//        showCancelButton = false,
//        showConfirmButton = true,
//        onConfirm = {
//            showReviewingDialog = false
//        },
//        onDismiss = { showReviewingDialog = false })

    // 头像选择弹框
    BottomSelectDialog(
        visible = showAvatarDialog,
        title = stringResource(R.string.select_avatar),
        options = avatarPickerState.getOptions(),
        onDismiss = { showAvatarDialog = false },
        onOptionSelected = { index, _ ->
            LogX.d("头像选择更新: $index")
            avatarPickerState.handleOptionSelected(index)
        })

    // 昵称修改弹框
    InputDialog(
        visible = showNicknameDialog,
        title = stringResource(R.string.edit_name),
        inputValue = nickname,
        inputPlaceholder = stringResource(R.string.enter_your_name),
        maxLength = 20,
        onDismiss = { showNicknameDialog = false },
        onInputChange = { },
        onConfirm = { newNickname ->
            if (newNickname.isNotBlank() && newNickname != nickname) {
                isUpdatingField = true
                updateError = null
                isUpdateBirthday = false
                // 立即更新昵称到服务器
                viewModel.updateSingleField(
                    avatar = avatarUrl,
                    nickname = newNickname,
                    onSuccess = { updatedUser ->
                        nickname = newNickname
                        isUpdatingField = false
                        updateError = null
                        LogX.d("昵称更新成功: $newNickname")
                    },
                    onError = { error ->
                        isUpdatingField = false
                        updateError = context.getString(R.string.nickname_update_failed, error)
                        LogX.e("昵称更新失败: $error")
                    })
            }
            showNicknameDialog = false
        })

    // 生日选择弹框
    DatePickerDialog(
        visible = showDatePickerDialog,
        title = stringResource(R.string.select_birthday),
        onDismiss = { showDatePickerDialog = false },
        onDateSelected = { calendar ->
            val newBirthday = calendar.time.time
            if (newBirthday != birthday) {
                isUpdatingField = true
                updateError = null

                isUpdateBirthday = true
                // 立即更新生日到服务器
                viewModel.updateSingleField(birthdayAt = newBirthday, onSuccess = { updatedUser ->
                    birthday = newBirthday
                    isUpdatingField = false
                    updateError = null
                    LogX.d("生日更新成功: $newBirthday")
                }, onError = { error ->
                    isUpdatingField = false
                    updateError = context.getString(R.string.birthday_update_failed, error)
                    LogX.e("生日更新失败: $error")
                })
            }
            showDatePickerDialog = false
        })

    // 国家选择弹框
    CountrySelectDialog(
        visible = showCountryDialog,
        selectedCountry = country,
        onDismiss = { showCountryDialog = false },
        onCountrySelected = { selectedCountry ->
            if (selectedCountry != country) {
                country = selectedCountry
                // 注意：国家信息通常不通过updateProfile接口更新，这里只更新本地状态
                // 如果需要更新到服务器，需要确认API是否支持
                LogX.d("国家选择更新: $selectedCountry")
            }
            showCountryDialog = false
        })

    // 语言选择弹框
    LanguageSelectDialog(
        visible = showLanguageDialog,
        languageList = languageList.languages,
        onDismiss = { showLanguageDialog = false },
        onLanguagesSelected = { selectedLanguages ->
            if (selectedLanguages != language) {
                language = languageList.languages?.find { it.language == selectedLanguages }?.value
                    ?: selectedLanguages
                LogX.d("语言选择更新: $selectedLanguages")
                languageViewModel.selectLanguage(selectedLanguages)
            }
            showLanguageDialog = false
        })
}

/**
 * 个人资料列表项组件
 */
@Composable
private fun ProfileListItem(
    label: String,
    value: String,
    showAvatar: Boolean = false,
    avatarUrl: String? = null,
    isUploading: Boolean = false,
    isReadOnly: Boolean = false,
    showDivider: Boolean = true,
    onClick: () -> Unit
) {
    Column {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(enabled = !isReadOnly) { onClick() }
                .padding(vertical = 16.dp), verticalAlignment = Alignment.CenterVertically) {
            // 标签
            Text(
                text = label,
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                modifier = Modifier.weight(1f)
            )

            // 值或头像
            if (showAvatar) {
                Box(contentAlignment = Alignment.Center) {
                    CircleAvatar(
                        imageUrl = avatarUrl?.buildImageUrl(),
                        size = 40.dp,
                        contentDescription = "Avatar"
                    )

                    // 上传状态覆盖层
                    if (isUploading) {
                        Box(
                            modifier = Modifier
                                .size(40.dp)
                                .background(
                                    Color.Black.copy(alpha = 0.6f),
                                    shape = androidx.compose.foundation.shape.CircleShape
                                ), contentAlignment = Alignment.Center
                        ) {
                            androidx.compose.material3.CircularProgressIndicator(
                                color = Color.White,
                                modifier = Modifier.size(20.dp),
                                strokeWidth = 2.dp
                            )
                        }
                    }
                }
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = value,
                        color = Color(0xFF9E9E9E),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Normal
                    )
                }
            }

            // 右箭头（只读项不显示）
            if (!isReadOnly) {
                Spacer(modifier = Modifier.size(8.dp))
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = null,
                    tint = Color(0xFF9E9E9E),
                    modifier = Modifier.size(20.dp)
                )
            }
        }

        // 分割线
        if (showDivider) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(1.dp)
                    .background(Color(0xFF363948).copy(alpha = 0.5f))
            )
        }
    }
}

/**
 * 国家选择弹框
 */
@Composable
private fun CountrySelectDialog(
    visible: Boolean,
    selectedCountry: String,
    onDismiss: () -> Unit,
    onCountrySelected: (String) -> Unit
) {
    val countries = listOf(
        "US",
        "CN",
        "JP",
        "KR",
        "GB",
        "FR",
        "DE",
        "IT",
        "ES",
        "RU",
        "IN",
        "BR",
        "CA",
        "AU",
        "MX",
        "AR",
        "TH",
        "VN",
        "ID",
        "MY"
    )

    BottomSelectDialog(
        visible = visible,
        title = "Select Country",
        options = countries,
        onDismiss = onDismiss,
        onOptionSelected = { _, country ->
            onCountrySelected(country)
        })
}

/**
 * 语言选择弹框
 */
@Composable
private fun LanguageSelectDialog(
    visible: Boolean,
    languageList: List<UserBean.LanguageBean>,
    onDismiss: () -> Unit,
    onLanguagesSelected: (String) -> Unit
) {

    BottomSelectDialog(
        visible = visible,
        title = stringResource(R.string.select_language),
        options = languageList.map { it.value },
        onDismiss = onDismiss,
        onOptionSelected = { _, language ->
            onLanguagesSelected(languageList.first { it.value == language }.language)
        })
}

@Preview
@Composable
fun ProfileEditScreenPreview() {
    AnchorTheme {
        ProfileEditScreenContent(null, viewModel())
    }
}
