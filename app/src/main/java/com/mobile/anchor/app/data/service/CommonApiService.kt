package com.mobile.anchor.app.data.service

import com.bdc.android.library.ktnet.coroutines.Await
import com.mobile.anchor.app.data.model.ImageStatusBean
import com.mobile.anchor.app.data.model.InfoStatusBean
import com.mobile.anchor.app.data.model.IsolatedConfigBean
import com.mobile.anchor.app.data.model.VersionBean
import com.mobile.anchor.app.i18n.I18nBean
import com.mobile.anchor.app.manager.FileUploadManager
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * 通用API服务接口
 * 包含文件上传、通用工具等接口
 */
interface CommonApiService {

    /**
     * 获取AWS S3上传链接
     * @param fileName 文件名
     * @param fileType 文件类型，如 "image/jpeg", "video/mp4"
     * @param fileSize 文件大小（可选）
     */
    @GET("/api/v1/common/aws/s3/upload_url")
    suspend fun getUploadUrl(
        @Query("bizType") bizType: Int = 1/*0普通业务上传 1日志上传*/,
        @Query("ext") extension: String = FileUploadManager.FileType.IMAGE.extension
    ): ApiResponse<FileUploadManager.UploadCredentialBean>


    @POST("/api/v1/anchor/log/upload")
    suspend fun getUploadLogUrl(@Body params: Map<String, String>): ApiResponse<FileUploadManager.UploadCredentialBean>

    @GET("/api/v1/common/anchor/check/config")
    suspend fun checkVersion(): ApiResponse<VersionBean>

    @POST("/api/v1/common/check/img")
    suspend fun checkImageStatus(@Body body: RequestBody): Await<ImageStatusBean>

    @POST("/api/v1/anchor/check/info")
    suspend fun checkInfoStatus(@Body body: RequestBody): Await<InfoStatusBean>

    @GET("/api/v1/common/anchor/check/config")
    suspend fun getIsolatedConfig(): Await<IsolatedConfigBean?>

    @GET("/api/v1/common/lang/info")
    suspend fun getLangInfo(@Query("md5") md5: String? = null): ApiResponse<I18nBean>
}
