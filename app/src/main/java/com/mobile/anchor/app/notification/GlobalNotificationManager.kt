package com.mobile.anchor.app.notification

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.bdc.android.library.extension.jump
import com.bdc.android.library.utils.ActivityManager
import com.mobile.anchor.app.R
import com.mobile.anchor.app.extension.toTimestamp
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.conversation.MyRongConversationActivity
import com.mobile.anchor.app.utils.ContextHolder
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.message.TextMessage
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * 全局站内通知管理器
 * 用于管理从app顶部弹出的全局站内通知
 */
object GlobalNotificationManager : DefaultLifecycleObserver {
    
    private const val TAG = "GlobalNotificationManager"
    
    // 通知队列，使用线程安全的队列
    private val notificationQueue = ConcurrentLinkedQueue<NotificationData>()
    
    // 当前显示的通知视图
    private var currentNotificationView: GlobalNotificationView? = null

    // 是否正在处理通知切换
    private var isProcessingNotificationSwitch = false
    
    // 主线程Handler
    private val mainHandler = Handler(Looper.getMainLooper())
    
    // 应用是否在前台
    private var isAppInForeground = true

    // 获取通知开关状态
    private fun isNotificationEnabled(): Boolean {
        return DataStoreManager.isGlobalNotificationEnabledSync()
    }
    
    // 初始化标志
    private var isInitialized = false

    /**
     * 确保在主线程初始化
     */
    private fun ensureInitialized() {
        if (!isInitialized) {
            mainHandler.post {
                if (!isInitialized) {
                    // 注册应用生命周期监听
                    ProcessLifecycleOwner.get().lifecycle.addObserver(this)
                    isInitialized = true
                    LogX.d(TAG, "GlobalNotificationManager 初始化完成")
                }
            }
        }
    }
    
    /**
     * 通知数据类
     */
    data class NotificationData(
        val title: String,
        val content: String,
        val senderAvatar: String? = null,
        val messageType: String? = null,
        val timestamp: Long = System.currentTimeMillis(),
        val onClick: (() -> Unit)? = null
    )
    
    /**
     * 显示融云消息通知
     */
    fun showRongCloudMessageNotification(message: Message) {
        // 确保初始化
        ensureInitialized()

        if (!isNotificationEnabled() || !isAppInForeground) {
            LogX.d(TAG, "通知已禁用或应用在后台，跳过显示通知")
            return
        }

        // 检查消息类型，只处理指定类型的消息
        if (!shouldShowNotificationForMessage(message)) {
            LogX.d(TAG, "消息类型不需要显示通知: ${message.content?.javaClass?.simpleName}")
            return
        }

        // 检查权限
        val currentActivity = ActivityManager.current as? Activity
        if (currentActivity != null && !NotificationPermissionHelper.hasAllRequiredPermissions(currentActivity)) {
            LogX.w(TAG, "缺少必要权限，无法显示全局通知")
            return
        }

        val notificationData = createNotificationFromMessage(message)
        showNotification(notificationData)
    }
    
    /**
     * 显示自定义通知
     */
    fun showCustomNotification(
        title: String,
        content: String,
        senderName: String? = null,
        senderAvatar: String? = null,
        onClick: (() -> Unit)? = null
    ) {
        // 确保初始化
        ensureInitialized()

        if (!isNotificationEnabled() || !isAppInForeground) {
            LogX.d(TAG, "通知已禁用或应用在后台，跳过显示通知")
            return
        }

        // 检查权限
        val currentActivity = ActivityManager.current as? Activity
        if (currentActivity != null && !NotificationPermissionHelper.hasAllRequiredPermissions(currentActivity)) {
            LogX.w(TAG, "缺少必要权限，无法显示全局通知")
            return
        }

        val notificationData = NotificationData(
            title = title,
            content = content,
            senderAvatar = senderAvatar,
            onClick = onClick
        )
        showNotification(notificationData)
    }
    
    /**
     * 显示通知
     */
    private fun showNotification(notificationData: NotificationData) {
        mainHandler.post {
            try {
                val currentActivity = ActivityManager.current as Activity
                if (currentActivity.isFinishing || currentActivity.isDestroyed) {
                    LogX.w(TAG, "当前Activity不可用，无法显示通知")
                    return@post
                }
                
                // 如果当前有通知在显示，先隐藏再显示新的
                if (currentNotificationView?.isShowing() == true) {
                    LogX.d(TAG, "有新消息到达，先隐藏当前通知再显示新通知")

                    // 如果正在处理通知切换，直接返回避免重复处理
                    if (isProcessingNotificationSwitch) {
                        LogX.d(TAG, "正在处理通知切换，跳过此次请求")
                        return@post
                    }

                    isProcessingNotificationSwitch = true
                    // 清空队列，只显示最新消息
                    notificationQueue.clear()

                    // 先隐藏当前通知
                    currentNotificationView?.dismiss()
                    currentNotificationView = null

                    // 等待动画完成后显示新通知
                    mainHandler.postDelayed({
                        isProcessingNotificationSwitch = false
                        showNotificationInternal(currentActivity, notificationData)
                    }, 250) // 250ms延迟确保前一个通知完全消失（包含动画时间）
                    return@post
                }
                
                // 显示通知
                showNotificationInternal(currentActivity, notificationData)
                
            } catch (e: Exception) {
                LogX.e("$TAG, 显示通知失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 内部显示通知方法
     */
    private fun showNotificationInternal(activity: Activity, notificationData: NotificationData) {
        try {
            currentNotificationView = GlobalNotificationView(activity).apply {
                setNotificationData(notificationData)
                setOnDismissListener {
                    currentNotificationView = null
                    // 显示队列中的下一个通知
                    showNextNotification()
                }
                show()
            }
            
            LogX.d(TAG, "通知显示成功: ${notificationData.title}")
            
        } catch (e: Exception) {
            LogX.e("$TAG, 显示通知内部错误: ${e.message}", e)
            currentNotificationView = null
            showNextNotification()
        }
    }
    
    /**
     * 显示队列中的下一个通知
     */
    private fun showNextNotification() {
        mainHandler.post {
            val nextNotification = notificationQueue.poll()
            if (nextNotification != null) {
                val currentActivity = ActivityManager.current as Activity
                if (!currentActivity.isFinishing && !currentActivity.isDestroyed) {
                    showNotificationInternal(currentActivity, nextNotification)
                }
            }
        }
    }
    
    /**
     * 检查是否应该为此消息显示通知
     */
    private fun shouldShowNotificationForMessage(message: Message): Boolean {
        val messageType = message.content?.javaClass?.simpleName ?: return false

        // 只处理指定类型的消息
        val supportedTypes = setOf(
            "TextMessage",
            "ImageMessage",
            "HQVoiceMessage",
            "MikChatGiftMessage"
        )

        val shouldShow = supportedTypes.contains(messageType)
        LogX.d(TAG, "消息类型: $messageType, 是否显示通知: $shouldShow")
        return shouldShow
    }

    /**
     * 从融云消息创建通知数据
     */
    private fun createNotificationFromMessage(message: Message): NotificationData {
        var senderName = message.senderUserId
        var senderAvatar = ""
        val content = when {
            message.content != null -> {
                when (message.content.javaClass.simpleName) {
                    "TextMessage" -> {
                        val textMessage = message.content as TextMessage
                        textMessage.content
                    }
                    "ImageMessage" -> ContextHolder.context.getString(R.string.rc_message_content_image)
                    "HQVoiceMessage" -> ContextHolder.context.getString(R.string.rc_message_content_voice)
                    "MikChatGiftMessage" -> "[Gift]"
                    else -> "[Message]" // 理论上不会到达这里，因为已经过滤了消息类型
                }
            }
            else -> "[Message]"
        }
        message.content?.let { messageContent ->
            val userInfo = messageContent.userInfo
            if (userInfo != null) {
                senderName = userInfo.name
                senderAvatar = userInfo.portraitUri.toString()
            }
        }
        return NotificationData(
            title = senderName,
            content = content,
            senderAvatar = senderAvatar,
            timestamp = message.receivedTime,
            messageType = message.content?.javaClass?.simpleName,
            onClick = {
                // 点击通知跳转到聊天界面
                try {
                    val currentActivity = ActivityManager.current as? Activity
                    currentActivity?.let { activity ->
                        LogX.d(TAG, "点击通知，跳转到聊天界面，发送者: $senderName")
                        activity.jump(MyRongConversationActivity::class.java, Bundle().apply {
                            putString("targetId", message.senderUserId)
                            putString("ConversationType", Conversation.ConversationType.PRIVATE.name)
                        })
                    }
                } catch (e: Exception) {
                    LogX.e( "$TAG,跳转聊天界面失败: ${e.message}", e)
                }
            }
        )
    }
    
    /**
     * 设置通知开关
     */
    suspend fun setNotificationEnabled(enabled: Boolean) {
        DataStoreManager.setGlobalNotificationEnabled(enabled)
        LogX.d(TAG, "通知开关设置为: $enabled")
    }

    /**
     * 获取通知开关状态（公共接口）
     */
    fun getNotificationEnabled(): Boolean = isNotificationEnabled()
    
    /**
     * 清空通知队列
     */
    fun clearNotificationQueue() {
        notificationQueue.clear()
        LogX.d(TAG, "通知队列已清空")
    }
    
    /**
     * 隐藏当前通知
     */
    fun hideCurrentNotification() {
        mainHandler.post {
            currentNotificationView?.dismiss()
        }
    }

    /**
     * 请求必要权限
     */
    fun requestPermissions(activity: Activity, onResult: ((Boolean) -> Unit)? = null) {
        NotificationPermissionHelper.requestAllRequiredPermissions(activity, onResult)
    }

    /**
     * 检查是否有必要权限
     */
    fun hasRequiredPermissions(context: Context): Boolean {
        return NotificationPermissionHelper.hasAllRequiredPermissions(context)
    }

    /**
     * 测试方法：显示测试通知
     */
    fun showTestNotification() {
        showCustomNotification(
            title = "测试通知",
            content = "这是一个测试通知，用于验证全局通知功能是否正常工作",
            senderName = "系统",
            onClick = {
                LogX.d(TAG, "测试通知被点击")
            }
        )
    }

    /**
     * 测试方法：连续显示多个通知（测试优化后的消息切换功能）
     */
    fun showMultipleTestNotifications() {
        // 显示第一个通知
        showCustomNotification(
            title = "第一条消息",
            content = "这是第一条测试消息，3秒后自动消失",
            senderName = "用户A"
        )

        // 1.5秒后显示第二个通知（测试前一个消失后显示新的）
        mainHandler.postDelayed({
            showCustomNotification(
                title = "第二条消息",
                content = "前一个通知应该先消失，然后显示这条",
                senderName = "用户B"
            )
        }, 1500)

        // 3秒后显示第三个通知
        mainHandler.postDelayed({
            showCustomNotification(
                title = "第三条消息",
                content = "这是第三条消息，测试连续切换效果",
                senderName = "用户C"
            )
        }, 3000)
    }

    /**
     * 测试方法：测试消息类型过滤功能
     */
    fun testMessageTypeFiltering() {
        LogX.d(TAG, "开始测试消息类型过滤功能")

        // 模拟支持的消息类型
        val supportedTypes = listOf("TextMessage", "ImageMessage", "HQVoiceMessage", "MikChatGiftMessage")
        supportedTypes.forEach { messageType ->
            LogX.d(TAG, "测试消息类型: $messageType - 应该显示通知")
        }

        // 模拟不支持的消息类型
        val unsupportedTypes = listOf("CommandMessage", "LocationMessage", "FileMessage", "VideoMessage")
        unsupportedTypes.forEach { messageType ->
            LogX.d(TAG, "测试消息类型: $messageType - 不应该显示通知")
        }

        LogX.d(TAG, "消息类型过滤测试完成")
    }

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        isAppInForeground = true
        LogX.d(TAG, "应用进入前台")
    }
    
    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        isAppInForeground = false
        LogX.d(TAG, "应用进入后台")
        // 应用进入后台时隐藏当前通知
        hideCurrentNotification()
    }
}
