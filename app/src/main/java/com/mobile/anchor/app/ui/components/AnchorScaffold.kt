package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.bdc.android.library.extension.finish
import com.mobile.anchor.app.R
import com.mobile.anchor.app.ui.theme.Background

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/5 22:51
 * @description :
 */
@Composable
fun AnchorScaffold(
    topBar: (@Composable () -> Unit)? = null,
    modifier: Modifier = Modifier,
    containerColor: Color = Background,
    content: @Composable (PaddingValues) -> Unit
) {
    topBar?.let {
        Scaffold(
            containerColor = containerColor, modifier = modifier, topBar = topBar
        ) { paddingValues ->
            content(paddingValues)
        }
    } ?: Scaffold(containerColor = containerColor) { paddingValues ->
        content(paddingValues)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnchorTopBar(
    title: String,
    containerColor: Color = Background,
    showNavigationIcon: Boolean = true,
    onNavigationClick: (() -> Unit)? = null,
    navigationIcon: @Composable () -> Unit = {
        val context = LocalContext.current
        IconButton(onClick = {
            onNavigationClick?.invoke() ?: run {
                context.finish()
            }
        }) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                modifier = Modifier.padding(start = 10.dp, end = 10.dp),
                tint = Color.White
            )
        }
    },
    actions: @Composable RowScope.() -> Unit = {},
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Text(
                text = title,
                fontWeight = FontWeight.Bold,
                style = MaterialTheme.typography.titleMedium.copy(fontSize = dimensionResource(R.dimen.sp_18).value.sp)
            )
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = containerColor, titleContentColor = MaterialTheme.colorScheme.onPrimary
        ),
        actions = actions,
        windowInsets = TopAppBarDefaults.windowInsets,
        navigationIcon = {
            if (showNavigationIcon) {
                navigationIcon()
            }
        },
        modifier = modifier
    )
}

@Preview
@Composable
fun AnchorScaffoldPreview() {
    AnchorScaffold(topBar = { AnchorTopBar("标题") }) { paddingValues ->
        Text(
            text = "内容", modifier = Modifier.padding(paddingValues)
        )
    }
}
