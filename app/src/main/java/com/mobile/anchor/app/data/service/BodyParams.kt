package com.mobile.anchor.app.data.service

import com.mobile.anchor.app.data.model.UserBean
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/24 10:53 上午
 * @description：
 */
object BodyParams {

    fun codeBody(email: String): RequestBody {
        val map = mapOf(
            "email" to email,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    /**
     * 登录请求体
     *
     * @param auth_token
     * @param auth_id
     * @param auth_type
     * @param gender
     * @param country_id
     * @return
     */
    fun loginBody(
        auth_token: String,
        auth_id: String, // 邮箱地址或Google邮箱
        auth_type: Int, // 1-Google登录, 2-邮箱验证码登录
        gender: Int,
        country_id: Int,
    ): RequestBody {
        val map = mapOf(
            "auth_token" to auth_token,
            "auth_id" to auth_id,
            "auth_type" to auth_type,
            "gender" to gender,
            "country_id" to country_id,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun updateWorkingModeBody(isOpen: Boolean): RequestBody {
        val map = mapOf(
            "working_mod" to isOpen,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun updateProfileBody(
        avatar: String = "",
        birthdayAt: Long = 0L,
        gender: Int = 1,//1男2女
        inviteCode: String = "",
        nickname: String,
        firebaseToken: String = "",
        videoFile: String = "",
        updateType: Int = 0//0 提交资料审核 更新基本信息 1更新firebase token 2更新推送字段
    ): RequestBody {

        val map = mapOf(
            "avatar" to avatar,
            "birthday_at" to birthdayAt.toString(),
            "gender" to gender,
            "invite_code" to inviteCode,
            "nickname" to nickname,
            "firebase_token" to firebaseToken,
            "update_type" to updateType,
            "videoFile" to videoFile
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun updateAlbumBody(
        items: List<UserBean.AlbumBean>,
    ): RequestBody {
        val map = mapOf(
            "media_list" to items,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun bindBankBody(
        bankId: Int,
        cardNum: String,
        realName: String,
        code: String,
    ): RequestBody {
        val map = mapOf(
            "bank_id" to bankId,
            "card_num" to cardNum,
            "real_name" to realName,
            "code" to code,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun checkInfoBody(
        nickname: String,
        invite_code: String,
    ): RequestBody {
        val map = mapOf(
            "nickname" to nickname,
            "invite_code" to invite_code,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun checkImageStatusBody(
        objectKey: String,
    ): RequestBody {
        val map = mapOf(
            "image" to objectKey
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun setLanguageBody(
        language: String,
    ): RequestBody {
        val map = mapOf(
            "lang" to language
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun applyWithdrawBody(
        amount: Int,
    ): RequestBody {
        val map = mapOf(
            "diamond" to amount,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun relationOpParamsBody(peerId: String): RequestBody {
        val map = mapOf(
            "peerId" to peerId.toLong(),
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun getUserListBody(peerIds: MutableList<String>): RequestBody {
        val map = mapOf(
            "user_id_list" to peerIds,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun giftClaimBody(giftId: Int, userId: Int): RequestBody {
        val map = mapOf(
            "gift_id" to giftId,
            "user_id" to userId,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun settlementListBody(): RequestBody {
        return jsonToBody(objectsMapToJson(emptyMap()))
    }

    fun bindGoogleBody(authId: String, authToken: String): RequestBody {
        val map = mapOf(
            "auth_id" to authId,
            "auth_token" to authToken,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun setupCallPriceBody(price: Int): RequestBody {
        mapOf(
            "price" to price,
        ).let {
            return jsonToBody(objectsMapToJson(it))
        }
    }

    fun setupCurrencyBody(currency: String): RequestBody {
        val map = mapOf(
            "currency" to currency,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    fun screenUploadBody(callId: Int, url: String): RequestBody {
        mapOf(
            "call_id" to callId,
            "url" to url,
        ).let {
            return jsonToBody(objectsMapToJson(it))
        }
    }

    fun videoUploadBody(callId: Int, url: String): RequestBody {
        mapOf(
            "call_id" to callId,
            "video_url" to url,
        ).let {
            return jsonToBody(objectsMapToJson(it))
        }
    }

    /**
     * TODO moshi map 转json字符串
     *
     *
     * @param map
     * @return
     */
    private fun objectsMapToJson(map: Map<String, Any>): String {
        val moshi = Moshi.Builder().build()
        val jsonAdapter: JsonAdapter<Map<String, Any>> = moshi.adapter(
            Types.newParameterizedType(
                MutableMap::class.java, String::class.java, Any::class.java
            )
        )
        return jsonAdapter.indent(" ").toJson(map) ?: ""
    }

    /**
     * TODO json 字符串转RequestBody
     *
     * @param json
     * @return
     */
    private fun jsonToBody(json: String): RequestBody =
        json.toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
}