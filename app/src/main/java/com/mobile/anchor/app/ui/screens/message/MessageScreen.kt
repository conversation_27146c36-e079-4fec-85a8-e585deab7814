package com.mobile.anchor.app.ui.screens.message

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.mobile.anchor.app.R
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.CircleAvatar
import com.mobile.anchor.app.ui.theme.Background
import com.mobile.anchor.app.utils.DateTimeUtils

/**
 * 消息页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MessageScreen() {
    // 模拟消息数据
    val messages = remember {
        listOf(
            Message(
                "1", 
                "张三", 
                "https://via.placeholder.com/150", 
                "你好，最近怎么样？", 
                System.currentTimeMillis() - 300000, // 5分钟前
                2
            ),
            Message(
                "2", 
                "李四", 
                "https://via.placeholder.com/150", 
                "明天的会议记得参加", 
                System.currentTimeMillis() - 3600000, // 1小时前
                0
            ),
            Message(
                "3", 
                "王五", 
                "https://via.placeholder.com/150", 
                "项目进度如何？需要帮助吗？", 
                System.currentTimeMillis() - 7200000, // 2小时前
                1
            ),
            Message(
                "4", 
                "赵六", 
                "https://via.placeholder.com/150", 
                "周末一起出去玩吧", 
                System.currentTimeMillis() - 86400000, // 1天前
                0
            )
        )
    }

    AnchorScaffold( topBar = {
        TopAppBar(
            title = {
                Text(
                    text = stringResource(id = R.string.home_title),
                    fontWeight = FontWeight.Bold
                )
            }, colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Background,
                titleContentColor = MaterialTheme.colorScheme.onPrimary
            )
        )
    }) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(messages) { message ->
                MessageItem(
                    message = message,
                    onClick = { /* 点击消息 */ }
                )
            }
        }
    }
}

@Composable
private fun MessageItem(
    message: Message,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box {
                CircleAvatar(
                    imageUrl = message.avatar,
                    size = 48.dp,
                    contentDescription = "联系人头像"
                )
                
                // 未读消息数量徽章
                if (message.unreadCount > 0) {
                    Badge(
                        modifier = Modifier.align(Alignment.TopEnd)
                    ) {
                        Text(
                            text = if (message.unreadCount > 99) "99+" else message.unreadCount.toString(),
                            style = MaterialTheme.typography.labelSmall
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = message.senderName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = if (message.unreadCount > 0) FontWeight.Bold else FontWeight.Medium,
                        modifier = Modifier.weight(1f)
                    )
                    Text(
                        text = DateTimeUtils.getSmartTimeDisplay(message.timestamp),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = message.lastMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (message.unreadCount > 0) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    },
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

/**
 * 消息数据类
 */
data class Message(
    val id: String,
    val senderName: String,
    val avatar: String,
    val lastMessage: String,
    val timestamp: Long,
    val unreadCount: Int
)
