package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/6/14 20:16
 * @description :
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class ImageStatusBean(val pass: Boolean/*true 通过 false 不通过*/, val reason: String) :
    Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class InfoStatusBean(val list: List<ColumnItem>) : Parcelable {

    @Parcelize
    @JsonClass(generateAdapter = true)
    data class ColumnItem(val column: String, val pass: Boolean, val reason: String) : Parcelable
}
