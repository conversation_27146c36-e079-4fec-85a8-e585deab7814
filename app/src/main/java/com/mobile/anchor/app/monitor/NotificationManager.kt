package com.mobile.anchor.app.monitor

import android.app.Notification
import android.app.NotificationChannel
import android.content.Context
import android.graphics.Color
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.mobile.anchor.app.R
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.utils.ContextHolder

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/7/5 11:04
 * @description :
 */
object NotificationManager {

    private val TAG =
        NotificationManager::class.simpleName ?: "NotificationManager"
    private const val NOTIFICATION_CHANNEL_ID = "network_quality_monitor"
    private const val NOTIFICATION_ID = 1001

    init {
        createNotificationChannel()
    }

    private fun createNotificationChannel() {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            val channel = NotificationChannel(
//                NOTIFICATION_CHANNEL_ID, "网络质量监控", NotificationManager.IMPORTANCE_HIGH
//            ).apply {
//                description = "通知网络不可用或质量差的情况"
//            }
//            val notificationManager =
//                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
//            notificationManager.createNotificationChannel(channel)
//        }
        val context = ContextHolder.context
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                context.getString(R.string.network_quality_monitor),
                android.app.NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = context.getString(R.string.notifications_for_network_unavailability)
                enableVibration(true)
                enableLights(true)
                setShowBadge(true)
                // 关键设置：允许通知悬浮显示
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                setBypassDnd(true)  // 绕过勿扰模式
                // 设置声音
                setSound(
                    RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION),
                    Notification.AUDIO_ATTRIBUTES_DEFAULT
                )
                // 设置震动模式
                vibrationPattern = longArrayOf(0, 500, 1000)
            }
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
            notificationManager.createNotificationChannel(channel)
            LogX.d(TAG, "Notification channel created")
        }
    }

    fun showNotification(
        title: String, message: String, priority: Int = NotificationCompat.PRIORITY_HIGH
    ) {
        try {
            val context = ContextHolder.context
            LogX.i(TAG, "Attempting to show notification: title='$title', message='$message'")

            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

            // 检查通知权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                if (!notificationManager.areNotificationsEnabled()) {
                    LogX.w(TAG, "Notifications are disabled for this app")
                    return
                }
            }

            val notification = NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID)
                .setSmallIcon(android.R.drawable.stat_notify_error).setContentTitle(title)
                .setContentText(message)
                .setStyle(NotificationCompat.BigTextStyle().bigText(message))
                .setPriority(priority)  // 使用传入的优先级
                .setCategory(NotificationCompat.CATEGORY_ALARM)  // 设置为警报类别
                .setAutoCancel(true).setDefaults(NotificationCompat.DEFAULT_ALL)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                // 关键设置：让通知能够悬浮显示
                .setFullScreenIntent(null, true)  // 全屏意图（可以为null）
                .setTimeoutAfter(30000)  // 30秒后自动消失
                // 增加震动和声音
                .setVibrate(longArrayOf(0, 500, 1000))
                .setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
                // 设置LED灯
                .setLights(Color.RED, 1000, 1000)
                // 显示时间戳
                .setShowWhen(true).setWhen(System.currentTimeMillis()).build()

            notificationManager.notify(NOTIFICATION_ID, notification)
            LogX.i(TAG, "Notification shown successfully: ID=$NOTIFICATION_ID")
        } catch (e: Exception) {
            LogX.e(TAG, "Failed to show notification: ${Log.getStackTraceString(e)}")
        }
    }
}