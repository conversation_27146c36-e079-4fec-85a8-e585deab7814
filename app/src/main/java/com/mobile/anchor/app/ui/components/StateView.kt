package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mobile.anchor.app.data.network.ktnet.error.msg
import com.mobile.anchor.app.ui.lce.PageState

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: He<PERSON><PERSON>
 * @date: 2025/6/9 20:04
 * @description : 通用状态视图组件，支持加载中、空状态、错误状态
 */

/**
 * 通用状态视图组件
 * @param state 页面状态
 * @param emptyTitle 空状态标题
 * @param emptyMessage 空状态描述
 * @param errorTitle 错误状态标题
 * @param errorMessage 错误状态描述（如果为null则使用异常信息）
 * @param onRetry 重试回调
 * @param loadingMessage 加载中提示文字
 * @param modifier 修饰符
 */
@Composable
fun StateView(
    state: PageState,
    modifier: Modifier = Modifier,
    emptyTitle: String = "No data",
    emptyMessage: String = "Data will appear here",
    errorTitle: String = "Load failed",
    errorMessage: String? = null,
    loadingMessage: String? = null,
    showLoading: Boolean = true,
    onRetry: (() -> Unit)? = null
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when (state) {
            is PageState.Loading -> {
                if (showLoading) {
                    LoadingContent(message = loadingMessage)
                }
            }

            is PageState.Empty -> {
                EmptyContent(
                    title = emptyTitle,
                    message = emptyMessage
                )
            }

            is PageState.Error -> {
                ErrorContent(
                    title = errorTitle,
                    message = errorMessage ?: state.throwable.msg
                    ?: "Network error, please try again later",
                    onRetry = onRetry
                )
            }

            else -> {
                // Default 和 Success 状态不显示任何内容
            }
        }
    }
}

/**
 * 加载中内容
 */
@Composable
private fun LoadingContent(message: String?) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CircularProgressIndicator(
            color = Color(0xFF9F2AF8),
            modifier = Modifier.size(32.dp)
        )

        message?.let {
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = it,
                color = Color(0xFF666666),
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal
            )
        }
    }
}

/**
 * 空状态内容
 */
@Composable
private fun EmptyContent(title: String, message: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = title,
            color = Color(0xFF9E9E9E),
            fontSize = 16.sp,
            fontWeight = FontWeight.Normal
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = message,
            color = Color(0xFF666666),
            fontSize = 14.sp,
            fontWeight = FontWeight.Normal
        )
    }
}

/**
 * 错误状态内容
 */
@Composable
private fun ErrorContent(
    title: String,
    message: String,
    onRetry: (() -> Unit)?
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            painter = painterResource(android.R.drawable.ic_dialog_alert),
            contentDescription = "Error",
            tint = Color(0xFF9E9E9E),
            modifier = Modifier.size(48.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = title,
            color = Color(0xFF9E9E9E),
            fontSize = 16.sp,
            fontWeight = FontWeight.Normal
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = message,
            color = Color(0xFF666666),
            fontSize = 14.sp,
            fontWeight = FontWeight.Normal
        )

        onRetry?.let { retry ->
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Tap to retry",
                color = Color(0xFF9F2AF8),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .clickable { retry() }
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            )
        }
    }
}