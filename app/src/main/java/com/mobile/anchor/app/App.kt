package com.mobile.anchor.app

import android.app.Activity
import android.app.Application
import android.os.Bundle
import android.view.MotionEvent
import android.view.Window
import androidx.lifecycle.ProcessLifecycleOwner
import com.bdc.android.library.ktnet.netWorkFailedInterceptor
import com.bdc.android.library.provider.ContextWrapperProvider
import com.bdc.android.library.utils.ActivityManager
import com.bdc.android.library.utils.AppUtil
import com.mobile.anchor.app.data.network.ktnet.interception.MyNetFailedInterceptor
import com.mobile.anchor.app.i18n.I18nContextWrapper
import com.mobile.anchor.app.i18n.I18nManager
import com.mobile.anchor.app.lifecycle.AppLifecycleObserver
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.CallStateManager
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.monitor.NetworkQualityMonitor
import com.mobile.anchor.app.socket.WebSocketManager
import com.mobile.anchor.app.ui.call.CallVideoChatActivity
import com.mobile.anchor.app.utils.ActivityUtils
import com.mobile.anchor.app.utils.ContextHolder
import com.mobile.anchor.app.utils.DeviceStatusUtil
import com.mobile.anchor.app.utils.RongYunUtil
import kotlinx.coroutines.runBlocking

/**
 * 应用程序入口
 * 负责全局初始化工作
 */
class App : Application() {

    override fun onCreate() {
        super.onCreate()
        ContextHolder.init(this)
        setupCrashHandler()

        if (packageName == AppUtil.getProcessName()) {
            // 初始化 ActivityUtils
            ActivityUtils.initialize(this)

            DeviceStatusUtil.registerSignalListener(this)
            //初始化融云
            val saveRongKey = runBlocking {
                DataStoreManager.getString(DataStoreManager.KEY_RONG_YUN_APP_KEY)
            }

            saveRongKey?.let {
                RongYunUtil.initRongYunIM(it)
            }

            // 初始化应用生命周期观察者
            initAppLifecycleObserver()

            // 初始化Activity生命周期回调
            initActivityLifecycleCallbacks()

//            UploadQueueManager.retryAllPending()

            netWorkFailedInterceptor = MyNetFailedInterceptor()


            // 基础初始化I18n管理器（不加载翻译数据）
            I18nManager.init(this)

            ContextWrapperProvider.wrap = { base ->
                I18nContextWrapper(base)
            }
            I18nManager.updateLanguageDataAsync(this)


            // 启动网络质量监控，使用更短的检测间隔以便及时发现网络问题
            val monitor = NetworkQualityMonitor.getInstance(this)
            monitor.startMonitoring(60_000L) // 60秒检测一次

            // 立即触发一次检测
            monitor.triggerImmediateCheck()
        }
    }

    override fun onTerminate() {
        super.onTerminate()
        // 在应用程序终止时释放 WebSocket 资源
        WebSocketManager.getInstance().release()
    }

    private fun setupCrashHandler() {
        val defaultUncaughtExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
            LogX.e("CrashHandler", "应用崩溃！线程: ${thread.name}, 错误信息: ${throwable.message}")
            LogX.e("CrashHandler", throwable) // 打印完整的堆栈信息到日志文件
            // 强制上传日志，确保崩溃信息被记录
            LogX.forceUpload()

            // 调用系统默认的异常处理器，确保应用崩溃行为符合预期
            defaultUncaughtExceptionHandler?.uncaughtException(thread, throwable)
        }
    }

    private fun initActivityLifecycleCallbacks() {
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                ActivityUtils.addActivity(activity)
                ActivityManager.addActivity(activity)
                // 为每个Activity的Window设置一个自定义的Callback，以拦截触摸事件
                val originalCallback = activity.window.callback
                activity.window.callback = object : Window.Callback by originalCallback {
                    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
                        if (event?.action == MotionEvent.ACTION_DOWN) {
                            // 在触摸事件发生时更新全局时间戳
                            WebSocketManager.getInstance()
                                .updateLastAppActionTimestamp(System.currentTimeMillis())
                        }
                        return originalCallback.dispatchTouchEvent(event)
                    }
                }
            }

            override fun onActivityStarted(activity: Activity) {
            }

            override fun onActivityStopped(activity: Activity) {
            }

            override fun onActivityResumed(activity: Activity) {
                WebSocketManager.getInstance()
                    .updateHeartbeatDataPage(activity::class.java.simpleName)

                //如果不在通话界面 强制重置通话状态
                if (ActivityManager.current !is CallVideoChatActivity) {
                    CallStateManager.endCall()
                }
            }

            override fun onActivitySaveInstanceState(
                p0: Activity, p1: Bundle
            ) {
            }

            override fun onActivityPaused(activity: Activity) {
                // Do nothing
            }

            override fun onActivityDestroyed(activity: Activity) {
                ActivityUtils.removeActivity(activity)
                ActivityManager.removeActivity(activity)
            }
        })
    }

    /**
     * 初始化应用生命周期观察者
     */
    private fun initAppLifecycleObserver() {
        // 注册应用生命周期观察者
        ProcessLifecycleOwner.get().lifecycle.addObserver(AppLifecycleObserver.getInstance())
    }
}
