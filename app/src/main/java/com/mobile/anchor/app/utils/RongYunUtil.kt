package com.mobile.anchor.app.utils

import android.content.Context
import android.net.Uri
import android.provider.MediaStore
import android.widget.ImageView
import androidx.core.net.toUri
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.GiftItemBean
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.extension.buildImageUrl
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.conversation.CustomSystemConversationProvider
import com.mobile.anchor.app.ui.conversation.MyRongConversationActivity
import io.rong.imkit.GlideKitImageEngine
import io.rong.imkit.IMCenter
import io.rong.imkit.MyCustomConversationProvider
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversation.extension.MyExtensionConfig
import io.rong.imkit.conversation.extension.RongExtensionManager
import io.rong.imkit.conversation.extension.parsemessage.MikChatAskGiftMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatForMeRechargeMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatGiftMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatSupportHerMessage
import io.rong.imkit.conversation.extension.parsemessage.MikChatSystemMessage
import io.rong.imkit.conversation.extension.provider.AskGiftMessageItemProvider
import io.rong.imkit.conversation.extension.provider.ForMeRechargeMessageItemProvider
import io.rong.imkit.conversation.extension.provider.GiftMessageItemProvider
import io.rong.imkit.conversation.extension.provider.MyTextMessageItemProvider
import io.rong.imkit.conversation.extension.provider.SupportHerMessageItemProvider
import io.rong.imkit.conversation.extension.provider.SystemNoticeMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.TextMessageItemProvider
import io.rong.imkit.conversationlist.provider.PrivateConversationProvider
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.InitOption
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import io.rong.imlib.model.UserInfo
import io.rong.message.SightMessage
import java.util.Locale

object RongYunUtil {

    fun initRongYunIM(rongCloudAppKey: String) {
        val appKey = rongCloudAppKey
        val areaCode: InitOption.AreaCode = InitOption.AreaCode.SG

        val initOption = InitOption.Builder().setAreaCode(areaCode).build()
        IMCenter.init(ContextHolder.context, appKey, initOption)
        RongConfigCenter.featureConfig().kitImageEngine = object : GlideKitImageEngine() {
            override fun loadConversationListPortrait(
                context: Context, url: String, imageView: ImageView, conversation: Conversation
            ) {
                Glide.with(context).load(url).apply(RequestOptions.bitmapTransform(CircleCrop()))
                    .error(R.mipmap.ic_default_avatar).placeholder(R.mipmap.ic_default_avatar)
                    .into(imageView)
            }

            override fun loadConversationPortrait(
                context: Context, url: String, imageView: ImageView, message: Message?
            ) {
                Glide.with(context).load(url).apply(RequestOptions.bitmapTransform(CircleCrop()))
                    .error(R.mipmap.ic_default_avatar).placeholder(R.mipmap.ic_default_avatar)
                    .into(imageView)
            }
        }
        //获取会话模板管理器
        val providerManager = RongConfigCenter.conversationListConfig().providerManager
        //注册一个自定义模板
        providerManager.replaceProvider(
            PrivateConversationProvider::class.java, MyCustomConversationProvider()
        )
        providerManager.addProvider(CustomSystemConversationProvider())
        //注册自己的会话页面
        RouteUtils.registerActivity(
            RouteUtils.RongActivityType.ConversationActivity, MyRongConversationActivity::class.java
        )
//        //自定义扩展
        RongExtensionManager.getInstance().extensionConfig = MyExtensionConfig()
        //增加礼物展示自定义消息
        RongConfigCenter.conversationConfig().apply {
            isShowMoreClickAction = false
            conversationHistoryMessageCount = 100 // 默认拉取历史消息数量
            conversationRemoteMessageCount = 100// 默认拉取远端历史消息数量
            addMessageProvider(GiftMessageItemProvider()) //送礼物消息展示
            addMessageProvider(AskGiftMessageItemProvider()) //索要礼物消息展示
//            addMessageProvider(VideCallMessageItemProvider())//视频通话
            addMessageProvider(SystemNoticeMessageItemProvider())//系统消息
            addMessageProvider(ForMeRechargeMessageItemProvider())//请求为我充值消息
            addMessageProvider(SupportHerMessageItemProvider())//用户支持我消息
            replaceMessageProvider(
                TextMessageItemProvider::class.java, MyTextMessageItemProvider()
            )
//            ) //替换文本消息 新的文本消息不支持链接点击
//            replaceMessageProvider(
//                ImageMessageItemProvider::class.java, MyImageMessageItemProvider()
//            )
//
//            replaceMessageProvider(
//                SightMessageItemProvider::class.java, MySightMessageItemProvider()
//            )
        }

        var localLanguage = Locale.getDefault().language
        if (localLanguage == "zh") { // 对中文简体和繁体做处理
            val country = Locale.getDefault().country
            localLanguage = when (country) {
                "TW", "HK", "MO" -> {
                    //繁体
                    "zh_TW"
                }

                else -> "zh_CN"
            }
        }
        RongConfigCenter.featureConfig().rc_translation_target_language = localLanguage
        //增加自定义消息类型
        val myMessages = ArrayList<Class<out MessageContent?>>()
        myMessages.add(MikChatGiftMessage::class.java)
        myMessages.add(MikChatAskGiftMessage::class.java)
//        myMessages.add(MikChatVideoCallMessage::class.java)
        myMessages.add(MikChatSystemMessage::class.java)
        myMessages.add(SightMessage::class.java)
        myMessages.add(MikChatForMeRechargeMessage::class.java)
        myMessages.add(MikChatSupportHerMessage::class.java)
        RongIMClient.registerMessageType(myMessages)
    }


    fun getPathFromUri(context: Context, uri: Uri): String? {
        var path: String? = null
        val projection = arrayOf(MediaStore.MediaColumns.DATA)
        val cursor = context.contentResolver.query(uri, projection, null, null, null)
        cursor?.use {
            if (it.moveToFirst()) {
                val columnIndex = it.getColumnIndexOrThrow(MediaStore.MediaColumns.DATA)
                path = it.getString(columnIndex)
            }
        }
        return path
    }

    fun sendAskGiftMessage(
        targetId: String,
        giftBean: GiftItemBean,
        callId: String = "",
        type: Conversation.ConversationType = Conversation.ConversationType.PRIVATE
    ) {
        val textMessage: MikChatAskGiftMessage =
            MikChatAskGiftMessage.obtain(
                giftBean.id,
                giftBean.showName,
                giftBean.icon,
                callId,
                giftBean.resource_id
            )
        // 获取特定属性的值
        val message = Message.obtain(targetId, type, textMessage)

        IMCenter.getInstance()
            .sendMessage(
                message,
                (ContextHolder.context.getString(R.string.ask_for_gift) + " : " + giftBean.showName) + " x " + "1",
                null,
                null
            )
    }

    fun cacheSystemNoticeInfo() {
        val baseNickName = ContextHolder.context.getString(R.string.system_notice)
        val baseHeadFileName =
            "https://agent.heartmerge.vip/1734599904598officialNo.png"
        val userInfo = UserInfo(
            Constants.RONG_YUN_ID_SYSTEM, baseNickName, Uri.parse(baseHeadFileName)
        )
        RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
    }


    /**
     * 刷新IM用户信息
     */
    fun refreshCacheUserInfo(userBean: UserBean?) {
        userBean?.let { user ->
            val userInfo = UserInfo(
                user.id,
                user.nickname,
                user.avatar.toUri()
            )
            RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
        }
    }
}