package com.mobile.anchor.app.utils

import java.util.regex.Pattern

/**
 * 字符串工具类
 * 提供常用的字符串处理和验证方法
 */
object StringUtils {
    
    /**
     * 判断字符串是否为空或空白
     */
    fun isEmpty(str: String?): <PERSON><PERSON><PERSON> {
        return str.isNullOrBlank()
    }
    
    /**
     * 判断字符串是否不为空
     */
    fun isNotEmpty(str: String?): <PERSON><PERSON><PERSON> {
        return !str.isNullOrBlank()
    }
    
    /**
     * 安全的字符串转换，避免空指针
     */
    fun safeString(str: String?): String {
        return str ?: ""
    }
    
    /**
     * 验证邮箱格式
     */
    fun isValidEmail(email: String?): <PERSON><PERSON><PERSON> {
        if (isEmpty(email)) return false
        val pattern = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
        )
        return pattern.matcher(email!!).matches()
    }
    
    /**
     * 验证手机号格式（中国大陆）
     */
    fun isValidPhoneNumber(phone: String?): Bo<PERSON>an {
        if (isEmpty(phone)) return false
        val pattern = Pattern.compile("^1[3-9]\\d{9}$")
        return pattern.matcher(phone!!).matches()
    }
    
    /**
     * 验证身份证号格式
     */
    fun isValidIdCard(idCard: String?): Boolean {
        if (isEmpty(idCard)) return false
        val pattern = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$")
        return pattern.matcher(idCard!!).matches()
    }
    
    /**
     * 验证密码强度（至少8位，包含字母和数字）
     */
    fun isValidPassword(password: String?): Boolean {
        if (isEmpty(password) || password!!.length < 8) return false
        val hasLetter = password.any { it.isLetter() }
        val hasDigit = password.any { it.isDigit() }
        return hasLetter && hasDigit
    }
    
    /**
     * 隐藏手机号中间四位
     */
    fun hidePhoneNumber(phone: String?): String {
        if (isEmpty(phone) || phone!!.length != 11) return safeString(phone)
        return "${phone.substring(0, 3)}****${phone.substring(7)}"
    }
    
    /**
     * 隐藏邮箱用户名部分
     */
    fun hideEmail(email: String?): String {
        if (isEmpty(email) || !isValidEmail(email)) return safeString(email)
        val atIndex = email!!.indexOf('@')
        if (atIndex <= 1) return email
        val username = email.substring(0, atIndex)
        val domain = email.substring(atIndex)
        return when {
            username.length <= 2 -> "${username[0]}*$domain"
            username.length <= 4 -> "${username[0]}**${username.last()}$domain"
            else -> "${username.substring(0, 2)}***${username.last()}$domain"
        }
    }
    
    /**
     * 隐藏身份证号中间部分
     */
    fun hideIdCard(idCard: String?): String {
        if (isEmpty(idCard) || idCard!!.length != 18) return safeString(idCard)
        return "${idCard.substring(0, 6)}********${idCard.substring(14)}"
    }
    
    /**
     * 格式化文件大小
     */
    fun formatFileSize(bytes: Long): String {
        val kb = 1024.0
        val mb = kb * 1024
        val gb = mb * 1024
        val tb = gb * 1024
        
        return when {
            bytes >= tb -> String.format("%.2f TB", bytes / tb)
            bytes >= gb -> String.format("%.2f GB", bytes / gb)
            bytes >= mb -> String.format("%.2f MB", bytes / mb)
            bytes >= kb -> String.format("%.2f KB", bytes / kb)
            else -> "$bytes B"
        }
    }
    
    /**
     * 生成随机字符串
     */
    fun generateRandomString(length: Int): String {
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        return (1..length)
            .map { chars.random() }
            .joinToString("")
    }
    
    /**
     * 首字母大写
     */
    fun capitalize(str: String?): String {
        if (isEmpty(str)) return ""
        return str!!.replaceFirstChar { 
            if (it.isLowerCase()) it.titlecase() else it.toString() 
        }
    }
    
    /**
     * 驼峰命名转下划线
     */
    fun camelToSnake(str: String?): String {
        if (isEmpty(str)) return ""
        return str!!.replace(Regex("([a-z])([A-Z])"), "$1_$2").lowercase()
    }
    
    /**
     * 下划线转驼峰命名
     */
    fun snakeToCamel(str: String?): String {
        if (isEmpty(str)) return ""
        return str!!.split("_").joinToString("") { word ->
            if (word.isNotEmpty()) word.replaceFirstChar { 
                if (it.isLowerCase()) it.titlecase() else it.toString() 
            } else word
        }
    }
    
    /**
     * 移除所有空白字符
     */
    fun removeAllWhitespace(str: String?): String {
        if (isEmpty(str)) return ""
        return str!!.replace(Regex("\\s+"), "")
    }
    
    /**
     * 截取字符串，超出部分用省略号表示
     */
    fun ellipsize(str: String?, maxLength: Int): String {
        if (isEmpty(str) || str!!.length <= maxLength) return safeString(str)
        return "${str.substring(0, maxLength - 3)}..."
    }
    
    /**
     * 判断字符串是否只包含数字
     */
    fun isNumeric(str: String?): Boolean {
        if (isEmpty(str)) return false
        return str!!.all { it.isDigit() }
    }
    
    /**
     * 判断字符串是否只包含字母
     */
    fun isAlpha(str: String?): Boolean {
        if (isEmpty(str)) return false
        return str!!.all { it.isLetter() }
    }
    
    /**
     * 判断字符串是否只包含字母和数字
     */
    fun isAlphanumeric(str: String?): Boolean {
        if (isEmpty(str)) return false
        return str!!.all { it.isLetterOrDigit() }
    }
}
