package com.mobile.anchor.app.data.model

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/6/17 15:49
 * @description :
 */
enum class RelationList(val value: String) {
    //关注列表
    FOLLOW("anchor_follow_user"),

    //粉丝列表
    FANS("user_follow_anchor"),

    //拉黑列表
    BLOCK("anchor_block_user"),

    //付费用户列表
    LOVER("anchor_lover_user"),

    //为我充值用户列表
    FOR_ME("anchor_for_me_recharge")
}