package com.mobile.anchor.app.data.network.ktnet

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 2:42 下午
 * @description： 网络相关的全局变量
 */
object NetConstant {
    //成功返回的code
    const val SUCCESS_CODE = 0

    //空数据
    const val EMPTY_CODE = 99999

    //默认errorCode 比如json解析错误等
    const val DEFAULT_ERROR_CODE = -1000

    //服务器内部错误
    const val SERVER_ERROR = 500

    //用户相关错误
    const val USER_ERROR = 1000

    //用户未注册
    const val USER_UN_REGISTER = 1001

    //密码错误
    const val PASSWORD_ERROR = 1003

    //验证码错误
    const val ERROR_VERIFY_CODE = 1010

    //获取验证码太频繁
    const val VERIFY_CODE_MULT = 1011

    //请求方法不存在
    const val NULL_METHOD = 1020

}