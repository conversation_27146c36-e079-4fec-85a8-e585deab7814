package com.mobile.anchor.app.ui.screens.mine

import androidx.lifecycle.viewModelScope
import com.mobile.anchor.app.data.model.TaskBean
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.UserApiService
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.viewmodels.BaseViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/16 21:00
 * @description : 奖励任务ViewModel
 */
class RewardTaskViewModel : BaseViewModel() {

    private val userApiService: UserApiService by NetDelegates()

    private val _taskList = MutableStateFlow<List<TaskBean>>(emptyList())
    val taskList: StateFlow<List<TaskBean>> = _taskList.asStateFlow()

    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing.asStateFlow()

    /**
     * 加载任务列表
     */
    fun loadTaskList() {
        ktHttpRequest {
            val response = userApiService.getTaskList().await()
            response?.let { pageResponse ->
                _taskList.value = pageResponse.records?.filter { it.isAnchorTask } ?: emptyList()
                LogX.d("获取任务列表成功，共${pageResponse.records?.size ?: 0}个任务")
            }
        }
    }

    /**
     * 刷新任务列表
     */
    fun refreshTaskList() {
        viewModelScope.launch {
            _isRefreshing.value = true
            try {
                val response = userApiService.getTaskList().await()
                response?.let { pageResponse ->
                    _taskList.value = pageResponse.records ?: emptyList()
                    LogX.d("刷新任务列表成功")
                }
            } catch (e: Exception) {
                LogX.e("刷新任务列表失败: ${e.message}")
            } finally {
                _isRefreshing.value = false
            }
        }
    }

    /**
     * 领取奖励
     */
    fun claimReward(taskId: String) {
        viewModelScope.launch {
            try {
                // TODO: 调用领取奖励接口
                LogX.d("领取任务奖励: $taskId")
                // 领取成功后刷新列表
                loadTaskList()
            } catch (e: Exception) {
                LogX.e("领取奖励失败: ${e.message}")
            }
        }
    }
}
