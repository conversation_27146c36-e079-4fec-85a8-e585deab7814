package com.mobile.anchor.app.utils;


import android.os.Environment;

import com.bdc.android.library.utils.AppManager;

import java.io.File;
import java.io.IOException;


/**
 * 文件路径获取工具类
 * <p>
 * 为了适配Android Q
 * 在Android Q中，移除了READ_EXTERNAL_STORAGE 和 WRITE_EXTERNAL_STORAGE两个权限
 * 采用专属沙盒来存储文件，谷歌官方推荐应用在沙盒内存储文件的地址为Context.getExternalFilesDir()下的文件夹
 */
public class PathUtil {


    //自定义文件夹名称
    private static final String PATH_CRASH = "crash";
    private static final String HTTP_CACHE = "HttpCache";
    private static final String VOICE = "voice";
    private static final String GOODS = "goods";
    private static final String APK = "apk";
    private static final String IMG = "img";
    private static final String VIDEO = "video";
    private static final String NOTIFICATION = "Notification";
    private static final String MUSIC = "music";

    public static String getRootCachePath(String dirName) {
        if (SDCardUtil.ExistSDCard()) {
            File f = AppManager.INSTANCE.getApplication().getExternalFilesDir(dirName);
            if (f == null) {
                File cacheDir = AppManager.INSTANCE.getApplication().getCacheDir();
                return cacheDir.getPath();
            } else {
                return f.getPath();
            }
        } else {
            File filesDir = AppManager.INSTANCE.getApplication().getFilesDir();
            return filesDir.getPath() + "/" + dirName;
        }
    }

    public static String getInnerRootCachePath(String dirName) {
        File f = AppManager.INSTANCE.getApplication().getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS + dirName);
        if (f == null) {
            File cacheDir = AppManager.INSTANCE.getApplication().getCacheDir();
            return cacheDir.getPath();
        } else {
            return f.getPath();
        }
    }

    public static String getPathCrash() {
        String resultPath = getRootCachePath(PATH_CRASH);
        checkDir(resultPath);
        return resultPath;
    }

    public static String getHttpCache() {
        String resultPath = getRootCachePath(HTTP_CACHE);
        checkDir(resultPath);
        return resultPath;
    }


    public static String getPathPic() {
        String resultPath = getRootCachePath(Environment.DIRECTORY_PICTURES);
        checkDir(resultPath);
        return resultPath;
    }

    public static String getPathIMg() {
        String resultPath = getRootCachePath(IMG);
        checkDir(resultPath);
        return resultPath;
    }

    public static String getPathVideo() {
        String resultPath = getRootCachePath(VIDEO);
        checkDir(resultPath);
        return resultPath;
    }

    public static String getPathNotification() {
        String resultPath = getRootCachePath(NOTIFICATION);
        checkDir(resultPath);
        return resultPath;
    }

    public static String getPathIMgChache() {
        String resultPath = getRootCachePath(IMG + "/img_chache");
        checkDir(resultPath);
        return resultPath;
    }

    public static String getInnerPathIMgChacheIn() {
        String resultPath = getInnerRootCachePath(IMG + "/img_chache");
        checkDir(resultPath);
        return resultPath;
    }

    public static String getPathDownload() {
        String resultPath = getRootCachePath(Environment.DIRECTORY_DOWNLOADS);
        checkDir(resultPath);
        return resultPath;
    }


    public static String getPathVoice() {
        String resultPath = getRootCachePath(VOICE);
        checkDir(resultPath);
        return resultPath;
    }

    public static String getPathApk() {
        String resultPath = getRootCachePath(APK);
        checkDir(resultPath);
        return resultPath;
    }

    public static String getPathGoods() {
        String resultPath = getRootCachePath(GOODS);
        checkDir(resultPath);
        return resultPath;
    }

    public static String getPathGoods2() {
        String gift;
        try {
            String[] gifts = AppManager.INSTANCE.getApplication().getAssets().list("gift");
            gift = gifts[0];
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

//        NanApplication.context.getAssets().open()
//        String resultPath = getRootCachePath(GOODS);
        String resultPath = "file:///android_asset/gift/";
        return gift;
    }

    /**
     * 音乐下载路径
     *
     * @return path
     */
    public static String getPathMusics() {
        String resultPath = getRootCachePath(MUSIC);
        checkDir(resultPath);
        return resultPath;
    }

    private static void checkDir(String resultPath) {
        File dir = new File(resultPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }


}
