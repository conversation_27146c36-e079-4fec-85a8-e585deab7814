package com.mobile.anchor.app.data.model

import android.net.Uri

/**
 * 视频录制状态
 */
sealed class VideoRecordState {
    /**
     * 录制前状态 - 显示相机预览
     */
    object BeforeRecord : VideoRecordState()

    /**
     * 录制中状态
     * @param duration 已录制时长（秒）
     * @param isPaused 是否暂停
     */
    data class Recording(
        val duration: Int = 0,
        val isPaused: Boolean = false
    ) : VideoRecordState()

    /**
     * 录制完成状态
     * @param videoUri 录制的视频文件URI
     * @param duration 录制总时长（秒）
     * @param isPlaying 是否正在播放视频
     * @param currentPlayPosition 当前播放位置（秒）
     */
    data class RecordCompleted(
        val videoUri: Uri,
        val duration: Int,
        val isPlaying: Boolean = false,
        val currentPlayPosition: Int = 0
    ) : VideoRecordState()
}

/**
 * 录制UI状态
 */
data class FaceRecordUiState(
    val recordState: VideoRecordState = VideoRecordState.BeforeRecord,
    val isUploading: Boolean = false,
    val uploadProgress: Float = 0f,
    val completed: Boolean = false,
    val errorMessage: String? = null,
    val hasPermission: <PERSON>olean = false
)
