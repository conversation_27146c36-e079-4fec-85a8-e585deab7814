package com.mobile.anchor.app.utils

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import com.mobile.anchor.app.logger.LogX
import java.util.*

/**
 * Activity 管理工具类
 * 管理所有 Activity 的实例，并提供全局 Context 获取
 */
object ActivityUtils {

    private val stack = Stack<Activity>()
    private var application: Application? = null

    /**
     * 初始化 ActivityUtils
     * 应该在 Application.onCreate() 中调用
     */
    fun initialize(application: Application) {
        this.application = application
    }

    /**
     * 获取全局 Application Context
     */
    fun getApplicationContext(): Context {
        return application ?: throw IllegalStateException(
            "ActivityUtils 未初始化！请在 Application.onCreate() 中调用 ActivityUtils.initialize(this)"
        )
    }

    /**
     * 获取当前 Activity Context（如果有的话）
     */
    fun getCurrentContext(): Context? {
        return if (stack.isNotEmpty()) stack.lastElement() else null
    }

    /**
     * 获取可用的 Context（优先返回当前 Activity，否则返回 Application）
     */
    fun getContext(): Context {
        return getCurrentContext() ?: getApplicationContext()
    }

    /**
     * 添加 Activity
     */
    @Synchronized
    fun addActivity(activity: Activity) {
        stack.add(activity)
    }

    /**
     * 移除 Activity
     */
    @Synchronized
    fun removeActivity(activity: Activity) {
        stack.remove(activity)
    }

    /**
     * 结束指定类名的 Activity
     */
    fun finishActivity(cls: Class<*>) {
        for (activity in stack) {
            if (activity.javaClass == cls) {
                finishActivity(activity)
                return
            }
        }
    }

    /**
     * 结束指定的 Activity
     */
    fun finishActivity(activity: Activity?) {
        if (activity != null) {
            activity.finish()
            stack.remove(activity)
        }
    }

    /**
     * 结束所有 Activity
     */
    fun finishAllActivity() {
        for (activity in stack) {
            activity?.finish()
        }
        stack.clear()
    }

    /**
     * 结束所有 Activity（除了指定类名的）
     */
    fun finishAllActivityExcept(className: String) {
        val toRemove = mutableListOf<Activity>()
        for (activity in stack) {
            if (activity != null && activity.componentName.className != className) {
                activity.finish()
                toRemove.add(activity)
            }
        }
        stack.removeAll(toRemove)
    }

    /**
     * 结束所有 Activity（除了指定的）
     */
    fun finishAllActivityExcept(exceptActivity: Activity) {
        val toRemove = mutableListOf<Activity>()
        for (activity in stack) {
            if (activity != null && activity != exceptActivity) {
                activity.finish()
                toRemove.add(activity)
            }
        }
        stack.removeAll(toRemove)
    }

    /**
     * 结束所有 Activity 然后跳转到登录页
     */
    fun finishAllActivityThenLogin() {
        try {
            // 这里可以根据实际项目调整登录页面的类名
            val loginClass = Class.forName("com.mobile.anchor.app.ui.screens.login.LoginActivity")
            val current = getCurrent()
            if (current != null) {
                val intent = Intent(current, loginClass)
                current.startActivity(intent)
            }
        } catch (e: ClassNotFoundException) {
            LogX.e("登录页面类未找到", e)
        }
        finishAllActivity()
    }

    /**
     * 获取 Activity 栈
     */
    fun getStack(): Stack<Activity> {
        return stack
    }

    /**
     * 获取当前 Activity
     */
    fun getCurrent(): Activity? {
        return if (stack.isNotEmpty()) stack.lastElement() else null
    }

    /**
     * 获取栈中 Activity 数量
     */
    fun getActivityCount(): Int {
        return stack.size
    }

    /**
     * 检查是否有 Activity 在栈中
     */
    fun hasActivity(): Boolean {
        return stack.isNotEmpty()
    }

    /**
     * 检查指定类的 Activity 是否在栈中
     */
    fun hasActivity(cls: Class<*>): Boolean {
        return stack.any { it.javaClass == cls }
    }

    /**
     * 获取指定类的 Activity
     */
    fun getActivity(cls: Class<*>): Activity? {
        return stack.find { it.javaClass == cls }
    }

    /**
     * 打印当前 Activity 栈信息（用于调试）
     */
    fun printStack() {
        LogX.d("Activity Stack (${stack.size} activities):")
        stack.forEachIndexed { index, activity ->
            LogX.d("  [$index] ${activity.javaClass.simpleName}")
        }
    }
}
