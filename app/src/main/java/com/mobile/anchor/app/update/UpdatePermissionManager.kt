package com.mobile.anchor.app.update

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.mobile.anchor.app.logger.LogX

/**
 * 更新权限管理器
 * 处理应用更新所需的各种权限
 */
object UpdatePermissionManager {
    
    private const val REQUEST_CODE_NOTIFICATION = 1001
    private const val REQUEST_CODE_INSTALL = 1002
    private const val REQUEST_CODE_STORAGE = 1003
    
    /**
     * 检查通知权限
     */
    fun checkNotificationPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true // Android 13 以下默认有通知权限
        }
    }
    
    /**
     * 请求通知权限
     */
    fun requestNotificationPermission(activity: Activity, callback: (Boolean) -> Unit) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (checkNotificationPermission(activity)) {
                callback(true)
                return
            }
            
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                REQUEST_CODE_NOTIFICATION
            )
        } else {
            callback(true)
        }
    }
    
    /**
     * 检查安装权限
     */
    fun checkInstallPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.packageManager.canRequestPackageInstalls()
        } else {
            true // Android 8.0 以下默认有安装权限
        }
    }
    
    /**
     * 请求安装权限
     */
    fun requestInstallPermission(activity: Activity, callback: (Boolean) -> Unit) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (checkInstallPermission(activity)) {
                callback(true)
                return
            }
            
            try {
                val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES).apply {
                    data = Uri.parse("package:${activity.packageName}")
                }
                activity.startActivityForResult(intent, REQUEST_CODE_INSTALL)
            } catch (e: Exception) {
                LogX.e("请求安装权限失败", e)
                callback(false)
            }
        } else {
            callback(true)
        }
    }
    
    /**
     * 检查存储权限
     */
    fun checkStoragePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 不需要存储权限来下载文件到应用私有目录
            true
        } else {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 请求存储权限
     */
    fun requestStoragePermission(activity: Activity, callback: (Boolean) -> Unit) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            callback(true)
            return
        }
        
        if (checkStoragePermission(activity)) {
            callback(true)
            return
        }
        
        ActivityCompat.requestPermissions(
            activity,
            arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
            REQUEST_CODE_STORAGE
        )
    }
    
    /**
     * 检查所有必要权限
     */
    fun checkAllPermissions(context: Context): Boolean {
        return checkNotificationPermission(context) && 
               checkInstallPermission(context) && 
               checkStoragePermission(context)
    }
    
    /**
     * 请求所有必要权限
     */
    fun requestAllPermissions(activity: Activity, callback: (Boolean) -> Unit) {
        var permissionsGranted = 0
        val totalPermissions = 3
        
        val checkComplete = {
            if (permissionsGranted >= totalPermissions) {
                callback(true)
            }
        }
        
        // 请求通知权限
        requestNotificationPermission(activity) { granted ->
            if (granted) permissionsGranted++
            checkComplete()
        }
        
        // 请求安装权限
        requestInstallPermission(activity) { granted ->
            if (granted) permissionsGranted++
            checkComplete()
        }
        
        // 请求存储权限
        requestStoragePermission(activity) { granted ->
            if (granted) permissionsGranted++
            checkComplete()
        }
    }
    
    /**
     * 处理权限请求结果
     */
    fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
        callback: (Boolean) -> Unit
    ) {
        when (requestCode) {
            REQUEST_CODE_NOTIFICATION,
            REQUEST_CODE_STORAGE -> {
                val granted = grantResults.isNotEmpty() && 
                             grantResults[0] == PackageManager.PERMISSION_GRANTED
                callback(granted)
            }
        }
    }
    
    /**
     * 处理Activity结果
     */
    fun onActivityResult(requestCode: Int, resultCode: Int, callback: (Boolean) -> Unit) {
        when (requestCode) {
            REQUEST_CODE_INSTALL -> {
                // 重新检查安装权限
                callback(true) // 不管用户是否授权，都继续流程
            }
        }
    }
    
    /**
     * 打开应用设置页面
     */
    fun openAppSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            LogX.e("打开应用设置失败", e)
        }
    }
}
