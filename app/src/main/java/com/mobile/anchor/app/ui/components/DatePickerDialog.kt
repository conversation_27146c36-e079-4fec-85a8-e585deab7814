package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.snapping.rememberSnapFlingBehavior
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomSheetScaffold
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SheetValue
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberBottomSheetScaffoldState
import androidx.compose.material3.rememberStandardBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.theme.Surface
import kotlinx.coroutines.launch
import java.util.Calendar

/**
 * 日期选择器弹窗组件
 *
 * @param visible 是否显示弹窗
 * @param title 弹窗标题
 * @param initialDate 初始日期
 * @param onDismiss 关闭弹窗回调
 * @param onDateSelected 日期选择回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatePickerDialog(
    visible: Boolean,
    title: String = "Select Date",
    initialDate: Calendar? = null,
    onDismiss: () -> Unit,
    onDateSelected: (Calendar) -> Unit
) {
    val coroutineScope = rememberCoroutineScope()

    // 创建底部表单状态 - 始终从Hidden开始
    val bottomSheetState = rememberStandardBottomSheetState(
        initialValue = SheetValue.Hidden,
        skipHiddenState = false
    )

    val scaffoldState = rememberBottomSheetScaffoldState(
        bottomSheetState = bottomSheetState
    )

    // 监听 visible 状态变化
    LaunchedEffect(visible) {
        if (visible) {
            bottomSheetState.expand()
        } else {
            bottomSheetState.hide()
        }
    }

    // 监听底部表单状态变化 - 只在用户手动关闭时触发
    LaunchedEffect(bottomSheetState.currentValue) {
        if (bottomSheetState.currentValue == SheetValue.Hidden && visible || bottomSheetState.targetValue == SheetValue.PartiallyExpanded) {
            onDismiss()
        }
    }
    if (visible) {
        BottomSheetScaffold(
            scaffoldState = scaffoldState,
            sheetContent = {
                DatePickerContent(
                    title = title,
                    initialDate = initialDate,
                    onDismiss = {
                        coroutineScope.launch {
                            bottomSheetState.hide()
                        }
                    },
                    onDateSelected = { calendar ->
                        onDateSelected(calendar)
                        coroutineScope.launch {
                            bottomSheetState.hide()
                        }
                    }
                )
            },
            sheetPeekHeight = 0.dp,
            sheetShape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
            sheetContainerColor = Surface,
            sheetDragHandle = null,
            containerColor = Color.Transparent,
            modifier = Modifier.fillMaxSize()
        ) {
            // 背景遮罩 - 只在visible时显示
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable {
                        coroutineScope.launch {
                            bottomSheetState.hide()
                        }
                    }
            )
        }
    }
}

@Composable
private fun DatePickerContent(
    title: String, initialDate: Calendar?, onDismiss: () -> Unit, onDateSelected: (Calendar) -> Unit
) {
    val currentDate = Calendar.getInstance()
    val currentYear = currentDate.get(Calendar.YEAR)
    val currentMonth = currentDate.get(Calendar.MONTH) + 1
    val currentDay = currentDate.get(Calendar.DAY_OF_MONTH)

    // 计算18岁限制的最大日期
    val maxAllowedDate = Calendar.getInstance().apply {
        add(Calendar.YEAR, -18)
    }
    val maxYear = maxAllowedDate.get(Calendar.YEAR)

    // 年份范围：1950年到18年前
    val years = (1950..maxYear).toList()
    val months = listOf(
        "01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"
    )

    // 默认日期为当前日期往前推18年，确保不超过限制
    val defaultDate = initialDate?.let { date ->
        if (isDateValid(date, maxAllowedDate)) date else maxAllowedDate
    } ?: maxAllowedDate

    var selectedYear by remember { mutableStateOf(defaultDate.get(Calendar.YEAR)) }
    var selectedMonth by remember { mutableStateOf(defaultDate.get(Calendar.MONTH) + 1) }
    var selectedDay by remember { mutableStateOf(defaultDate.get(Calendar.DAY_OF_MONTH)) }

    // 动态计算当前年月的天数，包括闰年处理
    val daysInMonth = remember(selectedYear, selectedMonth) {
        getDaysInMonth(selectedYear, selectedMonth)
    }

    // 计算可选择的天数范围，考虑18岁限制
    val availableDays = remember(selectedYear, selectedMonth, maxAllowedDate) {
        val maxDayInMonth = getDaysInMonth(selectedYear, selectedMonth)
        if (selectedYear == maxYear && selectedMonth == maxAllowedDate.get(Calendar.MONTH) + 1) {
            // 如果是限制年份的限制月份，只能选择到限制日期
            val maxAllowedDay = maxAllowedDate.get(Calendar.DAY_OF_MONTH)
            (1..minOf(maxDayInMonth, maxAllowedDay)).toList()
        } else {
            (1..maxDayInMonth).toList()
        }
    }

    // 当年月变化时，确保选中的日期有效
    LaunchedEffect(selectedYear, selectedMonth) {
        val maxDayInMonth = getDaysInMonth(selectedYear, selectedMonth)
        val maxAllowedDay =
            if (selectedYear == maxYear && selectedMonth == maxAllowedDate.get(Calendar.MONTH) + 1) {
                maxAllowedDate.get(Calendar.DAY_OF_MONTH)
            } else {
                maxDayInMonth
            }

        if (selectedDay > minOf(maxDayInMonth, maxAllowedDay)) {
            selectedDay = minOf(maxDayInMonth, maxAllowedDay)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Surface)
            .padding(horizontal = 0.dp, vertical = 16.dp)
    ) {
        // 标题和按钮
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextButton(onClick = onDismiss) {
                Text(
                    text = "Cancel", color = Color(0xFF999999), fontSize = 16.sp
                )
            }

            Text(
                text = title,
                color = Color.White,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )

            TextButton(onClick = {
                val calendar = Calendar.getInstance()
                calendar.set(selectedYear, selectedMonth - 1, selectedDay)

                // 验证选择的日期是否满足18岁限制
                if (isDateValid(calendar, maxAllowedDate)) {
                    onDateSelected(calendar)
                } else {
                    // 如果日期无效，自动调整到最大允许日期
                    onDateSelected(maxAllowedDate)
                }
            }) {
                Text(
                    text = "Confirm",
                    color = Primary,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))


        // 日期选择器
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp), // 固定高度确保一致性
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.Top
        ) {
            // 年份选择
            ScrollablePickerColumn(
                items = years.map { it.toString() },
                selectedIndex = years.indexOf(selectedYear),
                onItemSelected = { index -> selectedYear = years[index] },
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight(),
                label = "Year"
            )

            // 月份选择
            ScrollablePickerColumn(
                items = months,
                selectedIndex = selectedMonth - 1,
                onItemSelected = { index -> selectedMonth = index + 1 },
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight(),
                label = "Month"
            )

            // 日期选择 - 使用限制后的可用天数
            ScrollablePickerColumn(
                items = availableDays.map { it.toString().padStart(2, '0') },
                selectedIndex = availableDays.indexOf(selectedDay),
                onItemSelected = { index ->
                    if (index in availableDays.indices) {
                        selectedDay = availableDays[index]
                    }
                },
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight(),
                label = "Day"
            )
        }

        // 底部安全区域
        Spacer(modifier = Modifier.height(16.dp))
    }
}

/**
 * 可滑动的选择器列
 */
@Composable
private fun ScrollablePickerColumn(
    items: List<String>,
    selectedIndex: Int,
    onItemSelected: (Int) -> Unit,
    modifier: Modifier = Modifier,
    label: String
) {
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    val snapBehavior = rememberSnapFlingBehavior(lazyListState = listState)

    // 初始化滚动到选中项 - 使用两阶段初始化
    LaunchedEffect(Unit) {
        if (selectedIndex in items.indices && items.isNotEmpty()) {
            // 第一阶段：等待BottomSheet动画完成
            kotlinx.coroutines.delay(400)
            // 第二阶段：立即滚动到选中位置
            listState.scrollToItem(selectedIndex)
        }
    }

    // 监听selectedIndex变化（用于动态更新）
    LaunchedEffect(selectedIndex) {
        if (selectedIndex in items.indices && items.isNotEmpty() && listState.layoutInfo.totalItemsCount > 0) {
            // 如果已经初始化完成，使用动画滚动
            listState.animateScrollToItem(selectedIndex)
        }
    }

    // 监听滚动状态，自动选择中间的项
    LaunchedEffect(listState.isScrollInProgress) {
        if (!listState.isScrollInProgress) {
            // 添加小延迟确保滚动完全停止
            kotlinx.coroutines.delay(50)
            val layoutInfo = listState.layoutInfo
            val visibleItems = layoutInfo.visibleItemsInfo
            if (visibleItems.isNotEmpty()) {
                // 找到最接近中心的项
                val centerY = layoutInfo.viewportSize.height / 2
                val centerItem = visibleItems.minByOrNull {
                    kotlin.math.abs((it.offset + it.size / 2) - centerY)
                }
                centerItem?.let { item ->
                    // 调整索引，因为我们添加了空白项
                    val actualIndex = item.index - 2
                    if (actualIndex != selectedIndex && actualIndex in items.indices) {
                        onItemSelected(actualIndex)
                    }
                }
            }
        }
    }

    Column(
        modifier = modifier.fillMaxHeight(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 标签区域 - 固定高度确保对齐
        Box(
            modifier = Modifier.height(24.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = label,
                color = Color(0xFF999999),
                fontSize = 12.sp
            )
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f) // 使用weight让选择器占用剩余空间
        ) {
            // 添加空白项以便居中显示，显示5行需要前后各加2个空白项
            val paddedItems = listOf("", "") + items + listOf("", "")

            LazyColumn(
                state = listState,
                flingBehavior = snapBehavior,
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                contentPadding = PaddingValues(vertical = 0.dp)
            ) {
                itemsIndexed(paddedItems) { index, item ->
                    val actualIndex = index - 2
                    val isSelected = actualIndex == selectedIndex
                    val isVisible = item.isNotEmpty()

                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(57.dp) // 增加高度确保更好的视觉效果和对齐
                            .clickable(enabled = isVisible) {
                                if (actualIndex in items.indices) {
                                    onItemSelected(actualIndex)
                                    // 点击后滚动到选中位置
                                    coroutineScope.launch {
                                        listState.animateScrollToItem(actualIndex)
                                    }
                                }
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = if (isVisible) item else "",
                            color = when {
                                !isVisible -> Color.Transparent
                                isSelected -> Primary
                                else -> Color.White.copy(alpha = 0.7f)
                            },
                            fontSize = when {
                                !isVisible -> 16.sp
                                isSelected -> 20.sp
                                else -> 16.sp
                            },
                            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            // 中心指示线 - 上下两条线形成选中区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(57.dp)
                    .align(Alignment.Center)
            ) {
                // 上边线
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(0.5.dp)
                        .background(Primary.copy(alpha = 0.2f))
                        .align(Alignment.TopCenter)
                )
                // 下边线
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(0.5.dp)
                        .background(Primary.copy(alpha = 0.2f))
                        .align(Alignment.BottomCenter)
                )
            }
        }
    }
}

/**
 * 计算指定年月的天数，包括闰年处理
 */
private fun getDaysInMonth(year: Int, month: Int): Int {
    return when (month) {
        1, 3, 5, 7, 8, 10, 12 -> 31  // 大月
        4, 6, 9, 11 -> 30             // 小月
        2 -> if (isLeapYear(year)) 29 else 28  // 二月，考虑闰年
        else -> 30
    }
}

/**
 * 判断是否为闰年
 * 闰年规则：
 * 1. 能被4整除且不能被100整除
 * 2. 或者能被400整除
 */
private fun isLeapYear(year: Int): Boolean {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)
}

/**
 * 验证日期是否有效（不超过18岁限制）
 */
private fun isDateValid(date: Calendar, maxAllowedDate: Calendar): Boolean {
    return date.timeInMillis <= maxAllowedDate.timeInMillis
}

@Preview
@Composable
fun DatePickerDialogPreview() {
    AnchorTheme {
        DatePickerDialog(
            visible = true,
            title = "Select Birthday",
            onDismiss = {},
            onDateSelected = {})
    }
}

@Preview
@Composable
fun DatePickerDialogWithInitialDatePreview() {
    AnchorTheme {
        val initialDate = Calendar.getInstance().apply {
            set(2000, 5, 15) // 2000年6月15日 - 满足18岁限制
        }
        DatePickerDialog(
            visible = true,
            title = "Select Birthday (18+ only)",
            initialDate = initialDate,
            onDismiss = {},
            onDateSelected = {})
    }
}
