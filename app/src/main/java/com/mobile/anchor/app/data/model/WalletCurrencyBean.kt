package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/6/23 18:06
 * @description :
 */

@Parcelize
@JsonClass(generateAdapter = true)
data class WalletWrapperCurrencyBean(val list_v2: List<WalletCurrencyBean>?) : Parcelable


@Parcelize
@JsonClass(generateAdapter = true)
data class WalletCurrencyBean(
    val balance: Int = 0,
    val currency: String = "",
    val to_currency_money: Float = 0f,
    val min_with_draw_coin: Int = 0,
    val set_fee_rate: Float = 0f,
    val set_fee_amount: Int = 0,
    val set_fee_type: Int = 0,//0-无提现手续费，1-提现手续费，2-提现手续费率
    val apply_stat: Int = 0,//是否可提现 0-不可提现，1-可提现
    val withdraw_rule: String = ""
) : Parcelable {

    val isEnabled: Boolean
        get() = apply_stat == 1
}
