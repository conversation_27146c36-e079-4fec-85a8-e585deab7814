package com.mobile.anchor.app.extension

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter

class ViewPagerFmAdapter(
    manager: FragmentManager,
    lifeCycle:Lifecycle,
    private val mFragments: List<Fragment>
) : FragmentStateAdapter(manager,lifeCycle) {
    override fun getItemCount(): Int {
        return mFragments.size
    }

    override fun createFragment(position: Int): Fragment {
        return mFragments[position]
    }
}