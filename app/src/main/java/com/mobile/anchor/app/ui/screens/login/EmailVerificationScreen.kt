package com.mobile.anchor.app.ui.screens.login

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.EmailVerificationUiState
import com.mobile.anchor.app.ui.components.AnchorButton
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.CaptchaTextField
import com.mobile.anchor.app.ui.components.CaptchaType
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Background
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.utils.ContextHolder.context

/**
 * 邮箱验证页面Compose实现
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailVerificationScreen(
    viewModel: LoginViewModel,
    onLoginSuccess: () -> Unit = {},
    onNavigateBack: () -> Unit = {},
) {
    val uiState by viewModel.emailVerificationUiState.collectAsStateWithLifecycle()

    // 监听登录成功
    LaunchedEffect(uiState.isLoginSuccessful) {
        if (uiState.isLoginSuccessful) {
            onLoginSuccess()
        }
    }

    EmailVerificationContent(
        uiState = uiState,
        onNavigateBack = {
            // 返回时重置验证码发送状态，避免自动跳转
            viewModel.resetCodeSentState()
            // 延迟执行导航，确保状态重置完成
            onNavigateBack()
        },
        onResendCode = viewModel::resendCode,
        onLogin = viewModel::loginWithCode,
        onVerificationCodeChange = viewModel::updateVerificationCode,
        onClearError = viewModel::clearError,
        formatTime = viewModel::formatTime
    )
}

@Composable
private fun EmailVerificationContent(
    uiState: EmailVerificationUiState,
    onNavigateBack: () -> Unit = {},
    onResendCode: () -> Unit,
    onLogin: () -> Unit,
    onVerificationCodeChange: (String) -> Unit = {},
    onClearError: () -> Unit = {},
    formatTime: (Int) -> String = { "00:00" },
    modifier: Modifier = Modifier
) {
    AnchorScaffold(topBar = {
        AnchorTopBar(
            title = context.getString(R.string.email_verification), onNavigationClick = onNavigateBack
        )
    }) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .background(Background)
                .padding(16.dp)
                .padding(top = paddingValues.calculateTopPadding())
        ) {

            // 显示邮箱地址
            Text(
                text = stringResource(R.string.send_to),
                color = Color(0xFF9FA1A6),
                fontSize = 14.sp,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(5.dp))

            OutlinedTextField(
                value = uiState.email,
                onValueChange = { },
                placeholder = {},
                readOnly = true,
                singleLine = true,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = Color.White,
                    unfocusedTextColor = Color.White,
                    focusedBorderColor = Color(0xFF1A1D2E),
                    unfocusedBorderColor = Color(0xFF1A1D2E),
                    focusedContainerColor = Color(0xFF1A1D2E),
                    unfocusedContainerColor = Color(0xFF1A1D2E),
                    cursorColor = Primary
                ),
                shape = RoundedCornerShape(50.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 验证码输入提示
            Text(
                text = stringResource(R.string.enter_your_verification_code),
                color = Color(0xFF9FA1A6),
                fontSize = 14.sp,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(10.dp))

            CaptchaTextField(
                length = 6,
                captchaType = CaptchaType.NUMBER,
                value = uiState.verificationCode,
                onValueChange = onVerificationCodeChange,
                onComplete = { code ->
                    onVerificationCodeChange(code)
                })

            Spacer(modifier = Modifier.height(10.dp))

            // 错误信息显示
            uiState.errorMessage?.let { message ->
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = message,
                    color = Color.Red,
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            Spacer(modifier = Modifier.weight(1F))

            // 登录按钮
            AnchorButton(
                text = stringResource(R.string.login_next), onClick = {
                    onClearError()
                    onLogin()
                }, enabled = uiState.verificationCode.length == 6, isLoading = uiState.isLoading
            )

            Spacer(Modifier.height(10.dp))

            // 倒计时显示
            Text(
                text = formatTime(uiState.timeRemaining),
                color = Primary,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )

            Spacer(Modifier.height(10.dp))

            // 倒计时和重新发送
            Row(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 重新发送提示
                val annotatedText = buildAnnotatedString {
                    withStyle(
                        style = SpanStyle(
                            color = Color(0xFF8d8f95), fontWeight = FontWeight.Medium
                        )
                    ) {
                        append(stringResource(R.string.resend_email_code_hint))
                    }
                    append(" ")
                    withStyle(
                        style = SpanStyle(
                            color = if (uiState.canResendCode) Primary else Color(0xFF8d8f95),
                            fontWeight = FontWeight.Medium,
                            textDecoration = TextDecoration.Underline
                        )
                    ) {
                        append(stringResource(R.string.resend_email_code))
                    }
                }

                Text(
                    text = annotatedText,
                    fontSize = 14.sp,
                    modifier = Modifier.clickable(enabled = uiState.canResendCode && !uiState.isLoading) {
                        onResendCode()
                    })
            }

            Spacer(Modifier.height(20.dp))
        }
    }
}

@Preview
@Composable
fun EmailVerificationScreenPreview() {
    AnchorTheme {
        EmailVerificationContent(
            uiState = EmailVerificationUiState(),
            onResendCode = {},
            onLogin = {})
    }
}
