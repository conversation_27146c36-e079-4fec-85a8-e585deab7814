package com.mobile.anchor.app.data.service

import com.bdc.android.library.ktnet.coroutines.Await
import com.mobile.anchor.app.data.model.GiftItemBean
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * 礼物相关API服务
 */
interface GiftApiService {

    /**
     * 获取礼物列表
     */
    @GET("/api/v1/anchor/gift/list")
    suspend fun getGiftList(@Query("user_id") userId: String = ""): Await<PageResponse<GiftItemBean>?>

    @POST("/api/v1/anchor/gift/claim")
    suspend fun giftClaim(@Body body: RequestBody): Await<Any?>

}