package com.mobile.anchor.app.ui.screens.language

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bdc.android.library.extension.jump
import com.bdc.android.library.utils.ActivityManager
import com.bdc.android.library.utils.ToastUtil
import com.mobile.anchor.app.MainActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.i18n.I18nManager
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.StateView
import com.mobile.anchor.app.ui.lce.PageState

/**
 * 语言选择页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LanguageScreen(
    onNavigateBack: () -> Unit, viewModel: LanguageViewModel = viewModel()
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()
    val viewState by viewModel.viewState.observeAsState(PageState.Default)

    LaunchedEffect(Unit) {
        viewModel.loadLanguages()
    }

    LaunchedEffect(uiState) {
        if (uiState.isChanged) {
            // 同步切换语言，确保翻译数据已加载
            I18nManager.applyNewLangSync(context, uiState.currentCode ?: "")

            ToastUtil.show(context.getString(R.string.language_changed_successfully))

            // 短暂延迟确保Toast显示
            kotlinx.coroutines.delay(300)

            try {
                ActivityManager.clearAll()
                context.jump(
                    MainActivity::class.java
                )
            } catch (e: Exception) {
                LogX.e("LanguageScreen", "跳转MainActivity失败: ${e.message}")
                // 方案2：如果跳转失败，直接重新创建当前Activity
                if (context is android.app.Activity) {
                    context.recreate()
                }
            }
        }
    }

    AnchorScaffold(topBar = {
        AnchorTopBar(
            stringResource(R.string.language), onNavigationClick = onNavigateBack
        )
    }) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.languages.isEmpty() -> {
                    StateView(
                        state = viewState,
                        loadingMessage = stringResource(R.string.loading_languages),
                        emptyTitle = stringResource(R.string.no_languages_available),
                        emptyMessage = stringResource(R.string.language_list_will_appear_here),
                        onRetry = { viewModel.loadLanguages() })
                }

                else -> {
                    // 语言列表
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(1.dp)
                    ) {
                        items(uiState.languages) { language ->
                            LanguageItem(
                                language = language,
                                isSelected = language.is_select,
                                onClick = { viewModel.selectLanguage(language.language) })
                        }
                    }
                }
            }
        }
    }
}

/**
 * 语言选项组件
 */
@Composable
private fun LanguageItem(
    language: UserBean.LanguageBean, isSelected: Boolean, onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(horizontal = 24.dp, vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = language.value,
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal
            )

            if (language.langKey.isNotEmpty()) {
                Text(
                    text = language.langKey,
                    color = Color(0xFF9E9E9E),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal
                )
            }
        }

        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "Selected",
                tint = Color(0xFF9F2AF8)
            )
        }
    }
}
