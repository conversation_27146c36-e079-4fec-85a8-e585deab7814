package com.mobile.anchor.app.navigation


/**
 * 应用页面路由定义
 * 定义所有页面的路由常量和相关信息
 */
sealed class Screen(
    val route: String,
    val title: String,
) {
    // 底部导航页面 - 现在使用Fragment实现
    object Home : Screen(
        route = "home",
        title = "首页",
    )

    object Friend : Screen(
        route = "friend",
        title = "好友",
    )

    object Message : Screen(
        route = "message",
        title = "消息",
    )

    object Mine : Screen(
        route = "mine",
        title = "我的",
    )
    
    // 二级页面 - 仍然使用Compose Screen实现
    object Settings : Screen(route = "settings", title = "设置")

    object About : Screen(route = "about", title = "关于")

    object Blacklist : Screen(route = "blacklist", title = "黑名单")

    object Language : Screen(route = "language", title = "语言")

    object WebView : Screen(route = "webview?title={title}&url={url}", title = "网页") {
        fun createRoute(title: String, url: String): String {
            val encodedTitle = java.net.URLEncoder.encode(title, "UTF-8")
            val encodedUrl = java.net.URLEncoder.encode(url, "UTF-8")
            return "webview?title=$encodedTitle&url=$encodedUrl"
        }
    }

    object Help : Screen(route = "help", title = "帮助")

    object Login : Screen(route = "login", title = "登录")

    object EmailLogin : Screen(route = "email_login", title = "邮箱登录")

    object EmailVerification : Screen(route = "email_verification/{email}", title = "邮箱验证") {
        fun createRoute(email: String) = "email_verification/$email"
    }

    object Register : Screen(route = "register", title = "注册")

    // 新增页面
    object RankList : Screen(route = "rank_list", title = "排行榜")
    object MemberList : Screen(route = "member_list", title = "成员列表")
    object Feedback : Screen(route = "feedback", title = "意见反馈")
    object LevelActivity : Screen(route = "level_activity", title = "主播规则")
    object WorkingStandard : Screen(route = "working_standard", title = "工作标准")
    object BeginnerTutorial : Screen(route = "beginner_tutorial", title = "新手教程")
    object ConnectRateQuery : Screen(route = "connect_rate_query", title = "接通率查询")
    object Notification : Screen(route = "notification", title = "通知中心")
    
    // 个人中心相关页面
    object ProfileEdit : Screen(route = "profile_edit", title = "编辑资料")
    object ProfileBuild : Screen(route = "build_profile", title = "完善资料")
    object AlbumManage : Screen(route = "album_manage", title = "相册管理")
    object Level : Screen(route = "level", title = "等级")
    object Wallet : Screen(route = "wallet", title = "钱包")
    object Withdraw : Screen(route = "withdraw", title = "提现")
    object BankBind : Screen(route = "bank_bind", title = "绑定银行卡")
    object RewardTask : Screen(route = "reward_task", title = "任务")
    object Invite : Screen(route = "invite", title = "邀请好友")
    object ChangePassword : Screen(route = "change_password", title = "修改密码")
    object FaceRecord : Screen(route = "face_record", title = "人脸录制")

    // 详情页面（带参数）
    object UserDetail : Screen(route = "user_detail/{userId}", title = "用户详情") {
        fun createRoute(userId: String) = "user_detail/$userId"
    }
}

/**
 * 底部导航栏页面列表
 */
val bottomNavScreens = listOf(
    Screen.Home,
    Screen.Friend,
    Screen.Message,
    Screen.Mine
)

/**
 * 二级页面路由列表 - 这些页面仍使用Compose导航
 */
val secondaryScreens = listOf(
    Screen.Settings,
    Screen.About,
    Screen.Help,
    Screen.Login,
    Screen.EmailLogin,
    Screen.EmailVerification,
    Screen.Register,
    Screen.UserDetail,
    Screen.RankList,
    Screen.MemberList,
    Screen.Feedback,
    Screen.LevelActivity,
    Screen.WorkingStandard,
    Screen.BeginnerTutorial,
    Screen.ConnectRateQuery,
    Screen.Notification,
    Screen.ProfileEdit,
    Screen.ProfileBuild,
    Screen.AlbumManage,
    Screen.Level,
    Screen.Wallet,
    Screen.Withdraw,
    Screen.BankBind,
    Screen.RewardTask,
    Screen.Invite,
    Screen.ChangePassword,
    Screen.FaceRecord
)

/**
 * 获取所有页面路由
 */
fun getAllScreenRoutes(): List<String> {
    return listOf(
        Screen.Home.route,
        Screen.Friend.route,
        Screen.Message.route,
        Screen.Mine.route,
        Screen.Settings.route,
        Screen.UserDetail.route,
        Screen.About.route,
        Screen.Blacklist.route,
        Screen.Language.route,
        Screen.WebView.route,
        Screen.Help.route,
        Screen.Login.route,
        Screen.EmailLogin.route,
        Screen.EmailVerification.route,
        Screen.Register.route,
        Screen.RankList.route,
        Screen.MemberList.route,
        Screen.Feedback.route,
        Screen.LevelActivity.route,
        Screen.WorkingStandard.route,
        Screen.BeginnerTutorial.route,
        Screen.ConnectRateQuery.route,
        Screen.Notification.route,
        Screen.ProfileEdit.route,
        Screen.ProfileBuild.route,
        Screen.AlbumManage.route,
        Screen.Level.route,
        Screen.Wallet.route,
        Screen.Withdraw.route,
        Screen.BankBind.route,
        Screen.RewardTask.route,
        Screen.Invite.route,
        Screen.ChangePassword.route,
        Screen.FaceRecord.route
    )
}

/**
 * 根据路由获取页面标题
 */
fun getScreenTitle(route: String?): String {
    return when (route) {
        Screen.Home.route -> Screen.Home.title
        Screen.Friend.route -> Screen.Friend.title
        Screen.Message.route -> Screen.Message.title
        Screen.Mine.route -> Screen.Mine.title
        Screen.Settings.route -> Screen.Settings.title
        Screen.About.route -> Screen.About.title
        Screen.Help.route -> Screen.Help.title
        Screen.Login.route -> Screen.Login.title
        Screen.EmailLogin.route -> Screen.EmailLogin.title
        Screen.EmailVerification.route -> Screen.EmailVerification.title
        Screen.Register.route -> Screen.Register.title
        Screen.RankList.route -> Screen.RankList.title
        Screen.MemberList.route -> Screen.MemberList.title
        Screen.Feedback.route -> Screen.Feedback.title
        Screen.LevelActivity.route -> Screen.LevelActivity.title
        Screen.WorkingStandard.route -> Screen.WorkingStandard.title
        Screen.BeginnerTutorial.route -> Screen.BeginnerTutorial.title
        Screen.ConnectRateQuery.route -> Screen.ConnectRateQuery.title
        Screen.Notification.route -> Screen.Notification.title
        Screen.ProfileEdit.route -> Screen.ProfileEdit.title
        Screen.ProfileBuild.route -> Screen.ProfileBuild.title
        Screen.AlbumManage.route -> Screen.AlbumManage.title
        Screen.Level.route -> Screen.Level.title
        Screen.Wallet.route -> Screen.Wallet.title
        Screen.Withdraw.route -> Screen.Withdraw.title
        Screen.BankBind.route -> Screen.BankBind.title
        Screen.RewardTask.route -> Screen.RewardTask.title
        Screen.Invite.route -> Screen.Invite.title
        Screen.ChangePassword.route -> Screen.ChangePassword.title
        Screen.FaceRecord.route -> Screen.FaceRecord.title
        else -> {
            when {
                route?.startsWith("user_detail/") == true -> Screen.UserDetail.title
                route?.startsWith("email_verification/") == true -> Screen.EmailVerification.title
                else -> "未知页面"
            }
        }
    }
}
