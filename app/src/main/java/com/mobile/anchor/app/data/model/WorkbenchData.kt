package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@Parcelize
@JsonClass(generateAdapter = true)
data class DashboardBean(
    val matchStatus: String = "0",
    val workDayTime: String = "0",
    val workWeekTime: String = "0",
    val avgWeekCallTime: String = "0",
    val connectDayRate: String = "0",
    val connectWeekRate: String = "0",
    val todayMatchCallCompletionRate: String = "0",
    val totalMatchCallCompletionRate: String = "0",
    val todayVideoCallCompletionRate: String = "0",
    val totalVideoCallCompletionRate: String = "0",
    val matchingSec: String = "0",
    val paidSec: String = "0",
    val anchorRule: String = "",
    val workingStandard: String = "",
    val beginnerTutorial: String = "",

    val today_data: TodayData? = null,
    val week_data: TodayData? = null,
    val total_data: TodayData? = null,
    val self_this_daily_rank: SelfRank? = null,
    val self_this_weekly_rank: SelfRank? = null,
    val task_report: TaskReport? = null,
//    val score: Double = 0.0,
    val get_anchor_level_range: AnchorLevelRange? = null,
    val working_mod: Boolean = false,
    val video_price: Int = 0,
    val tips_list: List<String>? = null,
    val anchor_cycle_times: Int = 0,//主播循环匹配次数
    val anchor_match_times_step: Int = 0,//主播循环匹配任务进度
    val anchor_match_coin: Int = 0,//主播匹配循环任务奖励

    val match_call_effect_duration: Int = 0, // 匹配有效时长
    val normal_call_effect_duration: Int = 0, // 普通通话有效时长
    val each_match_effect_coin: Int = 0, // 每次有效匹配奖励
) : Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class TodayData(
    val match_call_times: Int = 0,//匹配通话次数
    val normal_call_times: Int = 0,//正常通话次数
    val match_call_effect_rate: Double = 0.0,//匹配完播率
    val normal_call_effect_rate: Double = 0.0,//video_call有效率
    val income: Int = 0,
    val working_duration: Long = 0,//工作时长(秒)
    val connect_rate: Double = 0.0,//接通率
    val avg_call_duration: Long = 0,//平均通话时长
    val anchor_effect_match_times: Int = 0,//有效匹配次数
) : Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class SelfRank(
    val avatar: String, val anchor_id: String, val nickname: String, val index: Int
) : Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class TaskReport(
    val daily_total: Int, val daily_finish_total: Int, val daily_duration: Int,
) : Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class AnchorLevelRange(
    val start_level: Int, val title_lang_key: String, val min_price: Int, val max_price: Int
) : Parcelable

data class WorkbenchUnionBean(
    val hasChild: String = "0",
    val agentName: String = "",
    val unionId: String = "",
    val currentAmount: String = "0",
    val todayDiamond: String = "0",
    val yesterdayDiamond: String = "0",
    val weekDiamond: String = "0",
    val monthDiamond: String = "0",
    val totalDiamond: String = "0"
)

data class HomeTaskBean(
    val todayValidMatchTimes: String = "0",
    val matchBonus: String = "0",
    val currentMatchTimes: String = "0",
    val matchSettleValue: String = "0"
)

data class MarqueeBean(
    val notifications: String = ""
)

/**
 * 通知数据模型
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class NotificationBean(
    val id: String = "",
    val title: String = "",
    val content: String = "",
    val avatar: String? = null,
    val nickname: String = "",
    val createTime: String = "",
    val isRead: Boolean = false,
    val type: Int = 0 // 通知类型：0-系统通知，1-用户通知
) : Parcelable {

    fun getHeadFileName(): String? = avatar

    fun getNickName(): String = nickname.ifEmpty { "System" }
}

/**
 * 通知列表响应
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class NotificationListBean(
    val list: List<NotificationBean>? = null, val cursor: String = "", val hasMore: Boolean = false
) : Parcelable