package com.mobile.anchor.app.service

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.ServiceInfo
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.TextView
import com.bdc.android.library.utils.DisplayUtil
import com.mobile.anchor.app.MainActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.socket.WebSocketManager
import com.mobile.anchor.app.ui.call.CallVideoChatActivity
import com.mobile.anchor.app.utils.ActivityUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 悬浮窗服务
 * 负责在应用进入后台时显示悬浮窗，应用进入前台时隐藏悬浮窗
 */
class OverlayWindowService : Service() {

    private lateinit var windowManager: WindowManager
    private lateinit var overlayView: View
    private lateinit var layoutParams: WindowManager.LayoutParams
    private lateinit var iconView: ImageView
    private lateinit var messageCountView: TextView
    private lateinit var sharedPreferences: SharedPreferences

    private val serviceScope = CoroutineScope(Dispatchers.Main + Job())

    private var initialX: Int = 0
    private var initialY: Int = 0
    private var initialTouchX: Float = 0f
    private var initialTouchY: Float = 0f
    private var isMoving: Boolean = false
    private var screenWidth: Int = 0

    // 默认距离顶部100dp
    private var topMargin: Int = 100

    // 用于平滑贴边动画的Animator
    private var snapAnimator: ValueAnimator? = null

    private var hasStartedForeground = false

    companion object {

        private const val TAG = "OverlayWindowService"

        private const val PREF_NAME = "overlay_window_pref"

        private const val KEY_TOP_MARGIN = "top_margin"

        // 用于启动和停止服务的Action
        const val ACTION_START_SERVICE = "com.mobile.anchor.app.START_OVERLAY_SERVICE"
        const val ACTION_STOP_SERVICE = "com.mobile.anchor.app.STOP_OVERLAY_SERVICE"
        const val ACTION_UPDATE_MESSAGE_COUNT = "com.mobile.anchor.app.UPDATE_MESSAGE_COUNT"

        // 消息数量的Extra键
        const val EXTRA_MESSAGE_COUNT = "message_count"
    }

    @SuppressLint("ForegroundServiceType")
    override fun onCreate() {
        super.onCreate()

        // Initialize WindowManager
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager

        // Get screen width
        val displayMetrics = resources.displayMetrics
        screenWidth = displayMetrics.widthPixels

        // Initialize SharedPreferences
        sharedPreferences = getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)

        // Load saved top margin from SharedPreferences
        topMargin = sharedPreferences.getInt(KEY_TOP_MARGIN, DisplayUtil.dp2px(this, 100f))

        runCatching {
            createOverlayWindow()
        }.onFailure {
            it.printStackTrace()
            LogX.e(Log.getStackTraceString(it))
        }
    }

    @SuppressLint("ForegroundServiceType")
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        runCatching {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                startForeground(
                    OverlayNotification.NOTIFICATION_ID,
                    OverlayNotification.createNotification(),
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC
                )
            } else {
                startForeground(
                    OverlayNotification.NOTIFICATION_ID, OverlayNotification.createNotification()
                )
            }
            hasStartedForeground = true

            ActivityUtils.getCurrent()?.takeIf { it is CallVideoChatActivity }?.let {
                OverlayNotification.notify(it)
            }
        }.onFailure {
            LogX.e(TAG, "startForeground failed: ${it.message}")
            LogX.e(TAG, Log.getStackTraceString(it))
        }

        intent?.let {
            when (it.action) {
                ACTION_STOP_SERVICE -> stopSelf()
                ACTION_UPDATE_MESSAGE_COUNT -> {
                    val count = it.getIntExtra(EXTRA_MESSAGE_COUNT, 0)
                    updateMessageCount(count)
                }
            }
        }

        // 启动检查在线状态的协程 (最好只启动一次，通常放在 onCreate 或第一次 onStartCommand 进来时)
        // 为了避免重复启动，可以加一个判断
        if (serviceScope.coroutineContext[Job]?.children?.none() == true) { // 检查是否有活跃的协程
            startCheckOnlineStatus()
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    /**
     * 创建悬浮窗
     */
    @SuppressLint("ClickableViewAccessibility")
    private fun createOverlayWindow() {
        // 加载悬浮窗布局
        overlayView = LayoutInflater.from(this).inflate(R.layout.layout_overlay_window, null)

        // 获取视图引用
        iconView = overlayView.findViewById(R.id.iv_floating_icon)
        messageCountView = overlayView.findViewById(R.id.tv_message_count)

        // 设置布局参数
        layoutParams = WindowManager.LayoutParams().apply {
            // 根据Android版本设置不同的窗口类型
            type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION") WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
            }

            // 设置布局属性
            format = PixelFormat.TRANSLUCENT
            flags =
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL // 允许触摸事件穿透到下层窗口
            gravity = Gravity.TOP or Gravity.START
            width = WindowManager.LayoutParams.WRAP_CONTENT
            height = WindowManager.LayoutParams.WRAP_CONTENT

            // 设置初始位置（右侧贴边，距离顶部100dp）
            // 首次添加时，floatingView.width 可能为 0，因为还未被测量和布局
            // 所以这里先用一个预估值，然后通过 snapToEdge 修正
            val estimatedIconWidth = DisplayUtil.dp2px(this@OverlayWindowService, 48f)
            x = screenWidth - estimatedIconWidth
            y = topMargin
        }

        // 设置触摸监听器，处理拖动
        overlayView.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 如果有正在进行的贴边动画，先取消它
                    snapAnimator?.cancel()

                    // 记录初始位置
                    initialX = layoutParams.x
                    initialY = layoutParams.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    isMoving = false
                    true
                }

                MotionEvent.ACTION_MOVE -> {
                    // 计算移动距离
                    val deltaX = event.rawX - initialTouchX
                    val deltaY = event.rawY - initialTouchY

                    // 如果移动距离超过阈值，则认为是拖动而不是点击
                    if (kotlin.math.abs(deltaX) > 5 || kotlin.math.abs(deltaY) > 5) {
                        isMoving = true
                    }

                    // 更新位置
                    layoutParams.x = initialX + deltaX.toInt()
                    layoutParams.y = initialY + deltaY.toInt()

                    // 更新视图
                    if (overlayView.isAttachedToWindow) {
                        windowManager.updateViewLayout(overlayView, layoutParams)
                    }
                    true
                }

                MotionEvent.ACTION_UP -> {
                    // 如果是点击而不是拖动，则打开应用
                    if (!isMoving) {
                        openApp()
                    } else {
                        // 释放后自动贴边
                        snapToEdge()

                        // 保存当前距离顶部的距离
                        topMargin = layoutParams.y
                        sharedPreferences.edit().putInt(KEY_TOP_MARGIN, topMargin).apply()
                    }
                    true
                }

                else -> false
            }
        }

        // 添加到窗口
        windowManager.addView(overlayView, layoutParams)

        // 初始时更新图标状态
        updateIconStatus(WebSocketManager.getInstance().isConnected())

        // 首次显示后，执行一次贴边，确保位置正确
        // 延迟执行以确保 floatingView.width 已经被测量
        overlayView.post {
            snapToEdge()
        }
    }

    /**
     * 释放后自动贴边，使用动画平滑过渡
     */
    private fun snapToEdge() {
        val middle = screenWidth / 2

        // 确定目标X坐标：贴左边或贴右边
        val targetX: Int = if (layoutParams.x < middle) {
            0
        } else {
            // 确保 floatingView.width 已经有值，如果为0，则使用一个估计值
            val viewWidth =
                if (overlayView.width > 0) overlayView.width else DisplayUtil.dp2px(this, 48f)
            screenWidth - viewWidth
        }

        // 如果当前位置已经非常接近目标位置，则不进行动画
        if (kotlin.math.abs(layoutParams.x - targetX) < 5) { // 5像素的阈值
            layoutParams.x = targetX
            if (overlayView.isAttachedToWindow) {
                windowManager.updateViewLayout(overlayView, layoutParams)
            }
            return
        }

        // 取消任何正在进行的动画
        snapAnimator?.cancel()

        // 创建ValueAnimator来实现平滑过渡
        snapAnimator = ValueAnimator.ofInt(layoutParams.x, targetX).apply {
            duration = 200L // 动画持续时间，200毫秒
            interpolator = AccelerateDecelerateInterpolator() // 加速-减速插值器，使动画更自然

            addUpdateListener { animator ->
                layoutParams.x = animator.animatedValue as Int
                // 在动画的每一帧更新视图位置
                if (overlayView.isAttachedToWindow) {
                    windowManager.updateViewLayout(overlayView, layoutParams)
                }
            }
            start()
        }
    }


    /**
     * 打开应用
     */
    private fun openApp() {
        val intent = if (ActivityUtils.getCurrent() is CallVideoChatActivity) {
            Intent(this, CallVideoChatActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
        } else {
            Intent(this, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
        }
        startActivity(intent)
    }

    /**
     * 更新消息数量
     */
    private fun updateMessageCount(count: Int) {
        if (::messageCountView.isInitialized) {
            if (count > 0) {
                messageCountView.visibility = View.VISIBLE
                messageCountView.text = if (count > 99) "99+" else count.toString()
            } else {
                messageCountView.visibility = View.GONE
            }
        }
    }

    /**
     * 更新图标状态（在线/离线）
     */
    private fun updateIconStatus(isOnline: Boolean) {
        if (::iconView.isInitialized) {
            iconView.setImageResource(
                if (isOnline) R.mipmap.ic_overlay_window_active else R.mipmap.ic_overlay_window_inactive
            )
        }
    }

    /**
     * 启动检查在线状态的协程
     */
    private fun startCheckOnlineStatus() {
        serviceScope.launch {
            while (true) {
                val isOnline = WebSocketManager.getInstance().isConnected()
                updateIconStatus(isOnline)
                delay(5000) // 每5秒检查一次
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 停止前台服务
        if (hasStartedForeground) {
            stopForeground(true)  // 移除前台通知
            hasStartedForeground = false
        }

        // 移除悬浮窗
        if (::overlayView.isInitialized && overlayView.isAttachedToWindow) {
            windowManager.removeView(overlayView)
        }

        // 取消协程任务
        serviceScope.cancel() // 取消所有协程任务

        // 取消悬浮窗动画
        snapAnimator?.cancel()
        snapAnimator = null
    }

}