package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/6/20 11:24
 * @description : 用户新配置模型
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class IsolatedConfigBean(
    val update_type: Int/*0不更新 1提示更新 2强制更新*/,
    val update_url: String/*更新地址*/,
    val update_tips: String/*更新信息*/
) : Parcelable {
    val isUpdateRequired: Boolean get() = update_type == 2

    val isUpdateSuggested: Boolean get() = update_type == 1

    val hasUpdate: Boolean get() = isUpdateSuggested || isUpdateSuggested
}
