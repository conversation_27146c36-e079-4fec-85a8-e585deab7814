package com.mobile.anchor.app.utils

import java.lang.reflect.Field

/**
 * 通过反射获取 RongExtension 对象。
 * 注意：使用反射会破坏封装性，且可能在 SDK 更新后失效，请谨慎使用。
 *
 * @param myInputPanelInstance MyInputPanel 的实例。
 * @return RongExtension 对象，如果获取失败则返回 null。
 */
fun getRongExtensionFromMyInputPanel(myInputPanelInstance: Any): Any? {
    try {
        // 1. 获取 MyInputPanel 的 Class 对象
        val clazz: Class<*> = myInputPanelInstance.javaClass

        // 2. 获取名为 "mRongExtension" 的私有字段
        val field: Field = clazz.getDeclaredField("mRongExtension")

        // 3. 设置字段为可访问，即使它是私有的
        field.isAccessible = true

        // 4. 获取字段的值
        return field.get(myInputPanelInstance)

    } catch (e: NoSuchFieldException) {
        println("错误：未找到字段 mRongExtension。${e.message}")
        e.printStackTrace()
    } catch (e: IllegalAccessException) {
        println("错误：无法访问字段 mRongExtension。${e.message}")
        e.printStackTrace()
    } catch (e: Exception) {
        println("发生未知错误：${e.message}")
        e.printStackTrace()
    }
    return null
}