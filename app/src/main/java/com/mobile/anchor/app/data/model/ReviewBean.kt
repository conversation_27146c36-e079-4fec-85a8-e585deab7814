package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON>cha<PERSON>
 * @date: 2025/6/13 20:42
 * @description :
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class ReviewBean(
    val id: String,
    val anchorId: String,
    val nickname: String,
    val avatar: String,
    val gender: Int,//性别1男2女
    val showAvatar: String,
    val videoFile: String,
    val showVideoFile: String,
    val reason: String,
    val birthday: Long,
    val stat: Int,//0没有提交过资料 1已提交审核中 2 审核通过 3审核不通过
    val updatedAt: Long,
    val createdAt: Long,
    val self_description: String,
    val check_reason: String = "",
    val check_jump_url: String = "",

    val media_list: List<UserBean.AlbumBean>? = null,

    val nickname_result: Int = 0,//默认0 1未通过 2通过
    val avatar_result: Int = 0,//默认0 1未通过 2通过

) : Parcelable {

    val isMissing: Boolean get() = stat == 0
    val isReviewing: Boolean get() = stat == 1
    val isCompleted: Boolean get() = stat == 2
    val isRejected: Boolean get() = stat == 3

    val isReviewingNickname: Boolean get() = nickname_result == 0
    val isRejectedNickname: Boolean get() = nickname_result == 1
    val isCompletedNickname: Boolean get() = nickname_result == 2

    val isReviewingAvatar: Boolean get() = avatar_result == 0
    val isRejectedAvatar: Boolean get() = avatar_result == 1
    val isCompletedAvatar: Boolean get() = avatar_result == 2
}

