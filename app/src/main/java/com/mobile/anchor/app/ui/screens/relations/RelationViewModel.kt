package com.mobile.anchor.app.ui.screens.relations

import android.net.Uri
import androidx.core.net.toUri
import androidx.lifecycle.viewModelScope
import com.mobile.anchor.app.data.model.RelationAction
import com.mobile.anchor.app.data.model.RelationList
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.BodyParams
import com.mobile.anchor.app.data.service.RelationApiService
import com.mobile.anchor.app.data.service.UserApiService
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.viewmodels.BaseViewModel
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imlib.model.UserInfo
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 好友页面 ViewModel
 */
class RelationViewModel : BaseViewModel() {

    private val friendApiService: RelationApiService by NetDelegates()
    private val userApiService: UserApiService by NetDelegates()

    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing.asStateFlow()

    private val _isLoadingMore = MutableStateFlow(false)
    val isLoadingMore: StateFlow<Boolean> = _isLoadingMore.asStateFlow()

    // 好友列表数据
    private val _recommendList = MutableStateFlow<List<UserBean>>(emptyList())

    private val _followList = MutableStateFlow<List<UserBean>>(emptyList())

    private val _loverList = MutableStateFlow<List<UserBean>>(emptyList())

    private val _userDetail = MutableStateFlow<UserBean?>(null)
    val userDetail: StateFlow<UserBean?> = _userDetail.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // 分页相关
    private var recommendCursor = ""
    private var recommendHasMore = true
    private var followCursor = ""
    private var followHasMore = true

    private var loverCursor = ""
    private var loverHasMore = true

    companion object {
        private const val PAGE_SIZE = 20
        private const val TYPE_RECOMMEND = 1
        private const val TYPE_LOVER = 2
        private const val TYPE_FOLLOW = 3
    }

    /**
     * 加载好友列表
     * @param tab 标签类型：Recommend 或 Follow
     * @param isRefresh 是否为刷新操作
     */
    fun loadRelationList(tab: Int, isRefresh: Boolean = false) {
        val type = getCurrentTabType(tab)

        val (currentCursor, currentList) = when (type) {
            TYPE_RECOMMEND -> recommendCursor to _recommendList.value
            TYPE_FOLLOW -> followCursor to _followList.value
            else -> loverCursor to _loverList.value
        }

        // 如果是刷新，重置游标
        val cursor = if (isRefresh) "" else currentCursor

        if (isRefresh) {
            _isRefreshing.value = true
        } else {
            _isLoadingMore.value = true
        }

        ktHttpRequest {
            val response = when (type) {
                TYPE_RECOMMEND -> {
                    friendApiService.getRecommendList(
                        cursor = cursor, size = PAGE_SIZE, type = type
                    ).await()
                }

                TYPE_FOLLOW -> {
                    userApiService.getRelationList(
                        RelationList.FOLLOW.value, cursor = cursor
                    ).await()
                }

                else -> {
                    userApiService.getRelationList(
                        RelationList.LOVER.value, cursor = cursor
                    ).await()
                }
            }

            response?.let { data ->
                val newList = if (isRefresh) {
                    data.records ?: emptyList()
                } else {
                    currentList + (data.records ?: emptyList())
                }

                //缓存融云信息
                data.records?.forEach { userBean ->
                    val userInfo =
                        UserInfo(userBean.id, userBean.nickname, userBean.showAvatar.toUri())
                    RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
                }

                when (type) {
                    TYPE_RECOMMEND -> {
                        _recommendList.value = newList
                        recommendCursor = data.cursor
                        recommendHasMore = (data.records?.size ?: 0) >= PAGE_SIZE
                    }

                    TYPE_FOLLOW -> {
                        _followList.value = newList
                        followCursor = data.cursor
                        followHasMore = (data.records?.size ?: 0) >= PAGE_SIZE
                    }

                    else -> {
                        _loverList.value = newList
                        loverCursor = data.cursor
                        loverHasMore = (data.records?.size ?: 0) >= PAGE_SIZE
                    }
                }

            } ?: run {
                LogX.e("API响应为空")
            }

            if (isRefresh) {
                _isRefreshing.value = false
            } else {
                _isLoadingMore.value = false
            }
        }

        // 在请求完成后检查是否为空状态
        viewModelScope.launch {
            delay(500) // 等待状态更新
            val currentList = when (type) {
                TYPE_RECOMMEND -> _recommendList.value
                TYPE_FOLLOW -> _followList.value
                else -> _loverList.value
            }
            if (currentList.isEmpty() && isRefresh) {
                setupEmptyState()
            }
        }
    }

    /**
     * 刷新好友列表
     */
    fun refreshRelationList(tab: Int) {
        loadRelationList(tab, isRefresh = true)
    }

    /**
     * 加载更多好友
     */
    fun loadMoreRelations(tab: Int) {
        val type = getCurrentTabType(tab)
        val hasMore = when (type) {
            TYPE_RECOMMEND -> recommendHasMore
            TYPE_FOLLOW -> followHasMore
            else -> loverHasMore
        }

        if (hasMore && !_isLoadingMore.value) {
            loadRelationList(tab, isRefresh = false)
        }
    }

    /**
     * 检查是否可以加载更多
     */
    fun canLoadMore(tab: Int): Boolean {
        return when (getCurrentTabType(tab)) {
            TYPE_RECOMMEND -> recommendHasMore
            TYPE_FOLLOW -> followHasMore
            else -> loverHasMore
        }
    }

    /**
     * 获取当前标签的好友列表
     */
    fun getRelationList(tab: Int): StateFlow<List<UserBean>> {
        return when (getCurrentTabType(tab)) {
            TYPE_RECOMMEND -> _recommendList
            TYPE_FOLLOW -> _followList
            else -> _loverList
        }
    }

    private fun getCurrentTabType(tab: Int): Int {
        return when (tab) {
            0 -> TYPE_RECOMMEND
            1 -> TYPE_LOVER
            else -> TYPE_FOLLOW
        }
    }

    /**
     * 获取用户详情
     * @param userId 用户ID
     */
    fun getUserDetail(userId: String) {
        if (userId.isEmpty()) {
            LogX.e("用户ID为空")
            return
        }

        _isLoading.value = true

        ktHttpRequest {
            val response = userApiService.getUserDetail(userId).await()

            response?.let { userBean ->
                _userDetail.value = userBean
                val userInfo =
                    UserInfo(userBean.id, userBean.nickname, Uri.parse(userBean.showAvatar))
                RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
                LogX.d("获取用户详情成功: ${userBean.nickname}")
            } ?: run {
                LogX.e("获取用户详情失败: API响应为空")
            }

            _isLoading.value = false
        }
    }

    fun relateUser(action: RelationAction, userId: String) {
        ktHttpRequest {
            val response = userApiService.relationOperation(
                action.value, BodyParams.relationOpParamsBody(userId)
            ).await()

            response?.let {
                // 更新本地用户详情状态
                _userDetail.value?.let { currentUser ->
                    val newShowRelation = when (action) {
                        RelationAction.FOLLOW -> "1" // 已关注
                        RelationAction.UNFOLLOW -> "0" // 未关注
                        else -> currentUser.showRelation
                    }
                    _userDetail.value = currentUser.copy(showRelation = newShowRelation)
                }
            } ?: run {
                LogX.e("关注用户失败: API响应为空")
            }
        }
    }

    /**
     * 拉黑/取消拉黑用户
     * @param userId 用户ID
     * @param isBlock true为拉黑，false为取消拉黑
     */
    fun blockUser(userId: String, isBlock: Boolean) {
        val action = if (isBlock) RelationAction.BLOCK else RelationAction.UNBLOCK
        val actionText = if (isBlock) "Block" else "Unblock"
        val successMessage =
            if (isBlock) "User blocked successfully" else "User unblocked successfully"
        val errorMessage = if (isBlock) "Failed to block user" else "Failed to unblock user"

        ktHttpRequest {
            val response = userApiService.relationOperation(
                action.value, BodyParams.relationOpParamsBody(userId)
            ).await()

            response?.let {
                LogX.d("${actionText}用户成功")
                // 更新本地用户详情状态
                _userDetail.value?.let { currentUser ->
                    val newShowRelation = if (isBlock) "4" else "0" // 4表示已拉黑，0表示无关系
                    _userDetail.value = currentUser.copy(showRelation = newShowRelation)
                }
                // 显示成功提示
                com.bdc.android.library.utils.ToastUtil.show(successMessage)
            } ?: run {
                LogX.e("${actionText}用户失败: API响应为空")
                // 显示失败提示
                com.bdc.android.library.utils.ToastUtil.show(errorMessage)
            }
        }
    }


    /**
     * 清空用户详情数据
     */
    fun clearUserDetail() {
        _userDetail.value = null
    }
}
