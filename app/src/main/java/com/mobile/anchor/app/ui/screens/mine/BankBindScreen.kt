package com.mobile.anchor.app.ui.screens.mine

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.Icon
import androidx.compose.material3.MenuAnchorType
import androidx.compose.material3.MenuDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bdc.android.library.extension.finish
import com.bdc.android.library.utils.ToastUtil
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.BankBean
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.components.AnchorButton
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTextField
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.lce.PageState
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Surface
import com.mobile.anchor.app.ui.viewmodels.WalletViewModel

/**
 * 绑定银行卡页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BankBindScreen(
) {
    val viewModel: WalletViewModel = viewModel()
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()

    // 表单状态
    var selectedBankName by remember { mutableStateOf("") }
    var accountId by remember { mutableStateOf(-1) }
    var accountNumber by remember { mutableStateOf("") }
    var fullName by remember { mutableStateOf("") }
    var isSubmitting by remember { mutableStateOf(false) }

    // 验证码相关状态
    val verificationCode by viewModel.verificationCode.collectAsState()
    val isCodeSending by viewModel.isCodeSending.collectAsState()
    val timeRemaining by viewModel.timeRemaining.collectAsState()
    val canResendCode by viewModel.canResendCode.collectAsState()

    val currentBankInfo by viewModel.bankInfo.collectAsState()
    LaunchedEffect(currentBankInfo) {
        currentBankInfo?.let {
            selectedBankName = it.name
            accountId = it.bank_id
            accountNumber = it.card_num
            fullName = it.real_name
        }
    }

    // 下拉框展开状态
    var bankNameExpanded by remember { mutableStateOf(false) }

    val bankList = viewModel.bankList.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.getBankInfo()
        viewModel.getBankList()
    }

    LaunchedEffect(uiState) {
        uiState.bindBankState.let { viewState ->
            if (viewState is PageState.Error) {
                LogX.e("绑定银行卡失败", viewState.throwable)
            } else if (viewState is PageState.Success) {
                ToastUtil.show(context.getString(R.string.successfully_bound_bank_card))
                context.finish()
            }
        }
    }

    AnchorScaffold(
        topBar = {
            AnchorTopBar(
                title = context.getString(R.string.bank_bind), onNavigationClick = {
                    context.finish()
                })
        }) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {

            Spacer(modifier = Modifier.height(20.dp))

            // BankName 下拉选择
            BankBindDropdownField(
                label = stringResource(R.string.bankname),
                value = selectedBankName,
                placeholder = stringResource(R.string.select_bank),
                options = bankList.value,
                expanded = bankNameExpanded,
                onExpandedChange = { bankNameExpanded = it },
                onOptionSelected = {
                    selectedBankName = it.name
                    accountId = it.id
                    bankNameExpanded = false
                })

            Spacer(modifier = Modifier.height(20.dp))

            // AccountNo 输入框
            BankBindTextField(
                label = stringResource(R.string.bank_no),
                value = accountNumber,
                placeholder = stringResource(R.string.enter_account_number),
                keyboardType = KeyboardType.Number,
                onValueChange = { newValue ->
                    // 限制为20位以内的纯数字
                    val filteredValue = newValue.filter { it.isDigit() }.take(20)
                    accountNumber = filteredValue
                })

            Spacer(modifier = Modifier.height(20.dp))

            // FullName 输入框
            BankBindTextField(
                label = stringResource(R.string.fullname),
                value = fullName,
                placeholder = stringResource(R.string.enter_full_name),
                keyboardType = KeyboardType.Text,
                onValueChange = { fullName = it })

            Spacer(modifier = Modifier.height(20.dp))

            // 验证码部分
            VerificationCodeSection(
                verificationCode = verificationCode,
                isCodeSending = isCodeSending,
                timeRemaining = timeRemaining,
                canResendCode = canResendCode,
                onVerificationCodeChange = { newValue ->
                    // 限制为6位纯数字
                    val filteredValue = newValue.filter { it.isDigit() }.take(6)
                    viewModel.updateVerificationCode(filteredValue)
                },
                onGetCode = viewModel::getVerificationCode
            )

            Spacer(modifier = Modifier.height(32.dp))

            // 提示文本
            Text(
                text = stringResource(R.string.bank_bind_warnning),
                color = Color.White.copy(alpha = 0.7f),
                fontSize = 14.sp,
                lineHeight = 20.sp,
                textAlign = TextAlign.Start,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(32.dp))

            AnchorButton(
                text = stringResource(R.string.update), onClick = {
                    isSubmitting = true
                    viewModel.bindBank(accountId, accountNumber, fullName, verificationCode)
                    isSubmitting = false
                }, enabled = isFormValid(
                    selectedBankName, accountNumber, fullName, verificationCode
                ) && !isSubmitting, isLoading = isSubmitting
            )
            Spacer(modifier = Modifier.height(32.dp))
        }
    }
}

/**
 * 银行绑定下拉选择框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BankBindDropdownField(
    label: String,
    value: String,
    placeholder: String,
    options: List<BankBean>,
    expanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    onOptionSelected: (BankBean) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = label,
            color = Color.White,
            fontSize = 16.sp,
            fontWeight = FontWeight.Normal,
            modifier = Modifier.padding(bottom = 12.dp)
        )

        ExposedDropdownMenuBox(
            expanded = expanded, onExpandedChange = onExpandedChange
        ) {
            AnchorTextField(
                value = value,
                placeholder = placeholder,
                readOnly = true,
                trailingIcon = {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "Dropdown",
                        tint = Color.White
                    )
                },
                modifier = Modifier.menuAnchor(MenuAnchorType.PrimaryNotEditable),
                onValueChange = { })

            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { onExpandedChange(false) },
                modifier = Modifier.background(Surface)
            ) {
                options.forEach { option ->
                    DropdownMenuItem(
                        text = {
                        Text(
                            text = option.name, color = Color.White
                        )
                    }, onClick = { onOptionSelected(option) }, colors = MenuDefaults.itemColors(
                        textColor = Color.White
                    )
                    )
                }
            }
        }
    }
}

/**
 * 银行绑定文本输入框
 */
@Composable
private fun BankBindTextField(
    label: String,
    value: String,
    placeholder: String,
    keyboardType: KeyboardType,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = label,
            color = Color.White,
            fontSize = 16.sp,
            fontWeight = FontWeight.Normal,
            modifier = Modifier.padding(bottom = 12.dp)
        )

        AnchorTextField(
            value = value,
            placeholder = placeholder,
            keyboardType = keyboardType,
            onValueChange = onValueChange
        )
    }
}

/**
 * 验证码部分组件
 */
@Composable
private fun VerificationCodeSection(
    verificationCode: String,
    isCodeSending: Boolean,
    timeRemaining: Int,
    canResendCode: Boolean,
    onVerificationCodeChange: (String) -> Unit,
    onGetCode: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = stringResource(R.string.verification_code),
            color = Color.White,
            fontSize = 16.sp,
            fontWeight = FontWeight.Normal,
            modifier = Modifier.padding(bottom = 12.dp)
        )

        // 验证码输入框，右边带获取验证码按钮
        AnchorTextField(
            value = verificationCode,
            placeholder = stringResource(R.string.enter_6_digit_code),
            keyboardType = KeyboardType.Number,
            onValueChange = onVerificationCodeChange,
            trailingIcon = {
                // 获取验证码按钮
                TextButton(
                    onClick = onGetCode,
                    modifier = Modifier
                        .width(100.dp)
                        .height(40.dp)
                        .padding(end = 8.dp)
                ) {
                    Text(
                        text = when {
                            isCodeSending -> stringResource(R.string.sending)
                            !canResendCode -> "${timeRemaining}s"
                            else -> stringResource(R.string.get_code)
                        },
                        textAlign = TextAlign.Center,
                        color = Color.White,
                        fontSize = 15.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            })
    }
}

/**
 * 验证表单是否有效
 */
private fun isFormValid(
    bankName: String, accountNumber: String, fullName: String, verificationCode: String
): Boolean {
    return bankName.isNotEmpty() && accountNumber.isNotEmpty() && fullName.isNotEmpty() && verificationCode.length == 6
}

@Preview
@Composable
fun BankBindScreenPreview() {
    AnchorTheme {
        BankBindScreen()
    }
}
