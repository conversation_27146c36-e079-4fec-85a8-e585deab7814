package com.mobile.anchor.app.ui.screens.blacklist

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.extension.buildImageUrl
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.ConfirmDialog
import com.mobile.anchor.app.ui.components.StateView
import com.mobile.anchor.app.ui.lce.PageState

/**
 * 黑名单页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BlacklistScreen(
    onNavigateBack: () -> Unit, viewModel: BlacklistViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val listState = rememberLazyListState()

    // 跟踪是否已完成初始加载，避免初始加载后立即触发加载更多
    var hasInitialLoaded by remember { mutableStateOf(false) }

    // 监听滚动到底部，触发加载更多
    val shouldLoadMore by remember {
        derivedStateOf {
            val layoutInfo = listState.layoutInfo
            val totalItemsNumber = layoutInfo.totalItemsCount
            val lastVisibleItemIndex = (layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0) + 1

            // 只有在已完成初始加载、有更多数据、列表不为空、且确实滚动到底部时才加载更多
            hasInitialLoaded && uiState.hasMore && uiState.blacklist.isNotEmpty() && totalItemsNumber > 0 && lastVisibleItemIndex > (totalItemsNumber - 3)
        }
    }

    LaunchedEffect(shouldLoadMore) {
        if (shouldLoadMore) {
            viewModel.loadMore()
        }
    }

    LaunchedEffect(Unit) {
        viewModel.loadBlacklist(isRefresh = true)
    }

    // 监听数据加载完成，设置初始加载标志
    LaunchedEffect(uiState.pageState) {
        if (uiState.pageState is PageState.Success || uiState.pageState is PageState.Empty) {
            hasInitialLoaded = true
        }
    }

    AnchorScaffold(
        topBar = {
            AnchorTopBar(
                stringResource(R.string.blacklist), onNavigationClick = onNavigateBack
            )
        }) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 使用 StateView 处理状态
            when (uiState.pageState) {
                is PageState.Loading, is PageState.Empty, is PageState.Error -> {
                    StateView(
                        state = uiState.pageState,
                        emptyTitle = stringResource(R.string.no_blocked_users),
                        emptyMessage = stringResource(R.string.users_you_block_will_appear_here),
                        onRetry = { viewModel.refresh() })
                }

                else -> {
                    // 显示列表内容
                    PullToRefreshBox(
                        isRefreshing = uiState.pageState is PageState.Loading,
                        onRefresh = { viewModel.refresh() },
                        modifier = Modifier.fillMaxSize()
                    ) {
                        LazyColumn(
                            state = listState,
                            modifier = Modifier.fillMaxSize(),
                            contentPadding = PaddingValues(16.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(uiState.blacklist) { user ->
                                BlacklistUserItem(
                                    user = user,
                                    onRemove = { viewModel.removeFromBlacklist(user.id) })
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 黑名单用户项
 */
@Composable
private fun BlacklistUserItem(
    user: UserBean, onRemove: () -> Unit
) {
    var showRemoveDialog by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier.fillMaxWidth(), colors = CardDefaults.cardColors(
            containerColor = Color(0xFF363948)
        ), shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 头像
            AsyncImage(
                model = user.avatar?.buildImageUrl(),
                contentDescription = "Avatar",
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape),
                contentScale = ContentScale.Crop
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 用户信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = user.nickname,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )

                Text(
                    text = stringResource(R.string.blocked),
                    color = Color(0xFF9E9E9E),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal
                )
            }

            // 移除按钮
            IconButton(
                onClick = { showRemoveDialog = true }) {
                Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = stringResource(R.string.remove),
                    tint = Color(0xFF9E9E9E)
                )
            }
        }
    }

    // 移除确认弹框
    ConfirmDialog(
        visible = showRemoveDialog,
        title = stringResource(R.string.remove_blacklist),
        content = stringResource(R.string.confirm_remove_blacklist_content, user.nickname),
        confirmText = stringResource(R.string.remove),
        cancelText = stringResource(R.string.cancel),
        onDismiss = { showRemoveDialog = false },
        onConfirm = {
            showRemoveDialog = false
            onRemove()
        },
        onCancel = { showRemoveDialog = false })
}

@Preview
@Composable
fun BlacklistScreenPreview() {
    AnchorScaffold(
        topBar = { AnchorTopBar("Blacklist") }) {
        BlacklistUserItem(UserBean(nickname = "Nickname"), {})
    }
}
