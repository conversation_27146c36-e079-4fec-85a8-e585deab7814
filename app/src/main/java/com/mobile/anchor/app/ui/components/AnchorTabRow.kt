package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabPosition
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.mobile.anchor.app.ui.theme.AnchorTheme

/**
 * 自定义标签栏组件
 * 带有渐变指示器和可滚动功能的标签栏
 *
 * @param tabs 标签列表
 * @param selectedTabIndex 当前选中的标签索引
 * @param onTabSelected 标签选择回调
 * @param modifier 修饰符
 * @param backgroundColor 背景颜色
 * @param selectedTextColor 选中标签的文字颜色
 * @param unselectedTextColor 未选中标签的文字颜色
 * @param indicatorGradient 指示器渐变色
 */
@Composable
fun AnchorTabRow(
    tabs: List<String>,
    modifier: Modifier = Modifier,
    badges: List<(@Composable BoxScope.() -> Unit)?> = emptyList(),
    badgeModifier: BoxScope.() -> Modifier = { Modifier.align(Alignment.TopEnd) },
    backgroundColor: Color = Color(0xFF1A1A2E),
    selectedTextSize: TextUnit = 16.sp,
    unselectedTextSize: TextUnit = 14.sp,
    selectedTextColor: Color = Color.White,
    unselectedTextColor: Color = Color(0XFFAFB0B5),
    indicatorGradient: Brush = Brush.horizontalGradient(
        colors = listOf(Color(0xffEC12E2), Color(0xff8531ff))
    ),
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit,
) {
    // 用于存储每个 Tab 的文本宽度（像素）
    val tabTextWidthsPx = remember { mutableStateMapOf<Int, IntSize>() }
    // 用于存储每个 Tab 的绝对 X 偏移量（屏幕像素）
    val tabOffsetsPx = remember { mutableStateMapOf<Int, Int>() }

    val density = LocalDensity.current

    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .padding(horizontal = 0.dp)
    ) {
        ScrollableTabRow(
            selectedTabIndex = selectedTabIndex,
            containerColor = Color.Transparent,
            contentColor = selectedTextColor,
            edgePadding = 0.dp,
            indicator = { tabPositions: List<TabPosition> ->
                // 确保有选中的 Tab 且数据已测量
                if (selectedTabIndex < tabPositions.size) {
                    val currentTabPosition = tabPositions[selectedTabIndex]
                    val textWidthPx = tabTextWidthsPx[selectedTabIndex]?.width ?: 0
                    val tabOffsetX = tabOffsetsPx[selectedTabIndex] ?: 0

                    // 将像素宽度转换为 Dp
                    val indicatorWidthDp = with(density) { textWidthPx.toDp() }
                    val indicatorHeight = 8.dp
                    val indicatorCornerRadius = 5.dp

                    // 计算指示器相对于 Tab 容器左边缘的偏移量
                    val indicatorHorizontalOffset =
                        (currentTabPosition.width - indicatorWidthDp) / 2

                    Box(
                        modifier = Modifier
                            .tabIndicatorOffset(currentTabPosition)
                            .fillMaxWidth()
                            .wrapContentSize(Alignment.BottomStart)
                            .offset(
                                x = indicatorHorizontalOffset, y = (-23).dp
                            )
                            .width(indicatorWidthDp)
                            .height(indicatorHeight)
                            .zIndex(-1F)
                            .clip(RoundedCornerShape(indicatorCornerRadius))
                            .background(indicatorGradient)
                    )
                }
            },
            divider = { /* 不需要分割线 */ },
            modifier = Modifier
                .wrapContentWidth(Alignment.Start)
                .padding(horizontal = 0.dp)
        ) {
            tabs.forEachIndexed { index, title ->
                val isSelected = selectedTabIndex == index
                Tab(
                    selected = isSelected,
                    onClick = { onTabSelected(index) },
                    text = {
                        Box {
                            Text(
                                text = title,
                                color = if (isSelected) selectedTextColor else unselectedTextColor,
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontSize = if (isSelected) selectedTextSize else unselectedTextSize
                                ),
                                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                                modifier = Modifier.onGloballyPositioned { coordinates ->
                                    tabTextWidthsPx[index] = coordinates.size
                                })

                            badges.getOrNull(index)?.let { badge ->
                                Box(modifier = badgeModifier()) {
                                    badge()
                                }
                            }
                        }
                    },
                    modifier = Modifier
                        .padding(vertical = 8.dp)
                        .onGloballyPositioned { coordinates ->
                            tabOffsetsPx[index] = coordinates.boundsInWindow().left.toInt()
                        })
            }
        }
    }
}

@Preview
@Composable
fun AnchorTabRowPreview() {
    AnchorTheme {
        AnchorTabRow(
            tabs = listOf("Recommend", "Follow", "Nearby"),
            selectedTabIndex = 1,
            onTabSelected = {})
    }
}
