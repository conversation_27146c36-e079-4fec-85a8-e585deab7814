package com.mobile.anchor.app.ui.viewmodels

import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.setEvent
import com.mobile.anchor.app.data.model.ReviewBean
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.CommonApiService
import com.mobile.anchor.app.data.service.UserApiService
import com.mobile.anchor.app.data.service.VideoApiService
import com.mobile.anchor.app.manager.DataStoreManager
import kotlinx.coroutines.flow.asSharedFlow

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/13 20:41
 * @description :
 */
class MainViewModel : BaseViewModel() {

    private val userApiService: UserApiService by NetDelegates()
    private val commonApiService: CommonApiService by NetDelegates()
    private val videoApiService: VideoApiService by NetDelegates()

    private val _pageEvents = SharedFlowEvents<ReviewEvent>()
    val pageEvents = _pageEvents.asSharedFlow()

    fun getIsolatedConfig() {
        ktHttpRequest {
            val response = commonApiService.getIsolatedConfig().await()
            response?.let {
                DataStoreManager.saveIsolatedConfig(it)
            }
        }
    }

    fun getVideoConfigDetail() {
        ktHttpRequest {
            val response = videoApiService.videoConfigDetail().await()
            response?.records?.let {
                DataStoreManager.saveVideoConfig(it)
            }
        }
    }

    fun checkUserReviewStatus() {
        ktHttpRequest {
            val response = userApiService.getUser().await()
            response?.let {
                DataStoreManager.saveLoginData(it)
            }
            val response2 = userApiService.getReviewInfo().await()
            response2?.let {
                _pageEvents.setEvent(ReviewEvent.FetchReviewSuccess(response.anchor, it))
            }
        }
    }
}

sealed class ReviewEvent {
    data class FetchReviewSuccess(val userBean: UserBean?, val reviewBean: ReviewBean?) :
        ReviewEvent()
}