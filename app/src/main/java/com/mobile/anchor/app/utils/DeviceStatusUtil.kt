package com.mobile.anchor.app.utils

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Debug
import android.telephony.PhoneStateListener
import android.telephony.SignalStrength
import android.telephony.TelephonyManager

object DeviceStatusUtil {
    private var mobileSignalStrength = 0
    // 获取电池温度（单位：°C）
    fun getBatteryTemperature(context: Context): Float {
        val intentFilter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        val batteryStatus = context.registerReceiver(null, intentFilter)
        val temperature = batteryStatus?.getIntExtra("temperature", -1) ?: -1
        return if (temperature != -1) {
            temperature / 10.0f // 原始单位是 0.1°C
        } else {
            -1f
        }
    }

    // 获取当前 App 使用内存（单位：MB）
    fun getUsedMemory(context: Context): Long {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = Debug.MemoryInfo()
        Debug.getMemoryInfo(memoryInfo)
        val totalPss = memoryInfo.totalPss // 单位是 KB
        return totalPss / 1024L
    }

    // 获取设备总内存（单位：MB）
    fun getTotalMemory(context: Context): Long {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        return memoryInfo.totalMem / (1024L * 1024L)
    }

    // 可选：获取当前可用内存（单位：MB）
    fun getAvailableMemory(context: Context): Long {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        return memoryInfo.availMem / (1024L * 1024L)
    }

    /**
     * 获取当前网络类型：WIFI、2G、3G、4G、5G、UNKNOWN、NONE
     */
    fun getNetworkType(context: Context): String {
        val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
        val network = cm?.activeNetwork ?: return "NONE"
        val caps = cm.getNetworkCapabilities(network) ?: return "NONE"

        return when {
            caps.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WIFI"
            caps.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                val tm = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
                val networkType =
                    tm.dataNetworkType
                when (networkType) {
                    TelephonyManager.NETWORK_TYPE_GPRS,
                    TelephonyManager.NETWORK_TYPE_EDGE,
                    TelephonyManager.NETWORK_TYPE_CDMA,
                    TelephonyManager.NETWORK_TYPE_1xRTT,
                    TelephonyManager.NETWORK_TYPE_IDEN -> "2G"

                    TelephonyManager.NETWORK_TYPE_UMTS,
                    TelephonyManager.NETWORK_TYPE_EVDO_0,
                    TelephonyManager.NETWORK_TYPE_EVDO_A,
                    TelephonyManager.NETWORK_TYPE_HSDPA,
                    TelephonyManager.NETWORK_TYPE_HSUPA,
                    TelephonyManager.NETWORK_TYPE_HSPA,
                    TelephonyManager.NETWORK_TYPE_EVDO_B,
                    TelephonyManager.NETWORK_TYPE_EHRPD,
                    TelephonyManager.NETWORK_TYPE_HSPAP -> "3G"

                    TelephonyManager.NETWORK_TYPE_LTE -> "4G"

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        TelephonyManager.NETWORK_TYPE_NR
                    } else {
                        -1
                    } -> "5G"

                    else -> "UNKNOWN"
                }
            }
            else -> "UNKNOWN"
        }
    }

    /**
     * 获取WiFi信号强度（dBm），如果不是WiFi返回null
     * -30 到 -100
     * -30 表示非常强
     * -60 中等
     * -80 差
     * -100 基本无信号
     */
    fun getWifiSignalStrength(context: Context): Int? {
        val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = cm.activeNetwork ?: return null
        val caps = cm.getNetworkCapabilities(network) ?: return null
        if (!caps.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) return null

        val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
        val info = wifiManager.connectionInfo
        return info.rssi // 信号强度，单位 dBm，通常范围 -100 ~ 0，值越大信号越强
    }

    /**
     * 获取移动网络信号强度，如果不是移动网络返回null
     * 需要注册监听更新信号强度（示例中仅返回缓存值）
     * 0	无信号或极差	无法正常通信或连接
     * 1	差	网络非常慢，经常断线
     * 2	一般	网络可用但不稳定或较慢
     * 3	良好	网络稳定，速度中等
     * 4	优秀	网络连接稳定，速度较快
     */
    fun getMobileSignalStrength(): Int {
        return mobileSignalStrength
    }

    /**
     * 初始化信号监听，放在app启动时调用（需权限 android.permission.READ_PHONE_STATE）
     */
    fun registerSignalListener(context: Context) {
        val tm = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        tm.listen(object : PhoneStateListener() {
            override fun onSignalStrengthsChanged(signalStrength: SignalStrength?) {
                super.onSignalStrengthsChanged(signalStrength)
                signalStrength?.let {
                    // 4G/5G信号等级(0-31)，或者你可以根据具体网络类型自行解析
                    mobileSignalStrength = it.level
                }
            }
        }, PhoneStateListener.LISTEN_SIGNAL_STRENGTHS)
    }

    /**
     * 获取CPU核心数
     */
    fun getCpuCoreCount(): Int = Runtime.getRuntime().availableProcessors()
}

