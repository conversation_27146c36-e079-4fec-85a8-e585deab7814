package com.mobile.anchor.app.ui.activities

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.mobile.anchor.app.navigation.Screen
import com.mobile.anchor.app.ui.screens.mine.WalletScreen
import com.mobile.anchor.app.ui.screens.mine.WithdrawScreen

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/13 20:13
 * @description :
 */
class WalletActivity : ComposeWrapperActivity() {

    @Composable
    override fun BuildContent() = Content()

    @Composable
    private fun Content() {
        val navController = rememberNavController()

        NavHost(
            navController = navController, startDestination = Screen.Wallet.route
        ) {
            composable(Screen.Wallet.route) {
                WalletScreen(navController)
            }

            composable(Screen.Withdraw.route) {
                WithdrawScreen(
                    navController = navController
                )
            }
        }
    }
}