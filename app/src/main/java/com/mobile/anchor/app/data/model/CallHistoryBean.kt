package com.mobile.anchor.app.data.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class CallHistoryBean(
    @<PERSON>son(name = "list")
    val list: MutableList<CallHistoryItemBean>?,
    @<PERSON><PERSON>(name = "cursor")
    val cursor: String = "",
)

@JsonClass(generateAdapter = true)
data class CallHistoryItemBean(
    @Json(name = "callId")
    val callId: String = "",
    @<PERSON><PERSON>(name = "userId")
    val userId: Int = 0,
    @<PERSON><PERSON>(name = "anchor_id")
    val anchorId: String = "",
    @<PERSON><PERSON>(name = "userNickName")
    val userNickName: String = "",
    @<PERSON><PERSON>(name = "userAvatar")
    val userAvatar: String = "",
    @<PERSON><PERSON>(name = "connectAt")
    val connectAt: String = "",
    @<PERSON><PERSON>(name = "callDurationSecond")
    val callDurationSecond: String = "",
    @<PERSON><PERSON>(name = "call_stat")
    val callStat: Int = 0, //1未接通 2已接通
    @<PERSON><PERSON>(name = "coin_total")
    val coinTotal: Int = 0,
    @<PERSON><PERSON>(name = "effect")
    val effect: <PERSON>olean = false
)