package com.mobile.anchor.app.ui.conversation

import androidx.fragment.app.Fragment
import com.bdc.android.library.base.fragment.BaseCoreFragment
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.FragmentMessageBinding
import com.mobile.anchor.app.extension.attach
import com.mobile.anchor.app.extension.checkSelected
import com.mobile.anchor.app.ui.viewmodels.UserViewModel

class MessageFragment : BaseCoreFragment<FragmentMessageBinding, UserViewModel>() {

    override fun getLayoutId(): Int = R.layout.fragment_message
    override fun initView() {
        super.initView()
        val titles = arrayOf(
            getString(R.string.message_tabs_messages),
            getString(R.string.message_tabs_call)
        )
        mBinding.tab.setTitles(titles)
        initViewPager()
        mBinding.tab.setViewPager(mBinding.viewPager)
    }

    private fun initViewPager() {
        val fragments = mutableListOf<Fragment>()
        fragments.add(MyConversionListFragment())
        fragments.add(CallHistoryFragment.newInstance())
//        fragments.add(FollowOrFansFragment.newInstance())
        mBinding.viewPager.attach(childFragmentManager, lifecycle, fragments) { position ->
            mBinding.tab.setCurrentTab(position)
        }
        mBinding.viewPager.checkSelected(0)
    }

    override fun bindListener() {
//        mBinding.flVideoHistory.click {
//            jump(VideoHistoryActivity::class.java)
//        }
    }

}