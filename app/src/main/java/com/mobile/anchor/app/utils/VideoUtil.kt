package com.mobile.anchor.app.utils

import android.content.Context
import android.media.MediaPlayer
import android.view.Window
import android.view.WindowManager
import com.mobile.anchor.app.BuildConfig
import java.io.IOException

object VideoUtil {
    //播放铃声
    fun startRing(context: Context): MediaPlayer? {
        try {
            val fd = context.assets.openFd("startvideobg.mp3")
            val mediaPlayer = MediaPlayer().apply {
                setDataSource(fd.fileDescriptor, fd.startOffset, fd.getLength())
                isLooping = true
                prepare()
                start()
            }
            return mediaPlayer
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
    }

    //禁止截屏  debug环境放开
    fun screenSecure(window: Window) {
        if (!BuildConfig.DEBUG) {
            window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
        }
    }

}