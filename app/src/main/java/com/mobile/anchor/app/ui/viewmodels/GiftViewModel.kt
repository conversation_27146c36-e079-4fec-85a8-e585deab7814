package com.mobile.anchor.app.ui.viewmodels

import androidx.lifecycle.viewModelScope
import com.bdc.android.library.ktnet.tryAwait
import com.bdc.android.library.mvi.SharedFlowEvents
import com.bdc.android.library.mvi.setEvent
import com.bdc.android.library.mvi.setState
import com.mobile.anchor.app.data.model.GiftItemBean
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.network.ktnet.error.msg
import com.mobile.anchor.app.data.service.BodyParams
import com.mobile.anchor.app.data.service.GiftApiService
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2025/6/17 17:13
 * @description
 */
class GiftViewModel : BaseViewModel() {

    private val giftApiService: GiftApiService by NetDelegates()
    private val _pageEvents = SharedFlowEvents<GiftRequestEvent>()
    val pageEvents = _pageEvents.asSharedFlow()

    fun fetchGiftList(userId: String) {
        viewModelScope.launch {
            val gifts = giftApiService.getGiftList(userId).tryAwait {
                _pageEvents.setEvent(GiftRequestEvent.FetchGiftListFailed(it.msg))
            }
            gifts?.records?.let {
                _pageEvents.setEvent(GiftRequestEvent.FetchGiftListSuc(it))
            }
        }
    }

    fun giftGive(
        userId: String,
        giftBean: GiftItemBean,
    ) {
        viewModelScope.launch {
            val request = giftApiService.giftClaim(
                BodyParams.giftClaimBody(
                    giftBean.id.toInt(),
                    userId.toInt()
                )
            ).tryAwait {
                _pageEvents.setEvent(GiftRequestEvent.GiftSendFailed(it.msg))
            }
            request?.let {
                _pageEvents.setEvent(GiftRequestEvent.GiftSendSuc(giftBean))
            }
        }
    }

}

sealed class GiftRequestEvent {
    data object Default : GiftRequestEvent()
    data class FetchGiftListFailed(val msg: String) : GiftRequestEvent()
    data class FetchGiftListSuc(val gifts: MutableList<GiftItemBean>) : GiftRequestEvent()
    data class GiftSendFailed(val msg: String) : GiftRequestEvent()
    data class GiftSendSuc(val giftBean: GiftItemBean) : GiftRequestEvent()
}