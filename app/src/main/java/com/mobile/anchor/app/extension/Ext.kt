package com.mobile.anchor.app.extension

import android.content.Context
import android.content.ContextWrapper
import android.content.res.Resources
import android.os.Bundle
import android.util.TypedValue
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelStoreOwner
import androidx.viewpager2.widget.ViewPager2
import com.bdc.android.library.extension.jump
import com.mobile.anchor.app.MainActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.notification.GlobalNotificationManager
import com.mobile.anchor.app.ui.activities.AlbumManageActivity
import com.mobile.anchor.app.ui.activities.BankBindActivity
import com.mobile.anchor.app.ui.activities.FaceRecordActivity
import com.mobile.anchor.app.ui.activities.ProfileBuildActivity
import com.mobile.anchor.app.ui.activities.ProfileEditActivity
import com.mobile.anchor.app.ui.activities.WalletActivity
import com.mobile.anchor.app.ui.activities.WebActivity
import com.mobile.anchor.app.utils.Constants
import java.net.URL
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


fun String.isEmail(): Boolean {
    val s = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}\$"
    return this.matches(s.toRegex())
}

fun Long.formatDate(): String {
    val date = Date(this)
    val format = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    return format.format(date)
}

fun Long.formatTime(): String {
    if (this <= 0L) return "00:00:00"
    val date = Date(this)
    val format = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    return format.format(date)
}

fun Long.formatDateTime(): String {
    val date = Date(this)
    val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    return format.format(date)
}

fun String.toTimestamp(): Long {
    return try {
        val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        format.parse(this)?.time ?: 0L
    } catch (e: Exception) {
        0L
    }
}

fun String.buildImageUrl(
    thumbnail: Boolean = true, highQuality: Boolean = false, list: Boolean = false
): String {
    if (this.endsWith(".mp4") || this.endsWith(".svga")) {
        return this
    } else {
        val url =
            if (this.startsWith("http") || this.startsWith("https")) return this else "${Constants.ImageSpec.getDomain()}$this"
        return "${url}?=${Constants.ImageSpec.get(thumbnail, highQuality, list)}"
    }
}

fun String.extractS3ObjectKey(): String {
    return URL(this).path.removePrefix("/") // 去掉开头的 `/`
}

fun Long.toMinutes(): Int {
    return (this / 60).toInt()
}

fun Double.formatAsPercent(): String {
    val scaled = this * 100
    return if (scaled % 1.0 == 0.0) {
        "${scaled.toInt()}"
    } else {
        String.format("%.2f", scaled)
    }
}

fun Long.formatCountdown(): String {
    val totalSeconds = this
    val hours = totalSeconds / 3600
    val remainderSeconds = totalSeconds % 3600
    val minutes = remainderSeconds / 60
    val seconds = remainderSeconds % 60
    return String.format("%02d:%02d:%02d", hours, minutes, seconds)
}

inline val Int.dp: Int get() = (this * Resources.getSystem().displayMetrics.density + 0.5f).toInt()

inline val Int.px: Int get() = (this / Resources.getSystem().displayMetrics.density + 0.5f).toInt()

inline val Int.sp: Float
    get() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_SP, this.toFloat(), Resources.getSystem().displayMetrics
    )

fun Context.getDimen(id: Int): Int {
    return resources.getDimension(id).toInt()
}

fun ViewPager2.attach(
    manager: FragmentManager,
    lifeCycle: Lifecycle,
    fragments: List<Fragment>,
    isInputEnabled: Boolean = true, //默认可滑动
    isLimitAll: Boolean = false,//默认不加载全部
    onPageSelected: (position: Int) -> Unit
) {
    isUserInputEnabled = isInputEnabled
    if (isLimitAll) {
        offscreenPageLimit = fragments.size
    }
    adapter = ViewPagerFmAdapter(manager, lifeCycle, fragments)
    registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            onPageSelected(position)
        }
    })
}

// ViewPager2扩展函数，设置当前选中的索引
fun ViewPager2.checkSelected(position: Int) {
    if (currentItem != position) {
        setCurrentItem(position, false)
    }
}

fun Int.toShowDiamond(): Int {
    return this / 100
}

fun Context.getViewModelOwner(): ViewModelStoreOwner? {
    return when (this) {
        is ViewModelStoreOwner -> this
        is ContextWrapper -> baseContext.getViewModelOwner()
        else -> null
    }
}

fun Context.findLifecycleOwner(): LifecycleOwner? {
    return when (this) {
        is LifecycleOwner -> this
        is ContextWrapper -> baseContext.findLifecycleOwner()
        else -> null
    }
}

fun Context.jumpWithType(type: String, vararg params: Any?) {
    if (type.contains("http") || type.contains("https")) {
        // 网页跳转
        jump(WebActivity::class.java, Bundle().apply {
            putString("title", params?.firstOrNull().toString())
            putString("url", type)
        })
        return
    }
    // 内部跳转
    when (type) {
        //首页
        "Home" -> jump(MainActivity::class.java)
        //用户中心
        "UserCenter" -> jump(MainActivity::class.java)
        //结算
        "WeeklySettlement" -> jump(WalletActivity::class.java)
        //相册
        "Album" -> jump(AlbumManageActivity::class.java)
        //资料编辑
        "ProfileEdit" -> jump(ProfileEditActivity::class.java)
        //人脸视频
        "FaceVideo" -> jump(FaceRecordActivity::class.java)
        //绑卡
        "BankBind" -> jump(BankBindActivity::class.java)
        //钱包
        "Wallet" -> jump(WalletActivity::class.java)
        //提现
        "Withdraw" -> jump(WalletActivity::class.java)
        //认证通知
        "Certification" -> {
            GlobalNotificationManager.showCustomNotification(
                getString(R.string.verification_notice),
                getString(R.string.your_verification_has_been_approved),
                senderAvatar = "https://agent.heartmerge.vip/1734599904598officialNo.png"
            )
        }

        //认证被拒通知
        "CertificationFailed" -> {
            GlobalNotificationManager.showCustomNotification(
                getString(R.string.verification_notice),
                getString(R.string.your_verification_was_not_approved),
                senderAvatar = "https://agent.heartmerge.vip/1734599904598officialNo.png",
                onClick = {
                    jump(ProfileBuildActivity::class.java)
                })
        }

        //个人资料审核失败
        "PersonalInformationUpdateFaile" -> {
            GlobalNotificationManager.showCustomNotification(
                getString(R.string.profile_review_notice),
                getString(R.string.your_profile_review_failed),
                senderAvatar = "https://agent.heartmerge.vip/1734599904598officialNo.png",
                onClick = {
                    jump(ProfileEditActivity::class.java)
                })
        }

        "Invitefriends" -> {
            // todo 邀请好友
        }

        "InviteUsers" -> {
            // TODO: 邀请
        }
    }
}
