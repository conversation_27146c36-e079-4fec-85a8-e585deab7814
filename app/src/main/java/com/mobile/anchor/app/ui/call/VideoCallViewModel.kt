package com.mobile.anchor.app.ui.call

import androidx.lifecycle.viewModelScope
import com.bdc.android.library.ktnet.tryAwait
import com.mobile.anchor.app.data.model.RelationAction
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.network.ktnet.error.msg
import com.mobile.anchor.app.data.service.BodyParams
import com.mobile.anchor.app.data.service.GiftApiService
import com.mobile.anchor.app.data.service.UserApiService
import com.mobile.anchor.app.data.service.VideoApiService
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.viewmodels.BaseViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

class VideoCallViewModel : BaseViewModel() {
    private val videoCallViewModel: VideoApiService by NetDelegates()
    private val userApiService: UserApiService by NetDelegates()

    fun screenUpload(callId: Int, url: String) {
        viewModelScope.launch {
            val response =
                videoCallViewModel.screenShotUpload(BodyParams.screenUploadBody(callId, url))
                    .tryAwait {
                        LogX.e("screenUpload", "screenUpload failed: ${it.msg}  $callId")
                    }
            response?.let {
                LogX.e("screenUpload", "screenUpload success: $url")
            }
        }
    }

    fun videoUpload(callId: Int, url: String) {
        viewModelScope.launch {
            val response =
                videoCallViewModel.videoConfigUpload(BodyParams.videoUploadBody(callId, url))
                    .tryAwait {
                        LogX.e("videoUpload", "videoUpload failed: ${it.msg}  $callId")
                    }
            response?.let {
                LogX.e("videoUpload", "videoUpload success: $url")
            }
        }
    }

    fun relationOperation(action: RelationAction, userId: String) {
        viewModelScope.launch {
            val response = userApiService.relationOperation(
                action.value, BodyParams.relationOpParamsBody(userId)
            ).tryAwait {
                LogX.e("relationOperation", "relationOperation failed :$userId")
            }
            response?.let {
                LogX.e("relationOperation success: $userId")
            }
        }
    }

    fun countDownCoroutines(
        total: Int,
        onTick: (Int) -> Unit,
        onFinish: () -> Unit,
        scope: CoroutineScope = viewModelScope
    ): Job {
        return flow {
            for (i in total downTo 0 step 1) {
                emit(i)
                delay(1000)
            }
        }.flowOn(Dispatchers.Default)
            .onCompletion {
                onFinish.invoke()
            }
            .onEach { onTick.invoke(it) }
            .flowOn(Dispatchers.Main)
            .launchIn(scope)
    }
}