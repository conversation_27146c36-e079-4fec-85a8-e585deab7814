package com.mobile.anchor.app.ui.screens.login

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.bdc.android.library.extension.finish
import com.bdc.android.library.extension.jumpThenFinish
import com.mobile.anchor.app.MainActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.LoginUiState
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.manager.GoogleSignInManager
import com.mobile.anchor.app.navigation.Screen
import com.mobile.anchor.app.ui.components.AnchorButton
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTextField
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.utils.ContextHolder.context

/**
 * 登录选择页面
 * 提供Google登录和邮箱登录两种方式
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen(
    navController: NavController,
    viewModel: LoginViewModel = viewModel(),
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val uiState by viewModel.loginUiState.collectAsStateWithLifecycle()

    // 初始化Google登录管理器（先声明变量）
    var googleSignInManager: GoogleSignInManager? = null

    // 注册 Google 登录的 ActivityResultLauncher
    val googleSignInLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        googleSignInManager?.handleSignInResult(result.data)
    }

    // 创建Google登录管理器实例
    googleSignInManager = remember {
        GoogleSignInManager(context, googleSignInLauncher) { result ->
            viewModel.handleGoogleSignInResult(result)
        }
    }

    // 初始化Google登录
    DisposableEffect(context) {
        if (context is FragmentActivity) {
            googleSignInManager.initialize(context)
        }
        onDispose { }
    }

    // 监听Google登录成功，跳转到主页面
    LaunchedEffect(uiState.googleLoginSuccess) {
        if (uiState.googleLoginSuccess) {
            LogX.d("LoginScreen: Google登录成功，准备跳转到主页面 ${DataStoreManager.isUserLoggedIn()}")
            context.jumpThenFinish(MainActivity::class.java)
        }
    }
    LaunchedEffect(uiState.googleBindSuccess) {
        if (uiState.googleBindSuccess) {
            LogX.d("LoginScreen: Google绑定成功，准备跳转到主页面 ${DataStoreManager.isUserLoggedIn()}")
            context.finish()
        }
    }

    Box(
        modifier = modifier.fillMaxSize()
    ) {
        Image(
            painter = painterResource(R.mipmap.bg_login),
            contentDescription = "背景",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {

            Spacer(modifier = Modifier.weight(1F))

            // Google登录按钮
            Button(
                onClick = {
                    viewModel.clearError()
                    googleSignInManager.signIn()
                },
                enabled = !uiState.isGoogleLoading,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp),
                border = ButtonDefaults.outlinedButtonBorder.copy(
                    brush = Brush.horizontalGradient(
                        colors = listOf(Color.White, Color.White)
                    )
                ),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.White.copy(alpha = 0.1f), contentColor = Color.White
                ),
                shape = RoundedCornerShape(28.dp)
            ) {
                if (uiState.isGoogleLoading) {
                    CircularProgressIndicator(
                        color = Color.White, modifier = Modifier.size(24.dp)
                    )
                } else {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        // 使用邮箱图标代替Google图标（如果没有Google图标）
                        Image(
                            painter = painterResource(R.mipmap.ic_login_google),
                            contentDescription = "Google",
                            modifier = Modifier.size(24.dp),
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = stringResource(R.string.sign_in_with_google),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 邮箱登录按钮
            OutlinedButton(
                onClick = {
                    viewModel.clearError()
                    navController.navigate(Screen.EmailLogin.route)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.White
                ),
                border = ButtonDefaults.outlinedButtonBorder.copy(
                    brush = Brush.horizontalGradient(
                        colors = listOf(Color.White, Color.White)
                    )
                ),
                shape = RoundedCornerShape(28.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Image(
                        painter = painterResource(R.mipmap.ic_login_email),
                        contentDescription = "Email",
                        modifier = Modifier.size(24.dp),
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = stringResource(R.string.sign_in_with_email),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            // 错误信息显示
            uiState.errorMessage?.let { message ->
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = message,
                    color = Color.Red,
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            Spacer(modifier = Modifier.height(60.dp))
        }
    }
}

/**
 * 邮箱登录页面Compose实现
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailLoginScreen(
    viewModel: LoginViewModel, // 接收共享的ViewModel
    onNavigateToVerification: () -> Unit,
    onNavigateBack: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.loginUiState.collectAsStateWithLifecycle()
    EmailLoginContent(
        uiState = uiState,
        onNavigateToVerification = onNavigateToVerification,
        onNavigateBack = {
            // 返回时重置验证码发送状态，避免自动跳转
            viewModel.resetCodeSentState()
            onNavigateBack()
        },
        onEmailChange = viewModel::updateEmail,
        onGetCode = viewModel::getEmailCode,
        onClearError = viewModel::clearError,
        modifier = modifier
    )
}

@Composable
fun EmailLoginContent(
    uiState: LoginUiState,
    onNavigateToVerification: () -> Unit,
    onNavigateBack: () -> Unit = {},
    onEmailChange: (String) -> Unit = {},
    onGetCode: () -> Unit = {},
    onClearError: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    // 使用 remember 来跟踪之前的状态，只在状态变化时触发导航
    var previousCodeSent by remember { mutableStateOf(false) }

    // 监听验证码发送成功，自动跳转到验证页面
    LaunchedEffect(uiState.isCodeSent) {
        if (uiState.isCodeSent && !previousCodeSent) {
            previousCodeSent = true
            onNavigateToVerification()
        } else if (!uiState.isCodeSent) {
            previousCodeSent = false
        }
    }

    AnchorScaffold(topBar = {
        AnchorTopBar(
            title = context.getString(R.string.email_login), onNavigationClick = onNavigateBack
        )
    }) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(16.dp)
                .padding(top = paddingValues.calculateTopPadding()),
        ) {
            // 标题
            Text(
                text = stringResource(R.string.enter_your_email),
                color = Color(0xFF9FA1A6),
                fontSize = 14.sp,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(10.dp))

            AnchorTextField(uiState.email, stringResource(R.string.please_enter_email)) {
                onEmailChange.invoke(it)
            }

            Spacer(modifier = Modifier.height(32.dp))

            // 获取验证码按钮
            AnchorButton(
                text = stringResource(R.string.get_verification_code), onClick = {
                    onClearError()
                    onGetCode()
                }, enabled = uiState.isEmailValid, isLoading = uiState.isLoading
            )

            // 错误信息显示
            uiState.errorMessage?.let { message ->
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = message,
                    color = Color.Red,
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}


@Preview
@Composable
fun LoginScreenPreview() {
    AnchorTheme {
        LoginScreen(rememberNavController()) // Preview 中无法创建 ViewModel
    }
}

@Preview
@Composable
fun EmailLoginScreenPreview() {
    AnchorTheme {
        EmailLoginContent(
            uiState = LoginUiState(), onNavigateToVerification = {})
    }
}
