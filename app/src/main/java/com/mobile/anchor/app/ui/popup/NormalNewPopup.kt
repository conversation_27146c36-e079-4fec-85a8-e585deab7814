package com.mobile.anchor.app.ui.popup

import android.app.Activity
import android.content.res.ColorStateList
import androidx.core.content.ContextCompat
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.extension.setTextCompatColor
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.CenterPopupView
import com.mobile.anchor.app.R
import com.mobile.anchor.app.databinding.PopupNormalNewBinding
import kotlin.let
import kotlin.takeIf
import kotlin.text.isNotEmpty

/**
 * 作者：Lxf
 * 创建日期：2024/11/9 17:56
 * 描述：
 */
open class NormalNewPopup(
    private val context: Activity,
    val icon: Int,
    val title: String,
    val subTitle: String = "",
    val content: CharSequence,
    private val btnSure: String,
    private val btnCancel: String,
    private val mainColor: Int,
    private val titleColor: Int,
    val block: () -> Unit = {},
    private val cancelBlock: () -> Unit = {}
) : CenterPopupView(context) {

    lateinit var binding: PopupNormalNewBinding
    override fun getImplLayoutId(): Int = R.layout.popup_normal_new

    override fun initPopupContent() {
        super.initPopupContent()
        binding = PopupNormalNewBinding.bind(popupImplView)
        binding.ivEmoji.setImageResource(icon)

        binding.tvTitle.text = title
        binding.tvTitle.setTextCompatColor(titleColor)
        if (content.isNotEmpty()) {
            binding.tvContent.makeVisible()
            binding.tvContent.text = content
        }
        binding.btnSure.text = btnSure
//        val background = binding.btnSure.background as? GradientDrawable
        mainColor.takeIf { it > 0 }?.let {
            binding.btnSure.backgroundTintList =
                ColorStateList.valueOf(ContextCompat.getColor(context, mainColor))
        } // 替换为所需颜色
        if (btnCancel.isNotEmpty()) {
            binding.btnCancel.makeVisible()
            binding.btnCancel.text = btnCancel
        } else {
            binding.btnCancel.makeGone()
        }

        subTitle.takeIf { it.isNotEmpty() }?.let {
            binding.tvSubTitle.makeVisible()
            binding.tvSubTitle.text = it
        }

        binding.btnSure.click {
            dismissWith {
                block.invoke()
            }
        }
        binding.btnCancel.click {
            dismissWith {
                cancelBlock.invoke()
            }
        }
    }

    fun setContent(content: String) {
        binding.tvContent.text = content
    }
}

fun showNormalNewPopup(
    context: Activity,
    icon: Int = R.mipmap.ic_emoji_status_online,
    title: String = "",
    subTitle: String = "",
    content: CharSequence = "",
    btnSure: String = "",
    btnCancel: String = "",
    mainColor: Int = R.color.color_EC12E2,
    titleColor: Int = mainColor,
    dismissOnBackPressed: Boolean = true,
    dismissOnTouchOutside: Boolean = true,
    block: () -> Unit = {},
    cancelBlock: () -> Unit = {}
): NormalNewPopup {
    val normalNewPopup = NormalNewPopup(
        context,
        icon,
        title,
        subTitle,
        content,
        btnSure,
        btnCancel,
        mainColor,
        titleColor,
        block,
        cancelBlock
    )
    XPopup.Builder(context)
        .dismissOnBackPressed(dismissOnBackPressed)
        .dismissOnTouchOutside(dismissOnTouchOutside).asCustom(normalNewPopup).show()
    return normalNewPopup
}
