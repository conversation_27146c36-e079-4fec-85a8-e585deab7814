package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mobile.anchor.app.R
import com.mobile.anchor.app.ui.theme.AnchorTheme

/**
 * 审核状态徽章组件
 */
@Composable
fun ReviewStatusBadge(
    status: Int, modifier: Modifier = Modifier, isAvatar: Boolean = false // 是否为头像状态（影响形状）
) {
    val statusText = when (status) {
        1 -> stringResource(R.string.in_review)
        2 -> "" //已通过
        3 -> stringResource(R.string.rejected)
        else -> ""
    }

    val backgroundColor = when (status) {
        1 -> Color(0x80FFA500).copy(alpha = 0.5f) // 半透明橙色背景 - 审核中
        2 -> Color(0x8000C851).copy(alpha = 0.5f) // 半透明绿色背景 - 已通过
        3 -> Color(0x80FF6B6B).copy(alpha = 0.5f) // 半透明红色背景 - 未通过
        else -> Color.Transparent
    }

    val textColor = when (status) {
        1 -> Color(0xFFFFA500) // 黄色文字 - 审核中
        2 -> Color(0xFF00C851) // 绿色文字 - 已通过
        3 -> Color(0xFFFF6B6B) // 红色文字 - 未通过
        else -> Color.Transparent
    }

    if (statusText.isNotEmpty()) {
        Box(
            modifier = modifier
                .background(
                    color = backgroundColor, shape = if (isAvatar) {
                        RoundedCornerShape(bottomStart = 30.dp, bottomEnd = 30.dp)
                    } else {
                        // 昵称状态使用普通圆角
                        RoundedCornerShape(8.dp)
                    }
                )
                .padding(
                    horizontal = if (isAvatar) 8.dp else 6.dp,
                    vertical = if (isAvatar) 6.dp else 2.dp
                ), contentAlignment = Alignment.Center
        ) {
            Text(
                text = statusText,
                color = textColor,
                fontSize = if (isAvatar) 10.sp else 8.sp,
                fontWeight = FontWeight.Medium,
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

@Preview
@Composable
fun ReviewStatusBadgePreview() {
    AnchorTheme {
        Column(
            modifier = Modifier.padding(16.dp), verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text("头像审核状态:")
            ReviewStatusBadge(status = 0, isAvatar = true)
            ReviewStatusBadge(status = 1, isAvatar = true)
            ReviewStatusBadge(status = -1, isAvatar = true)

            Text("昵称审核状态:")
            ReviewStatusBadge(status = 0, isAvatar = false)
            ReviewStatusBadge(status = 1, isAvatar = false)
            ReviewStatusBadge(status = 3, isAvatar = false)
        }
    }
}
