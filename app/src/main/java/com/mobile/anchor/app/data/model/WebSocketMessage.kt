package com.mobile.anchor.app.data.model

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

// 通用 WebSocket 消息基类
@JsonClass(generateAdapter = true)
data class WebSocketResponse(
    val code: Int,
    val data: WebSocketMessage?,
    val message: String?
)

// WebSocket 消息体
@JsonClass(generateAdapter = true)
data class WebSocketMessage(
    val cmd: String,
    val owner: Long,
    val token: String,
    val seq: String,
    val connectionId: String? = null, // 连接ID字段
    val msg: String? = null,
    val extend: String? = null, // extend 字段实际是一个 JSON 字符串
    val ack: Any? = null // ack 字段可能为 null 或其他类型
)

// remote_call_invite 消息的 extend 结构
@JsonClass(generateAdapter = true)
data class RemoteCallInviteExtend(
    @Json(name = "peerUID") val peerUID: Int = 0,
    @<PERSON><PERSON>(name = "callID") val callID: Int = 0,
    @<PERSON><PERSON>(name = "rtcToken") val rtcToken: String = "",
    @<PERSON><PERSON>(name = "callerUid") val callerUid: String = "",
    @<PERSON><PERSON>(name = "callerNickname") val callerNickname: String= "",
    @Json(name = "callerAvatar") val callerAvatar: String= "",
    @Json(name = "caller_age") val callerAge: Int= 0,
    @Json(name = "caller_gender") val callerGender: Int= 1, // 1男2女
    @Json(name = "userCountry") val userCountry: CountryBean?,
    @Json(name = "call_type") val callType: Int= 1, // 通话类型 1正常呼叫 2匹配呼叫 3aib
    @Json(name = "anchor_call_timeout_refuse_duration") val anchorCallTimeoutRefuseDuration: Int, // 超时等待时长（秒）
    @Json(name = "free") val free: Boolean = false, // 是否使用匹配卡
    @Json(name = "is_recharge") val isRecharge: Boolean = false, // 是否充值
    @Json(name = "user_balance") val userBalance: Long = 0, // 用户余额
    @Json(name = "sense_tips") val senseTips: List<SenseTip>, // 提示词列表
    @Json(name = "core_duration_range") val coreDurationRange: List<CoreDurationRange>, // 核心倒计时数组
    @Json(name = "is_vip") val isVip: Boolean = false, // 是否vip
    @Json(name = "level") val level: Int = 0, // 用户等级
    @Json(name = "join_timeout") val joinTimeout: Int = 0, // 声网加入房间失败的超时时间
    @Json(name = "join_room_wait_timeout") val joinRoomWaitTimeout: Int = 0, // 进入房间后对方没进入房间的等待时间
    @Json(name = "showRelation") val showRelation: Int = 0 //  //0无关系 1用户关注主播 2用户被主播关注 3相互关注 4拉黑 5被拉黑

)


@JsonClass(generateAdapter = true)
data class SenseTip(
    val content: String,
    val type: Int
)

@JsonClass(generateAdapter = true)
data class CoreDurationRange(
    val title: String,
    val duration: Int
)

// remote_call_invite_cancel 消息的 extend 结构
@JsonClass(generateAdapter = true)
data class RemoteCallInviteCancelExtend(
    @Json(name = "peerUID") val peerUID: Int,
    @Json(name = "callID") val callID: Int
)

@JsonClass(generateAdapter = true)
data class RemoteCallSettleExtend(
    @Json(name = "call_id") val callID: Int = 0,
    @Json(name = "callCoin") val callCoin: Int = 0,
    @Json(name = "giftCoin") val giftCoin: Int = 0,
    @Json(name = "duration") val duration: Int = 0,
    @Json(name = "user") val user: UserBean?,
    @Json(name = "settle_stat") val settleStat: Int = 0,
    @Json(name = "call_type") val callType: Int = 0,
    @Json(name = "anchor_cycle_times") val anchorCycleTimes: Int = 0,
    @Json(name = "anchor_match_times_step") val anchorMatchTimesStep: Int = 0,
    @Json(name = "anchor_match_coin") val anchorMatchCoin: Int = 0,
    @Json(name = "extra_coin") val extraCoin: Int = 0
)

@JsonClass(generateAdapter = true)
data class RemoteCallSmallGiftExtend(
    @Json(name = "id") val id: Int = 0,
    @Json(name = "name") val name: String = "",
    @Json(name = "coin") val coin: Int = 0,
    @Json(name = "diamond") val diamond: Int = 0,
)
