package com.mobile.anchor.app.ui.screens.mine

import android.view.Gravity
import android.widget.FrameLayout
import android.widget.VideoView
import androidx.activity.compose.BackHandler
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import coil.compose.AsyncImage
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.extension.buildImageUrl
import com.mobile.anchor.app.logger.LogX
import kotlinx.coroutines.delay
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch

/**
 * 媒体预览页面
 * 支持图片和视频预览，带有从底部弹出的动画效果
 */
@Composable
fun MediaPreviewScreen(
    mediaList: List<UserBean.AlbumBean>, initialIndex: Int = 0, onDismiss: () -> Unit
) {
    if (mediaList.isEmpty()) {
        onDismiss()
        return
    }

    var isVisible by remember { mutableStateOf(false) }
    var isDismissing by remember { mutableStateOf(false) }

    // 启动进入动画
    LaunchedEffect(Unit) {
        delay(50) // 短暂延迟确保组件已渲染
        isVisible = true
    }

    // 处理返回键
    BackHandler {
        handleDismiss(
            isDismissing = isDismissing,
            onDismissingChange = { isDismissing = it },
            onDismiss = onDismiss
        )
    }

    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenHeight = with(density) { configuration.screenHeightDp.dp.toPx() }

    // 动画偏移量：从底部滑入，退出时滑出到底部
    val animatedOffset by animateFloatAsState(
        targetValue = when {
            isDismissing -> screenHeight // 退出时滑到底部
            isVisible -> 0f // 显示时在正常位置
            else -> screenHeight // 初始时在底部
        }, animationSpec = tween(durationMillis = 300), finishedListener = {
            if (isDismissing) {
                onDismiss()
            }
        }, label = "MediaPreviewOffset"
    )

    // 背景透明度动画
    val backgroundAlpha by animateFloatAsState(
        targetValue = if (isVisible && !isDismissing) 1f else 0f,
        animationSpec = tween(durationMillis = 300),
        label = "BackgroundAlpha"
    )

    Dialog(
        onDismissRequest = {
            handleDismiss(
                isDismissing = isDismissing,
                onDismissingChange = { isDismissing = it },
                onDismiss = onDismiss
            )
        }, properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
                .offset(y = with(density) { animatedOffset.toDp() })
        ) {
            // 分页器用于支持多媒体浏览
            val pagerState = rememberPagerState(
                initialPage = initialIndex.coerceIn(0, mediaList.size - 1),
                pageCount = { mediaList.size })

            HorizontalPager(
                state = pagerState, modifier = Modifier.fillMaxSize()
            ) { page ->
                val albumBean = mediaList[page]

                when (albumBean.resource_type) {
                    1 -> {
                        // 图片预览
                        ImagePreviewComponent(
                            imageUrl = albumBean.url, modifier = Modifier.fillMaxSize()
                        )
                    }

                    2 -> {
                        // 视频预览
                        VideoPreviewComponent(
                            videoUrl = albumBean.url, modifier = Modifier.fillMaxSize()
                        )
                    }

                    else -> {
                        // 默认显示图片预览
                        ImagePreviewComponent(
                            imageUrl = albumBean.url, modifier = Modifier.fillMaxSize()
                        )
                    }
                }
            }

            // 关闭按钮
            Box(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .statusBarsPadding()
                    .padding(16.dp)
                    .size(40.dp)
                    .background(
                        Color.Black.copy(alpha = 0.5f), CircleShape
                    )
                    .clickable {
                        handleDismiss(
                            isDismissing = isDismissing,
                            onDismissingChange = { isDismissing = it },
                            onDismiss = onDismiss
                        )
                    }
                    .alpha(backgroundAlpha), contentAlignment = Alignment.Center) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "关闭",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

/**
 * 处理关闭逻辑
 */
private fun handleDismiss(
    isDismissing: Boolean, onDismissingChange: (Boolean) -> Unit, onDismiss: () -> Unit
) {
    if (!isDismissing) {
        onDismissingChange(true)
    }
}


/**
 * 图片预览组件
 * 支持双指缩放（1x-5x）、拖拽移动和双击放大/恢复
 */
@Composable
private fun ImagePreviewComponent(
    imageUrl: String, modifier: Modifier = Modifier
) {
    var scale by remember { mutableFloatStateOf(1f) }
    var offsetX by remember { mutableFloatStateOf(0f) }
    var offsetY by remember { mutableFloatStateOf(0f) }
    var isLoading by remember { mutableStateOf(true) }
    var hasError by remember { mutableStateOf(false) }

    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidthPx = with(density) { configuration.screenWidthDp.dp.toPx() } // Renamed to Px
    val screenHeightPx = with(density) { configuration.screenHeightDp.dp.toPx() } // Renamed to Px

    // 动画相关
    val coroutineScope = rememberCoroutineScope()
    val scaleAnimatable = remember { Animatable(1f) }
    val offsetXAnimatable = remember { Animatable(0f) }
    val offsetYAnimatable = remember { Animatable(0f) }

    // 双击放大的目标缩放比例
    val doubleTapScale = 2.5f

    // 同步动画值到状态
    LaunchedEffect(scaleAnimatable.value) {
        scale = scaleAnimatable.value
    }
    LaunchedEffect(offsetXAnimatable.value) {
        offsetX = offsetXAnimatable.value
    }
    LaunchedEffect(offsetYAnimatable.value) {
        offsetY = offsetYAnimatable.value
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
            .pointerInput(Unit) { // Combined gesture detector for double tap
                detectTapGestures(
                    onDoubleTap = { tapOffset ->
                        coroutineScope.launch {
                            val currentScale = scaleAnimatable.value
                            val targetScale: Float
                            val targetOffsetX: Float
                            val targetOffsetY: Float

                            if (currentScale > 1.5f) {
                                // Restore to original state
                                targetScale = 1f
                                targetOffsetX = 0f
                                targetOffsetY = 0f

                                joinAll(
                                    launch { scaleAnimatable.animateTo(targetScale, tween(200)) },
                                    launch {
                                        offsetXAnimatable.animateTo(
                                            targetOffsetX, tween(200)
                                        )
                                    },
                                    launch {
                                        offsetYAnimatable.animateTo(
                                            targetOffsetY, tween(200)
                                        )
                                    })
                            } else {
                                // Zoom in and center on tap location
                                targetScale = doubleTapScale
                                val currentDisplayedOffsetX = offsetXAnimatable.value
                                val currentDisplayedOffsetY = offsetYAnimatable.value

                                val tapPointRelativeToImageX =
                                    (tapOffset.x - (screenWidthPx / 2f + currentDisplayedOffsetX)) / currentScale
                                val tapPointRelativeToImageY =
                                    (tapOffset.y - (screenHeightPx / 2f + currentDisplayedOffsetY)) / currentScale

                                targetOffsetX =
                                    (screenWidthPx / 2f) - (tapPointRelativeToImageX * targetScale + screenWidthPx / 2f)
                                targetOffsetY =
                                    (screenHeightPx / 2f) - (tapPointRelativeToImageY * targetScale + screenHeightPx / 2f)

                                val maxBoundX = (screenWidthPx * (targetScale - 1f)) / 2f
                                val maxBoundY = (screenHeightPx * (targetScale - 1f)) / 2f

                                val coercedOffsetX = targetOffsetX.coerceIn(-maxBoundX, maxBoundX)
                                val coercedOffsetY = targetOffsetY.coerceIn(-maxBoundY, maxBoundY)

                                joinAll(
                                    launch { scaleAnimatable.animateTo(targetScale, tween(200)) },
                                    launch {
                                        offsetXAnimatable.animateTo(
                                            coercedOffsetX, tween(200)
                                        )
                                    },
                                    launch {
                                        offsetYAnimatable.animateTo(
                                            coercedOffsetY, tween(200)
                                        )
                                    })
                            }
                        }
                    })
            }
            // Conditional pointerInput for transform gestures: only enabled when zoomed in
        .then(
            if (scaleAnimatable.value > 1.05f) { // Using a small threshold to prevent flickering around 1.0f
            Modifier.pointerInput(Unit) {
                detectTransformGestures { _, pan, zoom, _ ->
                    val currentScaleValue = scaleAnimatable.value
                    val newScale = (currentScaleValue * zoom).coerceIn(1f, 5f)

                    coroutineScope.launch {
                        scaleAnimatable.snapTo(newScale)

                        val maxOffsetX = (screenWidthPx * (newScale - 1f)) / 2f
                        val maxOffsetY = (screenHeightPx * (newScale - 1f)) / 2f

                        val currentOffsetXValue = offsetXAnimatable.value
                        val currentOffsetYValue = offsetYAnimatable.value

                        // Apply pan, clamping within bounds
                        val newOffsetX =
                            (currentOffsetXValue + pan.x).coerceIn(-maxOffsetX, maxOffsetX)
                        val newOffsetY =
                            (currentOffsetYValue + pan.y).coerceIn(-maxOffsetY, maxOffsetY)

                        offsetXAnimatable.snapTo(newOffsetX)
                        offsetYAnimatable.snapTo(newOffsetY)
                    }
                }
            }
        } else {
            Modifier // No additional modifier when not zoomed or minimally zoomed
        }), contentAlignment = Alignment.Center) {
        AsyncImage(
            model = imageUrl.buildImageUrl(highQuality = true),
            contentDescription = "预览图片",
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer(
                    scaleX = scale, scaleY = scale, translationX = offsetX, translationY = offsetY
                )
                .clip(RectangleShape),
            contentScale = ContentScale.Fit,
            placeholder = ColorPainter(Color.Gray),
            error = ColorPainter(Color.Gray),
            onLoading = {
                isLoading = true
                hasError = false
            },
            onSuccess = {
                isLoading = false
                hasError = false
            },
            onError = {
                isLoading = false
                hasError = true
            })

        // 加载指示器
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.3f)),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(48.dp), color = Color.White, strokeWidth = 4.dp
                )
            }
        }
    }
}

/**
 * 视频预览组件
 * 支持播放/暂停控制、循环播放、进度显示
 */
@Composable
private fun VideoPreviewComponent(
    videoUrl: String, modifier: Modifier = Modifier
) {
    var isPlaying by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(true) }
    var hasError by remember { mutableStateOf(false) }
    var currentPosition by remember { mutableIntStateOf(0) }
    var duration by remember { mutableIntStateOf(0) }
    var videoView by remember { mutableStateOf<VideoView?>(null) }
    var showControls by remember { mutableStateOf(true) }

    // 自动隐藏控制栏
    LaunchedEffect(showControls, isPlaying) {
        if (showControls && isPlaying) {
            delay(3000) // 3秒后自动隐藏
            showControls = false
        }
    }

    // 更新播放进度
    LaunchedEffect(isPlaying) {
        while (isPlaying) {
            videoView?.let { vv ->
                if (vv.isPlaying) {
                    currentPosition = vv.currentPosition
                    if (duration == 0) {
                        duration = vv.duration
                    }
                }
            }
            delay(100) // 每100ms更新一次进度
        }
    }

    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            videoView?.stopPlayback()
        }
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
            .pointerInput(Unit) {
                detectTapGestures(onTap = {
                    showControls = !showControls
                }, onDoubleTap = {
                    videoView?.let { vv ->
                        if (vv.isPlaying) {
                            vv.pause()
                            isPlaying = false
                        } else {
                            vv.start()
                            isPlaying = true
                        }
                    }
                })
            }) {
        // 视频播放器
        AndroidView(
            factory = { ctx ->
                FrameLayout(ctx).apply {
                    setBackgroundColor(android.graphics.Color.BLACK) // 设置黑色背景
                    val vv = VideoView(ctx).apply {
                        // 不要让 VideoView 被放在最顶层，避免遮盖其他控件
                        videoView = this

                        // 设置错误监听器
                        setOnErrorListener { _, what, extra ->
                            LogX.e("VideoPreview", "播放错误: what=$what, extra=$extra")
                            hasError = true
                            isLoading = false
                            false
                        }

                        // 设置准备监听器
                        setOnPreparedListener { mediaPlayer ->
                            LogX.d("VideoPreview", "视频准备完成")
                            isLoading = false
                            hasError = false
                            duration = mediaPlayer.duration
                            mediaPlayer.isLooping = true // 循环播放

                            // 自动开始播放
                            start()
                            isPlaying = true
                        }

                        // 设置完成监听器
                        setOnCompletionListener {
                            LogX.d("VideoPreview", "视频播放完成")
                            // 由于设置了循环播放，这里通常不会被调用
                        }

                        // 设置视频URI
                        try {
                            setVideoPath(videoUrl)
                            requestFocus()
                        } catch (e: Exception) {
                            LogX.e("VideoPreview", "设置视频路径失败: ${e.message}")
                            hasError = true
                            isLoading = false
                        }
                    }


                    addView(
                        vv, FrameLayout.LayoutParams(
                            FrameLayout.LayoutParams.MATCH_PARENT,
                            FrameLayout.LayoutParams.WRAP_CONTENT,
                            Gravity.CENTER
                        )
                    )
                }
            }, modifier = Modifier.fillMaxSize()
        )

        // 加载指示器
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.7f)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(48.dp), color = Color.White, strokeWidth = 4.dp
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = stringResource(R.string.loading),
                        color = Color.White,
                        fontSize = 14.sp
                    )
                }
            }
        }

        // 错误提示
        if (hasError) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.7f)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(R.string.video_loading_failed),
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = stringResource(R.string.please_check_the_network_connection_or_video_link),
                        color = Color.White.copy(alpha = 0.7f),
                        fontSize = 12.sp
                    )
                }
            }
        }

        // 播放控制栏
        if (showControls && !isLoading && !hasError) {
            VideoControlsOverlay(
                isPlaying = isPlaying,
                currentPosition = currentPosition,
                duration = duration,
                onPlayPause = {
                    videoView?.let { vv ->
                        if (vv.isPlaying) {
                            vv.pause()
                            isPlaying = false
                        } else {
                            vv.start()
                            isPlaying = true
                        }
                    }
                },
                onSeek = { position ->
                    videoView?.seekTo(position)
                    currentPosition = position
                },
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .pointerInput(Unit) {})
        }

        // 中央播放按钮（仅在暂停时显示）
        if (!isPlaying && !isLoading && !hasError && showControls) {
            Box(
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(80.dp)
                    .background(
                        Color.Black.copy(alpha = 0.6f), CircleShape
                    )
                    .clickable {
                        videoView?.let { vv ->
                            vv.start()
                            isPlaying = true
                        }
                    }, contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = "播放",
                    tint = Color.White,
                    modifier = Modifier.size(40.dp)
                )
            }
        }
    }
}

/**
 * 格式化时间显示
 */
@Composable
private fun VideoControlsOverlay(
    isPlaying: Boolean,
    currentPosition: Int,
    duration: Int,
    onPlayPause: () -> Unit,
    onSeek: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.6f), RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp)
            )
            .padding(16.dp)
    ) {
        // 进度条
        if (duration > 0) {
            // 时间显示
            Row(
                modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = formatTime(currentPosition), color = Color.White, fontSize = 12.sp
                )
                LinearProgressIndicator(
                    progress = { currentPosition.toFloat() / duration.toFloat() },
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .height(4.dp)
                        .padding(horizontal = 8.dp)
                        .clip(RoundedCornerShape(2.dp)),
                    color = Color.White,
                    trackColor = Color.White.copy(alpha = 0.3f)
                )
                Text(
                    text = formatTime(duration), color = Color.White, fontSize = 12.sp
                )
            }
        }
    }
}

/**
 * 格式化时间显示
 */
private fun formatTime(timeMs: Int): String {
    val seconds = timeMs / 1000
    val minutes = seconds / 60
    val remainingSeconds = seconds % 60
    return String.format("%02d:%02d", minutes, remainingSeconds)
}