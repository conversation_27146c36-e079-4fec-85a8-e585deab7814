package com.mobile.anchor.app.ui.screens.callhistory

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.mobile.anchor.app.data.model.CallHistoryItemBean
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.anchor.AnchorService
import com.mobile.anchor.app.ui.viewmodels.BaseViewModel
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 黑名单页面ViewModel
 */
class CallHistoryViewModel : BaseViewModel() {

    private val service: AnchorService by NetDelegates()
    val historyList = mutableStateListOf<CallHistoryItemBean>()
    var responseCursor = mutableStateOf("")
    val isRefreshing = mutableStateOf(false)
    val isLoadingMore = mutableStateOf(false)
    val hasMoreData = mutableStateOf(true) // 新增状态

    fun loadCallHistoryList(cursor: String, type: Int = 0, size: Int = 20, isRefresh: Boolean = false) {
        if (isRefresh) {
            isRefreshing.value = true
        } else {
            isLoadingMore.value = true
        }

        ktHttpRequest {
            val response = service.getCallList(cursor, size, type).await()
            response?.let {
                if (isRefresh || cursor.isEmpty()) {
                    historyList.clear()
                }
                responseCursor.value = it.cursor
                it.list?.let { newHistoryList ->
                    historyList.addAll(newHistoryList)
                    hasMoreData.value = newHistoryList.size == size // 根据返回列表大小判断是否还有更多数据
                } ?: run {
                    hasMoreData.value = false // 如果列表为空，则没有更多数据
                }
            } ?: run {
                hasMoreData.value = false // 发生错误时，也认为没有更多数据
            }
            isRefreshing.value = false
            isLoadingMore.value = false
        }
    }

    fun refreshCallHistoryList() {
        loadCallHistoryList(cursor = "", isRefresh = true)
    }

    fun loadMoreCallHistoryList() {
        if (responseCursor.value.isNotEmpty() && !isLoadingMore.value) {
            loadCallHistoryList(cursor = responseCursor.value)
        }
    }

    fun translateDuration(timestampInSeconds:Long): String {
        val date = Date(timestampInSeconds)
        val formatter = SimpleDateFormat("MM/dd HH:mm:ss", Locale.getDefault())
        return formatter.format(date)
    }
}