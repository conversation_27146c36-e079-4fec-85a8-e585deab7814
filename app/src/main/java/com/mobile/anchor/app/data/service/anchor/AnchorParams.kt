package com.mobile.anchor.app.data.service.anchor

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/24 10:53 上午
 * @description：
 */
object AnchorParams {

    fun getRandomAnchorBody(): RequestBody {
        return jsonToBody(objectsMapToJson(emptyMap()))
    }

    fun videoHistoryParamsBody(current: Int, size: Int): RequestBody {
        val map = mapOf(
            "current" to current,
            "size" to size,
        )
        return jsonToBody(objectsMapToJson(map))
    }

    /**
     * TODO moshi map 转json字符串
     *
     *
     * @param map
     * @return
     */
    private fun objectsMapToJson(map: Map<String, Any>): String {
        val moshi = Moshi.Builder().build()
        val jsonAdapter: JsonAdapter<Map<String, Any>> = moshi.adapter(
            Types.newParameterizedType(
                MutableMap::class.java, String::class.java, Any::class.java
            )
        )
        return jsonAdapter.indent(" ").toJson(map) ?: ""
    }

    /**
     * TODO json 字符串转RequestBody
     *
     * @param json
     * @return
     */
    private fun jsonToBody(json: String): RequestBody =
        json.toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
}