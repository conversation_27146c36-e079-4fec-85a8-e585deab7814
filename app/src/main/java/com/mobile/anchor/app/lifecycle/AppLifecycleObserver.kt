package com.mobile.anchor.app.lifecycle

import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import androidx.core.net.toUri
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.service.OverlayNotification
import com.mobile.anchor.app.service.OverlayWindowService
import com.mobile.anchor.app.ui.call.CallVideoChatActivity
import com.mobile.anchor.app.utils.ActivityUtils
import com.mobile.anchor.app.utils.ContextHolder

/**
 * 应用生命周期观察者
 * 用于监听应用进入前台/后台，并控制消息角标的显示/隐藏
 */
class AppLifecycleObserver : DefaultLifecycleObserver {

    companion object {
        private const val TAG = "AppLifecycleObserver"

        // 消息角标开关的SharedPreferences键
        private const val PREF_NAME = "message_badge_settings"
        private const val KEY_MESSAGE_BADGE_ENABLED = "message_badge_enabled"

        // 单例实例
        @Volatile
        private var instance: AppLifecycleObserver? = null

        fun getInstance(): AppLifecycleObserver {
            return instance ?: synchronized(this) {
                instance ?: AppLifecycleObserver().also { instance = it }
            }
        }

        /**
         * 检查是否有悬浮窗权限
         */
        fun hasOverlayPermission(context: Context): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Settings.canDrawOverlays(context)
            } else {
                true // 低版本Android默认有权限
            }
        }

        /**
         * 请求悬浮窗权限
         */
        fun requestOverlayPermission(context: Context) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(context)) {
                val intent = Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    "package:${context.packageName}".toUri()
                )
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
            }
        }

        /**
         * 设置消息角标开关状态
         */
        fun setMessageBadgeEnabled(enabled: Boolean) {
            val context = ContextHolder.context.applicationContext
            val sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
            sharedPreferences.edit().putBoolean(KEY_MESSAGE_BADGE_ENABLED, enabled).apply()
        }

        /**
         * 获取消息角标开关状态
         */
        fun isMessageBadgeEnabled(): Boolean {
            val context = ContextHolder.context.applicationContext
            val sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
            return sharedPreferences.getBoolean(KEY_MESSAGE_BADGE_ENABLED, true) // 默认开启
        }
    }

    // 应用是否在前台
    private var isAppForeground = false

    // 是否为冷启动（应用进程启动后第一次进入前台）
    private var isColdStart = true

    /**
     * 应用进入前台
     */
    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        LogX.d(TAG, "应用进入前台，冷启动状态: $isColdStart ${ActivityUtils.getCurrent()}")
        isAppForeground = true

        // 应用进入前台，停止消息角标服务
        stopMessageBadgeService()
    }

    /**
     * 应用进入后台
     */
    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        LogX.d(TAG, "应用进入后台 ${ActivityUtils.getCurrent()}")
        isAppForeground = false

        // 标记下次进入前台不是冷启动
        isColdStart = false

        val context = ContextHolder.context.applicationContext
        // 应用进入后台，启动消息角标服务（如果开关已开启）
        if (isMessageBadgeEnabled() && hasOverlayPermission(context)) {
            startMessageBadgeService()

            if (ActivityUtils.getCurrent() is CallVideoChatActivity){
                ActivityUtils.getCurrent()?.let {
                    OverlayNotification.notify(it)
                }
            }
        }
    }

    /**
     * 启动消息角标服务
     */
    private fun startMessageBadgeService() {
        val context = ContextHolder.context.applicationContext
        // 启动服务
        val intent = Intent(context, OverlayWindowService::class.java).apply {
            action = OverlayWindowService.ACTION_START_SERVICE
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Android 12及以上需要特殊处理
            try {
                context.startForegroundService(intent)
            } catch (e: Exception) {
                LogX.e(TAG, "启动前台服务失败: ${e.message}")
                // 尝试普通启动
                context.startService(intent)
            }
        } else {
            context.startService(intent)
        }

        LogX.d(TAG, "消息角标服务已启动")
    }

    /**
     * 停止消息角标服务
     */
    private fun stopMessageBadgeService() {
        val context = ContextHolder.context.applicationContext
        val intent = Intent(context, OverlayWindowService::class.java).apply {
            action = OverlayWindowService.ACTION_STOP_SERVICE
        }
        context.stopService(intent)
        LogX.d(TAG, "消息角标服务已停止")
    }

    /**
     * 更新消息数量
     */
    fun updateMessageCount(count: Int) {
        // 只有在应用在后台且消息角标开关开启时才更新消息数量
        if (!isAppForeground && isMessageBadgeEnabled()) {
            val context = ContextHolder.context.applicationContext
            val intent = Intent(context, OverlayWindowService::class.java).apply {
                action = OverlayWindowService.ACTION_UPDATE_MESSAGE_COUNT
                putExtra(OverlayWindowService.EXTRA_MESSAGE_COUNT, count)
            }

            // 如果服务已经在运行，直接使用startService更新消息数量
            context.startService(intent)
        }
    }

    /**
     * 应用是否在前台
     */
    fun isAppInForeground(): Boolean {
        return isAppForeground
    }

    /**
     * 检查是否为冷启动并标记为已启动
     * @return true表示冷启动，false表示从后台恢复
     */
    fun checkAndMarkColdStart(): Boolean {
        val wasColdStart = isColdStart
        if (wasColdStart) {
            // 标记为已经不是冷启动
            isColdStart = false
            LogX.d(TAG, "检测到冷启动，已标记为非冷启动状态")
        } else {
            LogX.d(TAG, "从后台恢复，非冷启动")
        }
        return wasColdStart
    }
}