package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/6/16 20:29
 * @description :
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class TaskBean(
    val task_id: String,//任务id
    val title: String,//任务id

    val sub_title: String,//副标题
    val icon: String, val reward: RewardBean,//奖励信息

    val task_type: Int,//任务类型 1一次性任务 2每日任务 3每周任务
    val jump_url: String,//跳转url

    val target: Int,//1内部跳转 2网页跳转
    val task_status: Int,//-1 未领取 1 已完成未领取 2已领取

    val task_category: String,//任务类别

    val complete_config_json: CompleteConfigBean,//完成目标
    val step: Int,//进度
    val user_type: Int = 0,//用户类型 1用户2主播
    val process_type: Int,//0 不现实进度 1显示进度
    val round: Int,//轮次
    val level: Int//等级

) : Parcelable {

    val isAnchorTask: Boolean get() = user_type == 2

    @Parcelize
    @JsonClass(generateAdapter = true)
    data class RewardBean(
        val diamond_num: Int, val gold_num: Int
    ) : Parcelable

    @Parcelize
    @JsonClass(generateAdapter = true)
    data class CompleteConfigBean(
        val total: Int
    ) : Parcelable
}
