package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.mobile.anchor.app.ui.view.MarqueeView

/**
 * 基于 MarqueeView 实现的 Compose 版本跑马灯组件
 * 支持多行文本，动态高度调整，从下到上滚动动画
 */
@Composable
fun MarqueeText(
    texts: List<String>,
    textColor: Color = Color.White,
    fontSize: TextUnit = 14.sp,
    maxLines: Int = 5,
    displayDuration: Long = 3000,
    scrollInterval: Long = 5000
) {
    AndroidView(
        factory = { context ->
            MarqueeView(context).apply {
                setMaxLines(maxLines)
                // 确保文本大小适配不同设备
                setTextGravity(android.view.Gravity.TOP or android.view.Gravity.START)
            }
        }, modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight() // 改为 wrapContentHeight 让高度自适应
            .padding(16.dp), update = { view ->
            view.setData(texts)
            // 强制重新测量和布局
            view.requestLayout()
        })
}

@Preview(showBackground = true)
@Composable
fun MarqueeTextPreview() {
    val sampleTexts = remember {
        listOf(
            "这是一条短消息。",
            "这是一条稍微长一点的消息，可能会占据两行。",
            "这是一条非常非常长的消息，它将尝试占用更多的空间，甚至可能达到最大行数，看看它是否能正确显示并滚动，这是为了测试多行文本的动画效果。",
            "第三条消息，这是单行。"
        )
    }

    Column(Modifier.padding(16.dp)) {
        Text("新版跑马灯测试:", style = MaterialTheme.typography.titleMedium)
        MarqueeText(
            texts = sampleTexts,
        )
    }
}
