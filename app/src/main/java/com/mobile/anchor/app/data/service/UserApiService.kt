package com.mobile.anchor.app.data.service

import com.bdc.android.library.ktnet.coroutines.Await
import com.mobile.anchor.app.data.model.AuthBean
import com.mobile.anchor.app.data.model.BankBean
import com.mobile.anchor.app.data.model.ReferralProfitBean
import com.mobile.anchor.app.data.model.RevenueBean
import com.mobile.anchor.app.data.model.ReviewBean
import com.mobile.anchor.app.data.model.SettlementBean
import com.mobile.anchor.app.data.model.SupportMeStatus
import com.mobile.anchor.app.data.model.TaskBean
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.data.model.WalletBean
import com.mobile.anchor.app.data.model.WalletCurrencyBean
import com.mobile.anchor.app.data.model.WalletWrapperCurrencyBean
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * 登录相关API服务
 */
interface UserApiService {

    /**
     * 获取邮箱验证码
     */
    @POST("/api/v1/common/email/send_code")
    suspend fun getEmailCode(@Body body: RequestBody): Await<Any>

    /**
     * 邮箱验证码登录 - 新的用户登录接口
     */
    @POST("/api/v1/anchor/login")
    suspend fun userLogin(@Body body: RequestBody): Await<AuthBean>


    @POST("/api/v1/anchor/logout")
    suspend fun userLogout(): Await<Any?>

    /**
     * 更新用户资料
     */
    @POST("/api/v1/anchor/update/info")
    suspend fun updateProfile(@Body request: RequestBody): Await<Any?>

    /**
     * 获取用户详情
     * @param userId 用户ID
     */
    @GET("/api/v1/anchor/user/detail")
    suspend fun getUserDetail(@Query("user_id") userId: String): Await<UserBean?>

    /**
     * 获取用户信息
     * 通用参数会自动添加，这里只需要定义业务参数
     */
    @GET("/api/v1/anchor/info")
    suspend fun getUser(): Await<AuthBean>

    @POST("/api/v1/anchor/update/medias")
    suspend fun updateAlbum(@Body request: RequestBody): Await<Any?>

    @GET("/api/v1/anchor/settle/bank/list")
    suspend fun getBankList(): Await<List<BankBean>?>

    @POST("/api/v1/anchor/settle/bank/set")
    suspend fun bindBank(@Body request: RequestBody): Await<Any>

    @GET("/api/v1/anchor/settle/bank/info")
    suspend fun getBankInfo(@Query("user_id") userId: String): Await<BankBean?>

    @GET("/api/v1/anchor/profile/info")
    suspend fun getReviewInfo(): Await<ReviewBean?>

    @GET("/api/v1/anchor/language/list")
    suspend fun getLanguageList(): Await<PageResponse<UserBean.LanguageBean>?>

    @POST("/api/v1/anchor/language/set")
    suspend fun setLanguage(@Body requestBody: RequestBody): Await<Any?>

    @GET("/api/v1/anchor/withdraw/list")
    suspend fun getSettlementList(): Await<PageResponse<SettlementBean>?>

    @GET("/api/v1/anchor/coin_record/list")
    suspend fun getRevenueList(
        @Query("start") start: Long,
        @Query("end") end: Long,
        @Query("cursor") cursor: String,
        @Query("size") size: Int
    ): Await<PageResponse<RevenueBean>?>

    @POST("/api/v1/anchor/withdraw/apply")
    suspend fun applyWithdraw(@Body request: RequestBody): Await<Any?>

    @POST("/api/v1/anchor/relation/{name}/op")
    suspend fun relationOperation(
        @Path("name") name: String, @Body request: RequestBody
    ): Await<Any?>

    @GET("/api/v1/anchor/task/list")
    suspend fun getTaskList(): Await<PageResponse<TaskBean>?>

    @GET("/api/v1/anchor/relation/{name}/list")
    suspend fun getRelationList(
        @Path("name") name: String, @Query("cursor") cursor: String
    ): Await<PageResponse<UserBean>?>

    @POST("/api/v1/anchor/user/list")
    suspend fun getUserList(@Body body: RequestBody): Await<PageResponse<UserBean>?>

    @GET("/api/v1/anchor/bind/google")
    suspend fun bindGoogle(@Body body: RequestBody): Await<Any?>

    @GET("/api/v1/anchor/wallet/query")
    suspend fun getWalletInfo(): Await<WalletBean?>

    @GET("/api/v1/anchor/withdraw/info")
    suspend fun getWithdrawInfo(): Await<WalletCurrencyBean?>

    @POST("/api/v1/anchor/update/call_price")
    suspend fun setupCallPrice(@Body body: RequestBody): Await<Any?>

    @GET("/api/v1/anchor/settle/currency/list")
    suspend fun getCurrencyList(): Await<WalletWrapperCurrencyBean?>

    @POST("/api/v1/anchor/settle/currency/set")
    suspend fun setCurrency(@Body body: RequestBody): Await<Any?>

    @GET("/api/v1/anchor/relation/lover/show")
    suspend fun getSupportMeStatus(@Query("userId") id: String): Await<SupportMeStatus?>

    @POST("/api/v1/anchor/relation/lover/send")
    suspend fun requestSupportMe(@Body body: RequestBody): Await<Boolean?>

    @GET("/api/v1/anchor/profit/list")
    suspend fun getReferralProfitList(@Query("cursor") cursor: String, @Query("size") size: Int): Await<PageResponse<ReferralProfitBean>?> //收益记录

}