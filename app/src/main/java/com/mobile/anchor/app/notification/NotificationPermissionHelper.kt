package com.mobile.anchor.app.notification

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.NotificationManagerCompat
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.popup.ComposePopup

/**
 * 通知权限检查助手类
 * 用于检查和请求通知相关权限
 */
object NotificationPermissionHelper {
    
    private const val TAG = "NotificationPermissionHelper"
    
    /**
     * 检查是否有悬浮窗权限
     */
    fun hasOverlayPermission(context: Context): Bo<PERSON>an {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }
    
    /**
     * 检查是否有通知权限
     */
    fun hasNotificationPermission(context: Context): Boolean {
        return NotificationManagerCompat.from(context).areNotificationsEnabled()
    }
    
    /**
     * 检查全局通知所需的所有权限
     */
    fun hasAllRequiredPermissions(context: Context): Boolean {
        val hasOverlay = hasOverlayPermission(context)
        val hasNotification = hasNotificationPermission(context)
        
        LogX.d(TAG, "权限检查结果 - 悬浮窗权限: $hasOverlay, 通知权限: $hasNotification")
        
        return hasOverlay && hasNotification
    }
    
    /**
     * 请求悬浮窗权限
     */
    fun requestOverlayPermission(activity: Activity, onResult: ((Boolean) -> Unit)? = null) {
        if (hasOverlayPermission(activity)) {
            onResult?.invoke(true)
            return
        }
        
        try {
            ComposePopup.showConfirmDialog(
                context = activity,
                title = "需要悬浮窗权限",
                content = "为了显示全局通知，需要开启悬浮窗权限",
                confirmText = "去设置",
                cancelText = "取消",
                onConfirm = {
                    try {
                        val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                            data = Uri.parse("package:${activity.packageName}")
                        }
                        activity.startActivity(intent)
                    } catch (e: Exception) {
                        LogX.e("$TAG, 打开悬浮窗权限设置失败: ${e.message}", e)
                        onResult?.invoke(false)
                    }
                },
                onCancel = {
                    onResult?.invoke(false)
                }
            )
        } catch (e: Exception) {
            LogX.e("$TAG,请求悬浮窗权限失败: ${e.message}", e)
            onResult?.invoke(false)
        }
    }
    
    /**
     * 请求通知权限
     */
    fun requestNotificationPermission(activity: Activity, onResult: ((Boolean) -> Unit)? = null) {
        if (hasNotificationPermission(activity)) {
            onResult?.invoke(true)
            return
        }
        
        try {
            ComposePopup.showConfirmDialog(
                context = activity,
                title = "需要通知权限",
                content = "为了显示消息通知，需要开启通知权限",
                confirmText = "去设置",
                cancelText = "取消",
                onConfirm = {
                    try {
                        val intent = Intent().apply {
                            when {
                                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                                    action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
                                    putExtra(Settings.EXTRA_APP_PACKAGE, activity.packageName)
                                }
                                else -> {
                                    action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                                    data = Uri.parse("package:${activity.packageName}")
                                }
                            }
                        }
                        activity.startActivity(intent)
                    } catch (e: Exception) {
                        LogX.e("$TAG,打开通知权限设置失败: ${e.message}", e)
                        onResult?.invoke(false)
                    }
                },
                onCancel = {
                    onResult?.invoke(false)
                }
            )
        } catch (e: Exception) {
            LogX.e("$TAG,请求通知权限失败: ${e.message}", e)
            onResult?.invoke(false)
        }
    }
    
    /**
     * 请求所有必需的权限
     */
    fun requestAllRequiredPermissions(activity: Activity, onResult: ((Boolean) -> Unit)? = null) {
        // 先检查悬浮窗权限
        if (!hasOverlayPermission(activity)) {
            requestOverlayPermission(activity) { overlayGranted ->
                if (overlayGranted) {
                    // 悬浮窗权限获取成功，检查通知权限
                    if (!hasNotificationPermission(activity)) {
                        requestNotificationPermission(activity) { notificationGranted ->
                            onResult?.invoke(notificationGranted)
                        }
                    } else {
                        onResult?.invoke(true)
                    }
                } else {
                    onResult?.invoke(false)
                }
            }
        } else if (!hasNotificationPermission(activity)) {
            // 悬浮窗权限已有，检查通知权限
            requestNotificationPermission(activity) { notificationGranted ->
                onResult?.invoke(notificationGranted)
            }
        } else {
            // 所有权限都已获取
            onResult?.invoke(true)
        }
    }
    
    /**
     * 显示权限说明对话框
     */
    fun showPermissionExplanationDialog(activity: Activity, onConfirm: () -> Unit = {}) {
        ComposePopup.showConfirmDialog(
            context = activity,
            title = "权限说明",
            content = "全局通知功能需要以下权限：\n\n" +
                    "• 悬浮窗权限：用于在屏幕顶部显示通知\n" +
                    "• 通知权限：用于系统通知管理\n\n" +
                    "这些权限仅用于改善您的使用体验，不会收集任何个人信息。",
            confirmText = "我知道了",
            showCancelButton = false,
            onConfirm = onConfirm
        )
    }
}
