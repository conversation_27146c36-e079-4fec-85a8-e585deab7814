package com.mobile.anchor.app.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.ContextCompat
import com.bdc.android.library.utils.AppManager
import com.luck.picture.lib.style.PictureSelectorStyle
import com.luck.picture.lib.utils.StyleUtils
import com.mobile.anchor.app.R
import com.yalantis.ucrop.UCrop
import java.io.File

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/3/11 14:45
 * @description :
 */
object UCropUtil {

    fun getIntent(context: Context, sourceUri: Uri): Intent {
        val destinationUri = Uri.fromFile(File(context.cacheDir, "cropped_image.jpg"))
        val intent = UCrop.of<Any>(sourceUri, destinationUri).withAspectRatio(1f, 1f) // 设置裁剪比例（例如 1:1）
            .withOptions(buildOptions(context)).getIntent(context)
        // 启动裁剪
        return intent
    }

    fun buildOptions(context: Context): UCrop.Options {
        val options = UCrop.Options()
        //是否显示裁剪菜单栏  ----  剪裁时  可以左右旋转，可以利用控制组件放大缩小图片
        options.setHideBottomControls(true)
        //是否显示裁剪框or图片拖动  设置false
        options.setFreeStyleCropEnabled(false)
        //是否显示裁剪边框  true
        options.setShowCropFrame(true)
        //是否显示裁剪框网格
        options.setShowCropGrid(true)
        //圆形头像剪裁模式  设置为false 一律传正方形，在展示的时候设置为圆形
        options.setCircleDimmedLayer(false)
        //剪裁比例  这里设置1:1
        options.withAspectRatio(1f, 1f)
        //创建自定义输出目录
        options.setCropOutputPathDir(getSandboxPath())
        //裁剪并自动拖动到中心
        options.isCropDragSmoothToCenter(false)
        //自定义Loader Bitmap
        options.isUseCustomLoaderBitmap(false)
        //是否不剪裁GIF 需要跳过
//         options.setSkipCropMimeType(getNotSupportCrop());
        //不剪裁GIF
        options.isForbidCropGifWebp(true)
        options.isForbidSkipMultipleCrop(false)
        //最大可剪裁数量
        options.setMaxScaleMultiplier(100f)
        val selectorStyle = PictureSelectorStyle()
        if (selectorStyle != null && selectorStyle.selectMainStyle.statusBarColor != 0) {
            val mainStyle = selectorStyle.selectMainStyle
//            val isDarkStatusBarBlack = mainStyle.isDarkStatusBarBlack
            val statusBarColor = mainStyle.statusBarColor
//            options.isDarkStatusBarBlack(isDarkStatusBarBlack)
            if (StyleUtils.checkStyleValidity(statusBarColor)) {
                options.setStatusBarColor(statusBarColor)
                options.setToolbarColor(statusBarColor)
            } else {
                options.setStatusBarColor(
                    ContextCompat.getColor(
                        context, R.color.background
                    )
                )
                options.setToolbarColor(
                    ContextCompat.getColor(
                        context, R.color.background
                    )
                )
            }
            val titleBarStyle = selectorStyle.titleBarStyle
            if (StyleUtils.checkStyleValidity(titleBarStyle.titleTextColor)) {
                options.setToolbarWidgetColor(titleBarStyle.titleTextColor)
            } else {
                options.setToolbarWidgetColor(
                    ContextCompat.getColor(
                        context, R.color.white
                    )
                )
            }
        } else {
            options.setStatusBarColor(
                ContextCompat.getColor(
                    context, R.color.background
                )
            )
            options.setToolbarColor(
                ContextCompat.getColor(
                    context, com.bdc.android.library.R.color.transparent
                )
            )
            options.setToolbarWidgetColor(
                ContextCompat.getColor(
                    context, R.color.white
                )
            )
        }
        return options
    }

    fun getSandboxPath(): String {
        val externalFilesDir: File? = AppManager.getApplication().getExternalFilesDir("")
        val customFile = File(externalFilesDir?.absolutePath, "Sandbox")
        if (!customFile.exists()) {
            customFile.mkdirs()
        }
        return customFile.absolutePath + File.separator
    }
}