package com.mobile.anchor.app.notification

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.app.Activity
import android.graphics.PixelFormat
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import com.mobile.anchor.app.R
import com.mobile.anchor.app.extension.loadAvatar
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.notification.GlobalNotificationManager.NotificationData
import com.mobile.anchor.app.utils.ContextHolder
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 全局通知弹窗视图
 * 从屏幕顶部滑入显示通知，支持点击和自动消失
 */
class GlobalNotificationView(private val activity: Activity) {
    
    companion object {
        private const val TAG = "GlobalNotificationView"
        private const val ANIMATION_DURATION = 200L // 加快动画速度
        private const val AUTO_DISMISS_DELAY = 3000L // 3秒后自动消失
        private const val SWIPE_THRESHOLD = 100f // 滑动阈值
    }
    
    private val windowManager: WindowManager = activity.getSystemService(Activity.WINDOW_SERVICE) as WindowManager
    private val notificationView: View
    private val layoutParams: WindowManager.LayoutParams
    
    // 视图组件
    private val tvTitle: TextView
    private val tvContent: TextView
    private val tvTime: TextView
    private val ivAvatar: ImageView
    private val ivClose: ImageView
    
    // 状态管理
    private var isShowing = false
    private var notificationData: NotificationData? = null
    private var onDismissListener: (() -> Unit)? = null
    
    // 动画和定时器
    private var showAnimator: ObjectAnimator? = null
    private var hideAnimator: ObjectAnimator? = null
    private val mainHandler = Handler(Looper.getMainLooper())
    private val autoDismissRunnable = Runnable { dismiss() }
    
    // 触摸相关
    private var initialY = 0f
    private var initialTouchY = 0f
    private var isDragging = false
    
    init {
        // 创建通知视图
        notificationView = LayoutInflater.from(activity).inflate(R.layout.layout_global_notification, null)
        
        // 初始化视图组件
        tvTitle = notificationView.findViewById(R.id.tv_title)
        tvContent = notificationView.findViewById(R.id.tv_content)
        tvTime = notificationView.findViewById(R.id.tv_time)
        ivAvatar = notificationView.findViewById(R.id.iv_avatar)
        ivClose = notificationView.findViewById(R.id.iv_close)
        
        // 设置窗口参数
        layoutParams = WindowManager.LayoutParams().apply {
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.WRAP_CONTENT
            type = WindowManager.LayoutParams.TYPE_APPLICATION_PANEL
            flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR
            format = PixelFormat.TRANSLUCENT
            gravity = Gravity.TOP
            y = getStatusBarHeight() // 状态栏下方
        }
        
        setupClickListeners()
        setupTouchListener()
    }
    
    /**
     * 设置通知数据
     */
    fun setNotificationData(data: NotificationData) {
        this.notificationData = data
        updateUI(data)
    }
    
    /**
     * 更新UI显示
     */
    private fun updateUI(data: NotificationData) {
        tvTitle.text = data.title
        tvContent.text = data.content
        
        // 设置时间
        tvTime.text = formatTime(data.timestamp)
        
        // 设置头像（这里可以后续集成图片加载库）
        // 暂时使用默认头像
        ivAvatar.loadAvatar(data.senderAvatar ?: "")
        
        LogX.d(TAG, "通知UI更新完成: ${data.title}")
    }
    
    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 整个通知区域点击
        notificationView.setOnClickListener {
            LogX.d(TAG, "通知被点击")
            notificationData?.onClick?.invoke()
            dismiss()
        }
        
        // 关闭按钮点击
        ivClose.setOnClickListener {
            LogX.d(TAG, "关闭按钮被点击")
            dismiss()
        }
    }
    
    /**
     * 设置触摸监听器，支持上滑关闭
     */
    private fun setupTouchListener() {
        notificationView.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialY = notificationView.translationY
                    initialTouchY = event.rawY
                    isDragging = false
                    // 暂停自动消失
                    mainHandler.removeCallbacks(autoDismissRunnable)
                    true
                }
                
                MotionEvent.ACTION_MOVE -> {
                    val deltaY = event.rawY - initialTouchY
                    if (Math.abs(deltaY) > 10) {
                        isDragging = true
                        // 只允许向上滑动
                        if (deltaY < 0) {
                            notificationView.translationY = initialY + deltaY
                        }
                    }
                    true
                }
                
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    val deltaY = event.rawY - initialTouchY
                    
                    if (isDragging) {
                        if (deltaY < -SWIPE_THRESHOLD) {
                            // 向上滑动超过阈值，关闭通知
                            dismiss()
                        } else {
                            // 回弹到原位置
                            ObjectAnimator.ofFloat(notificationView, "translationY", notificationView.translationY, initialY)
                                .setDuration(200)
                                .start()
                            // 重新开始自动消失计时
                            startAutoDismissTimer()
                        }
                    } else {
                        // 不是拖拽，当作点击处理
                        notificationView.performClick()
                    }
                    
                    isDragging = false
                    true
                }
                
                else -> false
            }
        }
    }
    
    /**
     * 显示通知
     */
    fun show() {
        if (isShowing) {
            LogX.w(TAG, "通知已在显示中")
            return
        }
        
        try {
            // 设置初始位置（在屏幕上方）
            notificationView.translationY = -notificationView.height.toFloat() - 200f
            
            // 添加到窗口
            windowManager.addView(notificationView, layoutParams)
            isShowing = true
            
            // 等待视图测量完成后开始动画
            notificationView.post {
                startShowAnimation()
            }
            
            LogX.d(TAG, "通知开始显示")
            
        } catch (e: Exception) {
            LogX.e("TAG, 显示通知失败: ${e.message}", e)
            isShowing = false
        }
    }
    
    /**
     * 开始显示动画
     */
    private fun startShowAnimation() {
        showAnimator = ObjectAnimator.ofFloat(notificationView, "translationY", notificationView.translationY, 0f).apply {
            duration = ANIMATION_DURATION
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // 动画结束后开始自动消失计时
                    startAutoDismissTimer()
                }
            })
            start()
        }
    }
    
    /**
     * 开始自动消失计时
     */
    private fun startAutoDismissTimer() {
        mainHandler.removeCallbacks(autoDismissRunnable)
        mainHandler.postDelayed(autoDismissRunnable, AUTO_DISMISS_DELAY)
    }
    
    /**
     * 隐藏通知
     */
    fun dismiss() {
        if (!isShowing) {
            return
        }
        
        // 取消自动消失计时
        mainHandler.removeCallbacks(autoDismissRunnable)
        
        // 取消显示动画
        showAnimator?.cancel()
        
        // 开始隐藏动画
        val targetY = -notificationView.height.toFloat() - 100f
        hideAnimator = ObjectAnimator.ofFloat(notificationView, "translationY", notificationView.translationY, targetY).apply {
            duration = ANIMATION_DURATION
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    removeFromWindow()
                }
            })
            start()
        }
        
        LogX.d(TAG, "通知开始隐藏")
    }
    
    /**
     * 从窗口移除视图
     */
    private fun removeFromWindow() {
        try {
            if (isShowing && notificationView.isAttachedToWindow) {
                windowManager.removeView(notificationView)
            }
        } catch (e: Exception) {
            LogX.e("TAG, 移除通知视图失败: ${e.message}", e)
        } finally {
            isShowing = false
            onDismissListener?.invoke()
            LogX.d(TAG, "通知已移除")
        }
    }
    
    /**
     * 设置消失监听器
     */
    fun setOnDismissListener(listener: () -> Unit) {
        this.onDismissListener = listener
    }
    
    /**
     * 是否正在显示
     */
    fun isShowing(): Boolean = isShowing
    
    /**
     * 获取状态栏高度
     */
    private fun getStatusBarHeight(): Int {
        val resourceId = activity.resources.getIdentifier("status_bar_height", "dimen", "android")
        return if (resourceId > 0) {
            activity.resources.getDimensionPixelSize(resourceId)
        } else {
            0
        }
    }
    
    /**
     * 格式化时间显示
     */
    private fun formatTime(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 60 * 1000 -> ContextHolder.context.getString(R.string.just_now)
            diff < 60 * 60 * 1000 -> ContextHolder.context.getString(R.string.minite_ago, diff / (60 * 1000))
            diff < 24 * 60 * 60 * 1000 -> ContextHolder.context.getString(R.string.hour_ago, diff / (60 * 60 * 1000))
            else -> {
                val sdf = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
                sdf.format(Date(timestamp))
            }
        }
    }
}
