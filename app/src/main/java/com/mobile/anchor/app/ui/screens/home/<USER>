package com.mobile.anchor.app.ui.screens.home

import androidx.lifecycle.viewModelScope
import com.mobile.anchor.app.data.model.DashboardBean
import com.mobile.anchor.app.data.model.HomeTaskBean
import com.mobile.anchor.app.data.model.WorkbenchUnionBean
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.BodyParams
import com.mobile.anchor.app.data.service.HomeApiService
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.viewmodels.BaseViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * 首页 ViewModel
 */
class HomeViewModel : BaseViewModel() {

    private val homeApiService: HomeApiService by NetDelegates()

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing.asStateFlow()

    private var refreshJob: Job? = null

    companion object {
        private const val REFRESH_INTERVAL = 60_000L // 1分钟的间隔
    }

    init {
        startPeriodicRefresh()
    }

    /**
     * 加载所有数据
     */
    fun loadAllData(enableRefresh: Boolean = true) {
        viewModelScope.launch {
            _isRefreshing.value = enableRefresh
            loadHomeTask()
            _isRefreshing.value = false
        }
        // 工作台数据使用ktHttpRequest单独加载
        loadWorkbenchData()
    }

    /**
     * 加载工作台数据
     */
    private fun loadWorkbenchData() {
        ktHttpRequest {
            val response = homeApiService.getDashboard().await()
            response?.let { data ->
                _uiState.value = _uiState.value.copy(workbenchBean = data)
                LogX.d("工作台数据加载成功")
            }
        }
    }

    /**
     * 加载任务数据
     */
    private suspend fun loadHomeTask() {
        // 模拟网络请求
        delay(500)
        val data = HomeTaskBean(
            todayValidMatchTimes = "5",
            matchBonus = "100",
            currentMatchTimes = "3",
            matchSettleValue = "10"
        )
        _uiState.value = _uiState.value.copy(homeTaskBean = data)
        LogX.d("任务数据加载成功")
    }

    /**
     * 更新在线状态
     */
    fun updateWorkModeStatus(open: Boolean) {
        LogX.d("开始更新在线状态: $open (isOpen: $open)")

        ktHttpRequest {
            try {
                val response =
                    homeApiService.updateWorkMode(BodyParams.updateWorkingModeBody(open)).await()

                LogX.d("在线状态更新成功: $open ${response}")
                response?.let { data ->
                    LogX.d("在线状态更新成功: $open")
                    // API调用成功，更新本地状态
                    _uiState.value = _uiState.value.copy(
                        workbenchBean = _uiState.value.workbenchBean.copy(working_mod = open)
                    )
                    LogX.d("在线状态更新成功: $open")

                    // 如果在线状态关闭，则匹配开关也关闭
                    if (!open) {
                        updateMatchSwitch("0")
                    }
                }
            } catch (e: Exception) {
                LogX.e("更新在线状态失败", e)
                // 可以在这里添加错误处理，比如显示Toast
            }
        }
    }

    /**
     * 自动开启工作模式（冷启动时调用）
     */
    fun autoEnableWorkMode() {
        LogX.d("冷启动自动开启工作模式")
        updateWorkModeStatus(open = true)
    }

    /**
     * 更新匹配开关状态
     */
    fun updateMatchSwitch(status: String) {
        viewModelScope.launch {
            // 模拟网络请求
            delay(300)
            _uiState.value = _uiState.value.copy(
                workbenchBean = _uiState.value.workbenchBean.copy(matchStatus = status)
            )
            LogX.d("匹配开关更新为: $status")
        }
    }

    private fun startPeriodicRefresh() {
        refreshJob?.cancel() // 取消之前的任务
        refreshJob = viewModelScope.launch {
            while (isActive) {
                if (DataStoreManager.isUserLoggedIn() && DataStoreManager.getUserObject() != null) {
                    loadAllData(enableRefresh = false) // 加载数据
                    delay(REFRESH_INTERVAL) // 延时1分钟
                } else {
                    LogX.d("用户未登录或用户对象为空，停止定时刷新")
                    stopPeriodicRefresh() // 停止刷新任务
                    break // 退出循环
                }
            }
        }
    }

    private fun stopPeriodicRefresh() {
        refreshJob?.cancel()
        refreshJob = null
    }

    override fun onCleared() {
        super.onCleared()
        stopPeriodicRefresh()
    }


}

/**
 * 首页 UI 状态
 */
data class HomeUiState(
    val workbenchBean: DashboardBean = DashboardBean(),
    val workbenchUnionBean: WorkbenchUnionBean = WorkbenchUnionBean(),
    val homeTaskBean: HomeTaskBean = HomeTaskBean(),
)
