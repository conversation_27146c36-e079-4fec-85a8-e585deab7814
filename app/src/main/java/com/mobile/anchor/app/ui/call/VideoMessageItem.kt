package com.mobile.anchor.app.ui.call

import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.core.content.ContextCompat
import coil.load
import com.angcyo.dsladapter.DslAdapterItem
import com.angcyo.dsladapter.DslViewHolder
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.VideoMessageBean

/**
 * 作者：Lxf
 * 创建日期：2024/8/2 16:30
 * 描述：
 */
class VideoMessageItem : DslAdapterItem() {
    init {
        itemLayoutId = R.layout.item_video_message
    }

    var videoMessageBean: VideoMessageBean? = null

    //重写`onItemBind`方法, 实现界面的绑定
    override fun onItemBind(
        itemHolder: DslViewHolder,
        itemPosition: Int,
        adapterItem: DslAdapterItem,
        payloads: List<Any>
    ) {
        super.onItemBind(itemHolder, itemPosition, adapterItem, payloads)
        itemHolder.apply {
            videoMessageBean?.apply {
                val foregroundColor = if (name == context.getString(R.string.label_me)) {
                    ContextCompat.getColor(context, R.color.color_B452FF)
                } else {
                    ContextCompat.getColor(context, R.color.color_EC12E2)
                }
                if (giftImage.isNotEmpty()) {
                    val showMsg =
                        if (isAsk) context.getString(R.string.ask_for_gift) else context.getString(R.string.send_to_you)
                    val spanStr = SpannableString("$name:  $showMsg")
                    spanStr.setSpan(
                        ForegroundColorSpan(foregroundColor),
                        0,
                        name.length,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )

                    tv(R.id.tv_label)?.text = spanStr

                    img(R.id.gift_img)?.apply {
                        visibility = View.VISIBLE
                        load(giftImage)
                    }
                    tv(R.id.gift_count)?.apply {
                        visibility = View.VISIBLE
                        text = "X$giftCount"
                    }
                } else {
                    val spanStr = SpannableString("$name:  $message")
                    spanStr.setSpan(
                        ForegroundColorSpan(foregroundColor),
                        0,
                        name.length,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    tv(R.id.tv_label)?.text = spanStr
                    tv(R.id.gift_count)?.visibility = View.GONE
                    img(R.id.gift_img)?.visibility = View.GONE
                }
            }
        }

    }
}