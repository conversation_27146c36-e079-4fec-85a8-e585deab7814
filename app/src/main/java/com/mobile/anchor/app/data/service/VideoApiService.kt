package com.mobile.anchor.app.data.service

import com.bdc.android.library.ktnet.coroutines.Await
import com.mobile.anchor.app.data.model.GiftItemBean
import com.mobile.anchor.app.data.model.RecordTimeBean
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * 视频相关API服务
 */
interface VideoApiService {

    /**
     * 获取礼物列表
     */
    @POST("/api/v1/anchor/screen_shot/upload")
    suspend fun screenShotUpload(@Body body: RequestBody): Await<Any?>

    @POST("/api/v1/anchor/video_config/upload")
    suspend fun videoConfigUpload(@Body body: RequestBody): Await<Any?>

    @GET("/api/v1/anchor/video_config/detail")
    suspend fun videoConfigDetail(): Await<PageResponse<RecordTimeBean>?>


}