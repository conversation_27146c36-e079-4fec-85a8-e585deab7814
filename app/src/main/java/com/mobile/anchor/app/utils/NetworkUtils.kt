package com.mobile.anchor.app.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.distinctUntilChanged

/**
 * 网络工具类
 * 提供网络状态检测和监听功能
 */
class NetworkUtils private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: NetworkUtils? = null
        
        fun getInstance(context: Context): NetworkUtils {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NetworkUtils(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    /**
     * 网络连接类型枚举
     */
    enum class NetworkType {
        WIFI,           // WiFi 连接
        CELLULAR,       // 移动网络
        ETHERNET,       // 以太网
        NONE            // 无网络连接
    }
    
    /**
     * 网络状态数据类
     */
    data class NetworkState(
        val isConnected: Boolean,
        val networkType: NetworkType
    )
    
    /**
     * 检查是否有网络连接
     */
    fun isNetworkAvailable(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                    capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.isConnected == true
        }
    }
    
    /**
     * 获取当前网络类型
     */
    fun getCurrentNetworkType(): NetworkType {
        if (!isNetworkAvailable()) return NetworkType.NONE
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return NetworkType.NONE
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return NetworkType.NONE
            
            when {
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkType.CELLULAR
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkType.ETHERNET
                else -> NetworkType.NONE
            }
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            when (networkInfo?.type) {
                ConnectivityManager.TYPE_WIFI -> NetworkType.WIFI
                ConnectivityManager.TYPE_MOBILE -> NetworkType.CELLULAR
                ConnectivityManager.TYPE_ETHERNET -> NetworkType.ETHERNET
                else -> NetworkType.NONE
            }
        }
    }
    
    /**
     * 检查是否为 WiFi 连接
     */
    fun isWifiConnected(): Boolean {
        return getCurrentNetworkType() == NetworkType.WIFI
    }
    
    /**
     * 检查是否为移动网络连接
     */
    fun isCellularConnected(): Boolean {
        return getCurrentNetworkType() == NetworkType.CELLULAR
    }
    
    /**
     * 获取网络状态
     */
    fun getNetworkState(): NetworkState {
        val isConnected = isNetworkAvailable()
        val networkType = if (isConnected) getCurrentNetworkType() else NetworkType.NONE
        return NetworkState(isConnected, networkType)
    }
    
    /**
     * 监听网络状态变化（Flow）
     */
    fun observeNetworkState(): Flow<NetworkState> = callbackFlow {
        val callback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                trySend(getNetworkState())
            }
            
            override fun onLost(network: Network) {
                super.onLost(network)
                trySend(NetworkState(false, NetworkType.NONE))
            }
            
            override fun onCapabilitiesChanged(
                network: Network,
                networkCapabilities: NetworkCapabilities
            ) {
                super.onCapabilitiesChanged(network, networkCapabilities)
                trySend(getNetworkState())
            }
        }
        
        val request = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
        
        connectivityManager.registerNetworkCallback(request, callback)
        
        // 发送当前状态
        trySend(getNetworkState())
        
        awaitClose {
            connectivityManager.unregisterNetworkCallback(callback)
        }
    }.distinctUntilChanged()
    
    /**
     * 获取网络类型的友好显示名称
     */
    fun getNetworkTypeName(networkType: NetworkType): String {
        return when (networkType) {
            NetworkType.WIFI -> "WiFi"
            NetworkType.CELLULAR -> "移动网络"
            NetworkType.ETHERNET -> "以太网"
            NetworkType.NONE -> "无网络"
        }
    }
    
    /**
     * 检查是否为计费网络（移动网络）
     */
    fun isMeteredNetwork(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            connectivityManager.isActiveNetworkMetered
        } else {
            isCellularConnected()
        }
    }
    
    /**
     * 获取网络连接信息的详细描述
     */
    fun getNetworkInfo(): String {
        val state = getNetworkState()
        return if (state.isConnected) {
            val typeName = getNetworkTypeName(state.networkType)
            val isMetered = if (isMeteredNetwork()) "（计费网络）" else ""
            "$typeName$isMetered"
        } else {
            "无网络连接"
        }
    }
}
