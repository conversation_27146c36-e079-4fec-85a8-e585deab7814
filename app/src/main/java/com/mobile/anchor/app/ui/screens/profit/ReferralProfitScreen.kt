package com.mobile.anchor.app.ui.screens.profit

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.ReferralProfitBean
import com.mobile.anchor.app.extension.formatDateTime
import com.mobile.anchor.app.extension.toShowDiamond
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.CircleAvatar
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.viewmodels.WalletViewModel
import com.mobile.anchor.app.utils.Constants
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/8/12 10:20
 * @description :
 */
@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ReferralProfitScreen() {
    val context = LocalContext.current
    val viewModel = viewModel<WalletViewModel>()
    val referralProfitList = viewModel.referralProfitList.collectAsState()
    val isRefreshing = viewModel.isRefreshing.collectAsState()
    val isLoadingMore by viewModel.isLoadingMore.collectAsState()
    val listState = rememberLazyListState()
    val pullRefreshState = rememberPullRefreshState(isRefreshing.value, {
        viewModel.getReferralProfitList()
    })

    LaunchedEffect(Unit) {
        viewModel.getReferralProfitList(true)
    }

    LaunchedEffect(listState, referralProfitList) {
        snapshotFlow { listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }.distinctUntilChanged()
            .collectLatest { index ->
                if (index != null && index >= referralProfitList.value.size - 3 && !isLoadingMore && referralProfitList.value.size >= Constants.GLOBAL_PAGE_SIZE && referralProfitList.value.size % Constants.GLOBAL_PAGE_SIZE == 0) {
                    viewModel.getReferralProfitList(false)
                }
            }
    }

    AnchorScaffold(
        topBar = {
            AnchorTopBar(context.getString(R.string.recharge_detail))
        }) { paddingValues ->
        Box(
            modifier = Modifier
                .pullRefresh(pullRefreshState)
                .padding(paddingValues)
        ) {
            LazyColumn(
                state = listState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)

            ) {
                items(referralProfitList.value) { profit ->
                    ReferralProfitItem(profit)
                }
            }

            // 下拉刷新指示器
            PullRefreshIndicator(
                refreshing = isRefreshing.value,
                state = pullRefreshState,
                modifier = Modifier.align(Alignment.TopCenter)
            )
        }
    }
}

@Composable
private fun ReferralProfitItem(profitBean: ReferralProfitBean?, modifier: Modifier = Modifier) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2A2B35)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 收入类型图标

            CircleAvatar(profitBean?.avatar, modifier = Modifier.size(48.dp))

            Spacer(modifier = Modifier.width(12.dp))

            // 收入信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = profitBean?.nickname ?: "",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = profitBean?.createdAt?.formatDateTime() ?: "",
                    color = Color.White.copy(alpha = 0.5f),
                    fontSize = 12.sp
                )
            }
            Row(
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 收入金额
                Text(
                    text = "+${profitBean?.profit?.toShowDiamond()}",
                    color = Primary,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
                Image(
                    painter = painterResource(R.mipmap.ic_coin),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(start = 5.dp)
                        .size(15.dp)
                )
            }
        }
    }
}

@Preview
@Composable
fun ReferralProfitItemPreview() {
    val profitBean = ReferralProfitBean(
        id = 1,
        anchorId = 1,
        userId = 1,
        profit = 100,
        avatar = "https://example.com/avatar.png",
        nickname = "John Doe",
        createdAt = System.currentTimeMillis()
    )
    ReferralProfitItem(profitBean = profitBean)
}
