package com.mobile.anchor.app.popup

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import coil.load
import com.angcyo.dsladapter.visible
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.google.android.material.tabs.TabLayout
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.BottomPopupView
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.GiftItemBean
import com.mobile.anchor.app.databinding.PopupGiftBinding
import com.mobile.anchor.app.extension.dp
import com.mobile.anchor.app.extension.findLifecycleOwner
import com.mobile.anchor.app.extension.getViewModelOwner
import com.mobile.anchor.app.extension.toShowDiamond
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.viewmodels.GiftRequestEvent
import com.mobile.anchor.app.ui.viewmodels.GiftViewModel
import kotlinx.coroutines.launch
import kotlin.math.ceil

@SuppressLint("ViewConstructor")
class GiftPopup(
    val activity: ComponentActivity,
    private val userId: String,
    private val block: (GiftItemBean) -> Unit = {}
) : BottomPopupView(activity) {
    private var giftViewModel: GiftViewModel? = null
    private var items = mutableListOf<GiftItemBean>()
    private val columnCount = 4
    private val pageSize = 8
    private var selGift: GiftItemBean? = null

    private lateinit var binding: PopupGiftBinding

    override fun init() {
        super.init()
    }

    override fun getImplLayoutId(): Int = R.layout.popup_gift

    @SuppressLint("NotifyDataSetChanged", "SetTextI18n")
    override fun initPopupContent() {
        super.initPopupContent()
        if (giftViewModel == null) {
            activity.getViewModelOwner()?.let {
                giftViewModel = ViewModelProvider(it)[GiftViewModel::class.java]
            }
            activity.findLifecycleOwner()?.let {
                giftViewModel?.pageEvents?.observeEvent(it) { event ->
                    when (event) {
                        is GiftRequestEvent.FetchGiftListFailed -> {
                            LogX.i("fetchGiftListFailed ${event.msg}")
                            ToastUtil.show(event.msg)
                            binding.progressBar.visible(false)
                        }

                        is GiftRequestEvent.FetchGiftListSuc -> {
                            binding.progressBar.visible(false)
                            event.gifts.let {
                                items.addAll(it)
                                binding.viewPager.adapter = GiftPagerAdapter(items.toList())
                                initialIndicator(items, binding)
                            }
                        }

                        else -> {}
                    }
                }
            }
        }

        binding = PopupGiftBinding.bind(popupContentView)
        fetchGift(userId)
        binding.viewPager.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                binding.tabIndicator.getTabAt(position)?.select()
            }
        })

        binding.tabIndicator.apply {
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    tab?.customView?.apply {
                        background = ContextCompat.getDrawable(
                            this.context, R.drawable.shape_indicator_white
                        )
                    }
                    binding.viewPager.setCurrentItem(tab?.position ?: 0, true)
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {
                    tab?.customView?.apply {
                        background = ContextCompat.getDrawable(
                            this.context, R.drawable.shape_indicator_gray
                        )
                    }
                }

                override fun onTabReselected(tab: TabLayout.Tab?) {
                }
            })
        }

        binding.btnRecharge.setOnClickListener {
            if (selGift == null) {
                ToastUtil.show(activity.getString(R.string.tip_sel_one_gift))
            } else {
                selGift?.let {
                    block.invoke(it)
                }
                dismiss()
            }
        }
    }

    private fun fetchGift(userId: String) {
        binding.progressBar.apply {
            visibility = VISIBLE
            isIndeterminate = true
        }
        lifecycleScope.launch {
            giftViewModel?.fetchGiftList(userId)
        }
    }

    private fun initialIndicator(items: List<GiftItemBean>, binding: PopupGiftBinding) {
        val page = ceil(items.size / pageSize.toDouble()).toInt()
        binding.tabIndicator.removeAllTabs()
        for (i in 0 until page) {
            binding.tabIndicator.apply {
                addTab(binding.tabIndicator.newTab().apply {
                    customView = View(context).apply {
                        layoutParams = ViewGroup.LayoutParams(5.dp, 5.dp)
                        background = ContextCompat.getDrawable(
                            this.context, R.drawable.shape_indicator_gray
                        )
                    }
                }, i == 0)
            }
        }
    }

    inner class GiftPagerAdapter(private val items: List<GiftItemBean>) :
        RecyclerView.Adapter<GiftPagerAdapter.ViewHolder>() {

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

        override fun onCreateViewHolder(
            parent: ViewGroup, viewType: Int
        ): GiftPagerAdapter.ViewHolder {
            val view =
                LayoutInflater.from(parent.context).inflate(R.layout.item_gift_pager, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            holder.itemView.findViewById<RecyclerView>(R.id.recyclerView)?.apply {
                layoutManager = GridLayoutManager(this.context, columnCount)
                val fromIndex = position * pageSize
                val toIndex = fromIndex + pageSize
                adapter = GiftAdapter(
                    items.subList(
                        fromIndex, if (items.size > toIndex) toIndex else items.size
                    )
                ) {
                    selGift = it
                    notifyDataSetChanged()

                }
            }
        }

        override fun getItemCount(): Int {
            return ceil(items.size / pageSize.toDouble()).toInt()
        }
    }

    inner class GiftAdapter(
        private val items: List<GiftItemBean>, private val onClick: (GiftItemBean) -> Unit
    ) : RecyclerView.Adapter<GiftAdapter.ViewHolder>() {
        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_gift_pager_child, parent, false)
            return ViewHolder(view)
        }

        override fun getItemCount(): Int {
            return items.size
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val item = items[position]
            holder.itemView.findViewById<ImageView>(R.id.iv_gift)?.load(item.icon)
            holder.itemView.findViewById<TextView>(R.id.tv_gift_name).text = item.showName
            holder.itemView.findViewById<TextView>(R.id.tv_gift_value).text =
                item.diamond.toShowDiamond().toString()

            holder.itemView.apply {
                background = if (item.checked) {
                    ContextCompat.getDrawable(
                        this.context, R.drawable.shape_gift_checked
                    )
                } else {
                    null
                }
                setOnClickListener {
                    <EMAIL> { it.checked }.forEach { it.checked = false }
                    item.checked = true
//                    GiftManager.getInstance(activity)
//                        .playGiftAnimation(activity = activity, item.id)
                    onClick.invoke(item)
                }
            }
        }
    }
}

fun showGiftPopup(
    activity: ComponentActivity,
    userId: String,
    block: (GiftItemBean) -> Unit = {}
): BasePopupView? {
    return XPopup.Builder(activity)
        .asCustom(GiftPopup(activity, userId, block)).show()
}