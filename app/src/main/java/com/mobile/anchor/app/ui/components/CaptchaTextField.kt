package com.mobile.anchor.app.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mobile.anchor.app.ui.theme.AnchorTheme

/**
 * 验证码输入类型
 */
enum class CaptchaType {
    NUMBER,    // 纯数字
    LETTER,    // 纯字母
    MIXED      // 数字和字母混合
}

/**
 * 验证码输入组件
 *
 * @param length 验证码位数，默认6位
 * @param captchaType 验证码类型，默认数字
 * @param value 当前输入的验证码值
 * @param onValueChange 值变化回调
 * @param onComplete 输入完成回调（当输入满指定位数时触发）
 * @param onShake 震动触发器，当值变化时会触发震动效果
 * @param modifier 修饰符
 */
@Composable
fun CaptchaTextField(
    length: Int = 6,
    captchaType: CaptchaType = CaptchaType.NUMBER,
    value: String = "",
    onValueChange: (String) -> Unit = {},
    onComplete: (String) -> Unit = {},
    onShake: Boolean = false,
    modifier: Modifier = Modifier
) {
    var currentValue by remember(value) { mutableStateOf(value) }
    var focusedIndex by remember { mutableStateOf(-1) }
    var previousValue by remember { mutableStateOf("") }
    var shakeOffset by remember { mutableStateOf(0f) }
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current

    // 动画：震动效果的偏移量
    val animatedShakeOffset by animateFloatAsState(
        targetValue = shakeOffset,
        animationSpec = spring(
            dampingRatio = 0.4f,
            stiffness = 1200f
        ),
        finishedListener = {
            // 动画完成后重置偏移量
            if (shakeOffset != 0f) {
                shakeOffset = 0f
            }
        },
        label = "shakeOffset"
    )

    // 当输入完成时触发回调（不再自动震动）
    LaunchedEffect(currentValue) {
        if (currentValue.length == length) {
            onComplete(currentValue)
        }
    }

    // 外部触发震动效果（比如验证失败时）
    LaunchedEffect(onShake) {
        if (onShake) {
            // 错误时更强的震动序列
            shakeOffset = 10f
            kotlinx.coroutines.delay(80)
            shakeOffset = -10f
            kotlinx.coroutines.delay(80)
            shakeOffset = 8f
            kotlinx.coroutines.delay(80)
            shakeOffset = -8f
            kotlinx.coroutines.delay(80)
            shakeOffset = 4f
        }
    }

    // 验证输入字符是否符合类型要求
    fun isValidChar(char: Char): Boolean {
        return when (captchaType) {
            CaptchaType.NUMBER -> char.isDigit()
            CaptchaType.LETTER -> char.isLetter()
            CaptchaType.MIXED -> char.isLetterOrDigit()
        }
    }

    // 获取键盘类型
    val keyboardType = when (captchaType) {
        CaptchaType.NUMBER -> KeyboardType.Number
        CaptchaType.LETTER -> KeyboardType.Text
        CaptchaType.MIXED -> KeyboardType.Text
    }

    // 计算高亮索引的逻辑
    fun calculateFocusedIndex(currentVal: String, previousVal: String): Int {
        return when {
            // 如果没有获得焦点，返回-1
            focusedIndex == -1 -> -1
            // 如果是输入操作（当前值长度大于之前值）
            currentVal.length > previousVal.length -> {
                // 输入时：高亮下一个要输入的位置
                // 例如：输入第1个字符后，高亮第2个框（索引1）
                minOf(currentVal.length, length - 1)
            }
            // 如果是删除操作（当前值长度小于之前值）
            currentVal.length < previousVal.length -> {
                // 删除时：高亮当前最后一个字符的位置
                // 这样实现您要求的删除逻辑：
                // 1. 第一次删除：清空内容但保持在当前位置高亮
                // 2. 如果继续删除：会移动到前一个位置
                maxOf(currentVal.length, 0)
            }
            // 如果长度相同但内容不同（替换操作）
            currentVal != previousVal -> {
                // 找到第一个不同的位置，高亮下一个位置
                val diffIndex = currentVal.zip(previousVal).indexOfFirst { it.first != it.second }
                if (diffIndex != -1) minOf(diffIndex + 1, length - 1) else currentVal.length
            }
            // 其他情况保持当前高亮
            else -> focusedIndex
        }
    }

    Box(
        modifier = modifier
            .offset(x = animatedShakeOffset.dp) // 使用 offset 实现震动效果
    ) {
        // 透明的输入框，覆盖整个区域用于接收键盘输入
        BasicTextField(
            value = currentValue,
            onValueChange = { newValue ->
                // 过滤无效字符并限制长度
                val filteredValue = newValue.filter { isValidChar(it) }.take(length)

                // 更新高亮索引
                focusedIndex = calculateFocusedIndex(filteredValue, currentValue)

                // 更新值
                previousValue = currentValue
                currentValue = filteredValue
                onValueChange(filteredValue)
            },
            keyboardOptions = KeyboardOptions(
                keyboardType = keyboardType, imeAction = ImeAction.Done
            ),
            keyboardActions = KeyboardActions(onDone = {
                keyboardController?.hide()
            }),
            textStyle = TextStyle(color = Color.Transparent),
            cursorBrush = SolidColor(Color.Transparent),
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .focusRequester(focusRequester)
                .onFocusChanged { focusState ->
                    if (focusState.isFocused) {
                        // 获得焦点时，高亮下一个要输入的位置
                        focusedIndex = minOf(currentValue.length, length - 1)
                    } else {
                        focusedIndex = -1
                    }
                })

        val spacing = 12.dp
        val fixedBoxWidth = 48.dp

        if (length >= 6) {
            @Suppress("UnusedBoxWithConstraintsScope") BoxWithConstraints(
                modifier = Modifier.fillMaxWidth()
            ) {
                val totalSpacing = spacing * (length - 1)
                val boxWidth = (maxWidth - totalSpacing) / length
                BuildCaptchaBox(length, spacing, boxWidth, currentValue, focusedIndex)
            }
        } else {
            BuildCaptchaBox(length, spacing, fixedBoxWidth, currentValue, focusedIndex)
        }
    }

    // 延迟自动聚焦，避免 BringIntoViewRequester 错误
    LaunchedEffect(Unit) {
        kotlinx.coroutines.delay(500) // 延迟500ms确保页面稳定
        try {
            focusRequester.requestFocus()
        } catch (e: Exception) {
            // 如果聚焦失败就忽略，用户可以手动点击
            println("CaptchaTextField: 自动聚焦失败，用户可以手动点击输入")
        }
    }
}

@Composable
private fun BuildCaptchaBox(
    length: Int, spacing: Dp, boxWidth: Dp, currentValue: String, focusedIndex: Int
) {
    val focusedColor = Color(0xFF9F2AF8)
    val unFocusedColor = Color(0xFF363948)
    val backgroundColor = Color(0xFF1A1D2E)

    // 呼吸灯效果的无限动画
    val infiniteTransition = rememberInfiniteTransition(label = "breathingLight")
    val breathingAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1500),
            repeatMode = RepeatMode.Reverse
        ),
        label = "breathingAlpha"
    )

    Row(
        horizontalArrangement = Arrangement.spacedBy(spacing, Alignment.CenterHorizontally),
        modifier = Modifier.fillMaxWidth()
    ) {
        repeat(length) { index ->
            val char = currentValue.getOrNull(index)?.toString() ?: ""
            val isFocused = focusedIndex == index
            val hasValue = char.isNotEmpty()

            // 动画：边框颜色渐变，只有在高亮且无内容时才添加呼吸灯效果
            val targetBorderColor = when {
                // 当前框被高亮且无内容时，使用呼吸灯效果
                isFocused && !hasValue -> focusedColor.copy(alpha = breathingAlpha)
                // 当前框被高亮但有内容，或者有值但未被高亮
                isFocused || hasValue -> focusedColor
                // 无值且未被高亮
                else -> unFocusedColor
            }

            val animatedBorderColor by animateColorAsState(
                targetValue = targetBorderColor,
                animationSpec = tween(durationMillis = 200),
                label = "borderColor"
            )

            // 动画：缩放效果，高亮时稍微放大
            val animatedScale by animateFloatAsState(
                targetValue = if (isFocused) 1.05f else 1f,
                animationSpec = spring(
                    dampingRatio = 0.6f,
                    stiffness = 800f
                ),
                label = "scale"
            )

            // 动画：文字颜色渐变
            val animatedTextColor by animateColorAsState(
                targetValue = if (hasValue || isFocused) focusedColor else Color.Transparent,
                animationSpec = tween(durationMillis = 150),
                label = "textColor"
            )

            // 动画：文字缩放，新输入时有弹跳效果
            val textScale by animateFloatAsState(
                targetValue = if (hasValue) 1f else 0.8f,
                animationSpec = spring(
                    dampingRatio = 0.7f,
                    stiffness = 1000f
                ),
                label = "textScale"
            )

            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .width(boxWidth)
                    .aspectRatio(1f)
                    .scale(animatedScale) // 添加缩放动画
                    .clip(RoundedCornerShape(8.dp))
                    .background(backgroundColor)
                    .border(
                        width = 2.dp,
                        color = animatedBorderColor, // 使用动画颜色
                        shape = RoundedCornerShape(8.dp)
                    )
                // 移除点击处理，因为透明的 BasicTextField 会处理点击
            ) {
                Text(
                    text = char,
                    fontSize = 30.sp,
                    fontWeight = FontWeight.Medium,
                    color = animatedTextColor, // 使用动画颜色
                    textAlign = TextAlign.Center,
                    modifier = Modifier.scale(textScale) // 添加文字缩放动画
                )
            }
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF101321)
@Composable
fun CaptchaTextFieldPreview() {
    AnchorTheme {
        Box(
            modifier = Modifier
                .background(Color(0xFF101321))
                .padding(16.dp)
        ) {
            CaptchaTextField(
                length = 6,
                captchaType = CaptchaType.NUMBER,
                value = "241a",
                onValueChange = { },
                onComplete = { })
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF101321)
@Composable
fun CaptchaTextFieldLetterPreview() {
    AnchorTheme {
        Box(
            modifier = Modifier
                .background(Color(0xFF101321))
                .padding(16.dp)
        ) {
            CaptchaTextField(
                length = 4,
                captchaType = CaptchaType.LETTER,
                value = "AB",
                onValueChange = { },
                onComplete = { })
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF101321)
@Composable
fun CaptchaTextFieldInteractivePreview() {
    AnchorTheme {
        Box(
            modifier = Modifier
                .background(Color(0xFF101321))
                .padding(16.dp)
        ) {
            var value by remember { mutableStateOf("") }
            var shouldShake by remember { mutableStateOf(false) }

            CaptchaTextField(
                length = 6,
                captchaType = CaptchaType.NUMBER,
                value = value,
                onValueChange = {
                    value = it
                    shouldShake = false // 重置震动状态
                },
                onComplete = { code ->
                    // 可以在这里处理完成逻辑
                    println("验证码输入完成: $code")
                    // 模拟验证逻辑：如果是 "123456" 就触发震动（模拟验证失败）
                    if (code == "123456") {
                        shouldShake = true
                    }
                },
                onShake = shouldShake
            )
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF101321)
@Composable
fun CaptchaTextFieldAnimationPreview() {
    AnchorTheme {
        Box(
            modifier = Modifier
                .background(Color(0xFF101321))
                .padding(16.dp)
        ) {
            var value by remember { mutableStateOf("12") }

            CaptchaTextField(
                length = 6,
                captchaType = CaptchaType.NUMBER,
                value = value,
                onValueChange = { value = it },
                onComplete = {
                    println("验证码输入完成: $it")
                },
                onShake = false // 默认不震动
            )
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF101321)
@Composable
fun CaptchaTextFieldNormalPreview() {
    AnchorTheme {
        Box(
            modifier = Modifier
                .background(Color(0xFF101321))
                .padding(16.dp)
        ) {
            var value by remember { mutableStateOf("") }

            CaptchaTextField(
                length = 6,
                captchaType = CaptchaType.NUMBER,
                value = value,
                onValueChange = { value = it },
                onComplete = { code ->
                    println("验证码输入完成: $code")
                    // 正常使用场景，不需要震动
                }
                // 注意：没有传入 onShake 参数，使用默认值 false
            )
        }
    }
}
