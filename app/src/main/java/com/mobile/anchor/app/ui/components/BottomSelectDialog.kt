package com.mobile.anchor.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomSheetScaffold
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.SheetValue
import androidx.compose.material3.Text
import androidx.compose.material3.rememberBottomSheetScaffoldState
import androidx.compose.material3.rememberStandardBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.bdc.android.library.utils.Logger
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Surface
import kotlinx.coroutines.launch

/**
 * 底部选择弹窗组件
 *
 * @param visible 是否显示弹窗
 * @param title 弹窗标题
 * @param options 选项列表
 * @param onDismiss 关闭弹窗回调
 * @param onOptionSelected 选项选择回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BottomSelectDialog(
    visible: Boolean,
    title: String = "",
    options: List<String>,
    onDismiss: () -> Unit,
    onOptionSelected: (Int, String) -> Unit
) {
    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp
    // 设置最大高度为屏幕高度的50%
    val maxHeight = screenHeight * 0.5f

    val coroutineScope = rememberCoroutineScope()

    // 创建底部表单状态
    val bottomSheetState = rememberStandardBottomSheetState(
        initialValue = SheetValue.Hidden,
        skipHiddenState = false
    )

    val scaffoldState = rememberBottomSheetScaffoldState(
        bottomSheetState = bottomSheetState
    )

    // 监听 visible 状态变化
    LaunchedEffect(visible) {
        if (visible) {
            bottomSheetState.expand()
        } else {
            bottomSheetState.hide()
        }
    }

    // 监听底部表单状态变化
    LaunchedEffect(bottomSheetState.currentValue) {
        if (bottomSheetState.currentValue == SheetValue.Hidden && visible || bottomSheetState.targetValue == SheetValue.PartiallyExpanded) {
            onDismiss()
        }
    }

    if (visible) {
        BottomSheetScaffold(
            scaffoldState = scaffoldState,
            sheetContent = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = maxHeight)
                        .background(Surface)
                        .padding(horizontal = 0.dp, vertical = 16.dp)
                ) {
                    // 标题
                    if (title.isNotEmpty()) {
                        Text(
                            text = title,
                            color = Color.White,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp, vertical = 0.dp)
                                .padding(bottom = 16.dp)
                        )
                    }

                    // 选项列表
                    LazyColumn(
                        modifier = Modifier.weight(1f, fill = false)
                    ) {
                        itemsIndexed(options) { index, option ->
                            BottomSelectOption(text = option, onClick = {
                                coroutineScope.launch {
                                    bottomSheetState.hide()
                                    // 延迟调用onOptionSelected，确保动画开始后再改变状态
                                    kotlinx.coroutines.delay(50)
                                    onOptionSelected(index, option)
                                }
                            })

                            if (index < options.size - 1) {
                                HorizontalDivider(
                                    color = Color(0xFF363948),
                                    thickness = 1.dp,
                                    modifier = Modifier.padding(vertical = 8.dp)
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // 取消按钮
                    BottomSelectOption(
                        text = "Cancel", onClick = {
                            coroutineScope.launch {
                                bottomSheetState.hide()
                            }
                        }, textColor = Color(0xFF999999)
                    )

                    // 底部安全区域
                    Spacer(modifier = Modifier.height(16.dp))
                }
            },
            sheetPeekHeight = 0.dp,
            sheetShape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
            sheetContainerColor = Surface,
            sheetDragHandle = null,
            containerColor = Color.Transparent,
            modifier = Modifier.fillMaxSize()
        ) {
            // 背景遮罩
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable {
                        coroutineScope.launch {
                            bottomSheetState.hide()
                        }
                    })
        }
    }
}

/**
 * 底部选择选项
 */
@Composable
private fun BottomSelectOption(
    text: String, onClick: () -> Unit, textColor: Color = Color.White
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 16.dp), contentAlignment = Alignment.Center) {
        Text(
            text = text, color = textColor, fontSize = 16.sp, fontWeight = FontWeight.Medium
        )
    }
}

@Preview
@Composable
fun BottomSelectDialogPreview() {
    AnchorTheme {
        BottomSelectDialog(
            visible = true,
            title = "Select Option",
            options = listOf("Camera", "Gallery", "Remove Photo"),
            onDismiss = {},
            onOptionSelected = { _, _ -> })
    }
}

@Preview
@Composable
fun BottomSelectDialogLongListPreview() {
    AnchorTheme {
        BottomSelectDialog(
            visible = true, title = "Select Country", options = listOf(
                "United States",
                "China",
                "Japan",
                "Korea",
                "United Kingdom",
                "France",
                "Germany",
                "Italy",
                "Spain",
                "Russia",
                "India",
                "Brazil",
                "Canada",
                "Australia",
                "Mexico",
                "Argentina",
                "Thailand",
                "Vietnam",
                "Indonesia",
                "Malaysia",
                "Singapore",
                "Philippines",
                "Taiwan",
                "Hong Kong",
                "Netherlands"
            ), onDismiss = {}, onOptionSelected = { _, _ -> })
    }
}
