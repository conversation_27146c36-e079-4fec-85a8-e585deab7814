package com.mobile.anchor.app.manager

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.floatPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.core.stringSetPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.mobile.anchor.app.data.model.AnchorConfigBean
import com.mobile.anchor.app.data.model.IsolatedConfigBean
import com.mobile.anchor.app.data.model.RecordTimeBean
import com.mobile.anchor.app.data.model.ReviewBean
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.utils.ActivityUtils
import com.mobile.anchor.app.utils.Constants
import com.mobile.anchor.app.utils.JsonUtils
import com.mobile.anchor.app.utils.MoshiUtil
import com.mobile.anchor.app.utils.RongYunUtil
import com.mobile.anchor.app.utils.toJson
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.runBlocking
import java.util.Locale

/**
 * DataStore 管理器
 * 提供便捷的数据存储和读取方法，基于 DataStore 实现
 * 使用 ActivityUtils 获取 Context
 */
object DataStoreManager {
    private const val DATASTORE_NAME = "anchor_preferences"

    /**
     * 获取 Context
     */
    private fun getContext(): Context {
        return ActivityUtils.getApplicationContext()
    }

    // 常用的 Key 常量
    const val KEY_USER_ID = "user_id"
    const val KEY_ACCESS_TOKEN = "access_token"
    const val KEY_REFRESH_TOKEN = "refresh_token"
    const val KEY_IS_FIRST_LAUNCH = "is_first_launch"
    const val KEY_THEME_MODE = "theme_mode"
    const val KEY_LANGUAGE = "app_language"
    const val KEY_NOTIFICATION_ENABLED = "notification_enabled"
    const val KEY_GLOBAL_NOTIFICATION_ENABLED = "global_notification_enabled"
    const val KEY_RONG_YUN_APP_KEY = "rong_yun_app_key"
    const val KEY_RONG_YUN_TOKEN = "rong_yun_token"
    const val KEY_PROFILE_COMPLETED = "profile_completed"
    const val KEY_USER_OBJECT = "user_object"  // 完整用户对象
    const val KEY_ANCHOR_CONFIG = "anchor_config"  // 登录配置对象
    const val KEY_REVIEW_INFO = "review_info"
    const val KEY_CURRENCY = "currency"  // 货币设置
    const val KEY_APP_ID = "app_id"  //

    // DataStore Keys
    private val USER_ID_KEY = stringPreferencesKey(KEY_USER_ID)
    private val ACCESS_TOKEN_KEY = stringPreferencesKey(KEY_ACCESS_TOKEN)
    private val REFRESH_TOKEN_KEY = stringPreferencesKey(KEY_REFRESH_TOKEN)
    private val IS_FIRST_LAUNCH_KEY = booleanPreferencesKey(KEY_IS_FIRST_LAUNCH)
    private val THEME_MODE_KEY = stringPreferencesKey(KEY_THEME_MODE)
    private val LANGUAGE_KEY = stringPreferencesKey(KEY_LANGUAGE)
    private val NOTIFICATION_ENABLED_KEY = booleanPreferencesKey(KEY_NOTIFICATION_ENABLED)
    private val GLOBAL_NOTIFICATION_ENABLED_KEY = booleanPreferencesKey(KEY_GLOBAL_NOTIFICATION_ENABLED)

    // DataStore 实例
    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = DATASTORE_NAME)
    private val dataStore: DataStore<Preferences>
        get() = getContext().dataStore

    // JSON 序列化工具
    private val moshi = Moshi.Builder().add(KotlinJsonAdapterFactory()).build()
    private val userBeanAdapter = moshi.adapter(UserBean::class.java)
    private val authConfigAdapter = moshi.adapter(AnchorConfigBean::class.java)
    private val reviewBeanAdapter = moshi.adapter(ReviewBean::class.java)

    /**
     * 存储字符串
     */
    suspend fun putString(key: String, value: String?) {
        val prefKey = stringPreferencesKey(key)
        dataStore.edit { preferences ->
            if (value != null) {
                preferences[prefKey] = value
            } else {
                preferences.remove(prefKey)
            }
        }
    }

    /**
     * 获取字符串
     */
    suspend fun getString(key: String, defaultValue: String? = null): String? {
        val prefKey = stringPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    fun getStringSync(key: String, defaultValue: String? = null): String? {
        return runBlocking {
            val prefKey = stringPreferencesKey(key)
            dataStore.data.map { preferences ->
                preferences[prefKey] ?: defaultValue
            }.first()
        }
    }

    /**
     * 获取字符串 Flow
     */
    fun getStringFlow(key: String, defaultValue: String? = null): Flow<String?> {
        val prefKey = stringPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 存储整数
     */
    suspend fun putInt(key: String, value: Int) {
        val prefKey = intPreferencesKey(key)
        dataStore.edit { preferences ->
            preferences[prefKey] = value
        }
    }

    /**
     * 获取整数
     */
    suspend fun getInt(key: String, defaultValue: Int = 0): Int {
        val prefKey = intPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    /**
     * 获取整数 Flow
     */
    fun getIntFlow(key: String, defaultValue: Int = 0): Flow<Int> {
        val prefKey = intPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 存储长整数
     */
    suspend fun putLong(key: String, value: Long) {
        val prefKey = longPreferencesKey(key)
        dataStore.edit { preferences ->
            preferences[prefKey] = value
        }
    }

    /**
     * 获取长整数
     */
    suspend fun getLong(key: String, defaultValue: Long = 0L): Long {
        val prefKey = longPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    /**
     * 获取长整数 Flow
     */
    fun getLongFlow(key: String, defaultValue: Long = 0L): Flow<Long> {
        val prefKey = longPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 存储浮点数
     */
    suspend fun putFloat(key: String, value: Float) {
        val prefKey = floatPreferencesKey(key)
        dataStore.edit { preferences ->
            preferences[prefKey] = value
        }
    }

    /**
     * 获取浮点数
     */
    suspend fun getFloat(key: String, defaultValue: Float = 0f): Float {
        val prefKey = floatPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    /**
     * 获取浮点数 Flow
     */
    fun getFloatFlow(key: String, defaultValue: Float = 0f): Flow<Float> {
        val prefKey = floatPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 存储布尔值
     */
    suspend fun putBoolean(key: String, value: Boolean) {
        val prefKey = booleanPreferencesKey(key)
        dataStore.edit { preferences ->
            preferences[prefKey] = value
        }
    }

    /**
     * 获取布尔值
     */
    suspend fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        val prefKey = booleanPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    /**
     * 获取布尔值 Flow
     */
    fun getBooleanFlow(key: String, defaultValue: Boolean = false): Flow<Boolean> {
        val prefKey = booleanPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 存储字符串集合
     */
    suspend fun putStringSet(key: String, value: Set<String>?) {
        val prefKey = stringSetPreferencesKey(key)
        dataStore.edit { preferences ->
            if (value != null) {
                preferences[prefKey] = value
            } else {
                preferences.remove(prefKey)
            }
        }
    }

    /**
     * 获取字符串集合
     */
    suspend fun getStringSet(key: String, defaultValue: Set<String>? = null): Set<String>? {
        val prefKey = stringSetPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }.first()
    }

    /**
     * 获取字符串集合 Flow
     */
    fun getStringSetFlow(key: String, defaultValue: Set<String>? = null): Flow<Set<String>?> {
        val prefKey = stringSetPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences[prefKey] ?: defaultValue
        }
    }

    /**
     * 移除指定键的数据
     */
    suspend fun remove(key: String) {
        val stringKey = stringPreferencesKey(key)
        val intKey = intPreferencesKey(key)
        val longKey = longPreferencesKey(key)
        val floatKey = floatPreferencesKey(key)
        val booleanKey = booleanPreferencesKey(key)
        val stringSetKey = stringSetPreferencesKey(key)

        dataStore.edit { preferences ->
            preferences.remove(stringKey)
            preferences.remove(intKey)
            preferences.remove(longKey)
            preferences.remove(floatKey)
            preferences.remove(booleanKey)
            preferences.remove(stringSetKey)
        }
    }

    /**
     * 清除所有数据
     */
    suspend fun clear() {
        dataStore.edit { preferences ->
            preferences.clear()
        }
    }

    /**
     * 检查是否包含指定键
     */
    suspend fun contains(key: String): Boolean {
        val stringKey = stringPreferencesKey(key)
        return dataStore.data.map { preferences ->
            preferences.contains(stringKey)
        }.first()
    }

    /**
     * 获取所有键值对的 Flow
     */
    fun getAllFlow(): Flow<Map<String, Any?>> {
        return dataStore.data.map { preferences ->
            preferences.asMap().mapKeys { it.key.name }
        }
    }

    /**
     * 获取用户 ID
     */
    fun getUserId(): String? = runBlocking { getString(KEY_USER_ID) }

    /**
     * 获取用户 ID（同步方式，用于兼容）
     */
    fun getUserIdSync(): String? = runBlocking { getUserId() }

    /**
     * 获取用户 ID Flow
     */
    fun getUserIdFlow(): Flow<String?> = getStringFlow(KEY_USER_ID)

    /**
     * 保存访问令牌
     */
    suspend fun saveTokens(accessToken: String, refreshToken: String? = null) {
        dataStore.edit { preferences ->
            preferences[ACCESS_TOKEN_KEY] = accessToken
            refreshToken?.let { preferences[REFRESH_TOKEN_KEY] = it }
        }
    }

    /**
     * 获取访问令牌
     */
    suspend fun getAccessToken(): String? = getString(KEY_ACCESS_TOKEN)

    /**
     * 获取访问令牌（同步方式，用于兼容）
     */
    fun getAccessTokenSync(): String? = runBlocking { getAccessToken() }

    /**
     * 获取访问令牌 Flow
     */
    fun getAccessTokenFlow(): Flow<String?> = getStringFlow(KEY_ACCESS_TOKEN)

    /**
     * 获取刷新令牌
     */
    suspend fun getRefreshToken(): String? = getString(KEY_REFRESH_TOKEN)

    /**
     * 获取刷新令牌（同步方式，用于兼容）
     */
    fun getRefreshTokenSync(): String? = runBlocking { getRefreshToken() }

    /**
     * 获取刷新令牌 Flow
     */
    fun getRefreshTokenFlow(): Flow<String?> = getStringFlow(KEY_REFRESH_TOKEN)

    /**
     * 清除用户数据
     */
    suspend fun clearUserData() {
        dataStore.edit { preferences ->
            preferences.remove(USER_ID_KEY)
            preferences.remove(ACCESS_TOKEN_KEY)
            preferences.remove(REFRESH_TOKEN_KEY)
        }
    }

    /**
     * 是否首次启动
     */
    suspend fun isFirstLaunch(): Boolean = getBoolean(KEY_IS_FIRST_LAUNCH, true)

    /**
     * 是否首次启动（同步方式，用于兼容）
     */
    fun isFirstLaunchSync(): Boolean = runBlocking { isFirstLaunch() }

    /**
     * 是否首次启动 Flow
     */
    fun isFirstLaunchFlow(): Flow<Boolean> = getBooleanFlow(KEY_IS_FIRST_LAUNCH, true)

    /**
     * 设置已启动过
     */
    suspend fun setNotFirstLaunch() {
        putBoolean(KEY_IS_FIRST_LAUNCH, false)
    }

    /**
     * 检查用户资料是否已完善
     */
    suspend fun isProfileCompleted(): Boolean = getBoolean(KEY_PROFILE_COMPLETED, false)

    /**
     * 检查用户资料是否已完善（同步方式，用于兼容）
     */
    fun isProfileCompletedSync(): Boolean = runBlocking { isProfileCompleted() }

    /**
     * 检查用户资料是否已完善 Flow
     */
    fun isProfileCompletedFlow(): Flow<Boolean> = getBooleanFlow(KEY_PROFILE_COMPLETED, false)

    /**
     * 设置用户资料已完善
     */
    suspend fun setProfileCompleted(completed: Boolean = true) {
        putBoolean(KEY_PROFILE_COMPLETED, completed)
    }

    /**
     * 判断用户资料是否完善（基于用户信息字段）
     * 检查必要字段：昵称、生日、公会代码等
     */
    suspend fun checkProfileCompleteness(): Boolean {
        // 自动更新完善状态
        setProfileCompleted(getUserObject()?.isVerified ?: false)
        return getUserObject()?.isVerified ?: false
    }

    // ==================== 用户对象存储方法 ====================

    /**
     * 保存完整的用户对象
     * 登录成功后调用此方法保存整个用户对象
     *
     * @param userBean 用户对象
     */
    suspend fun saveUserObject(userBean: UserBean?) {
        try {
            if (userBean == null) {
                // 如果传入null，则清除用户对象
                putString(KEY_USER_OBJECT, null)
                LogX.d("DataStoreManager: 清除用户对象")
                return
            }

            // 将用户对象序列化为JSON字符串
            val userJson = userBeanAdapter.toJson(userBean)
            putString(KEY_USER_OBJECT, userJson)

            // 保存融云相关信息
            putString(KEY_RONG_YUN_TOKEN, userBean.rongcloudToken)
            putString(KEY_RONG_YUN_APP_KEY, userBean.rongcloudAppID)
            RongYunUtil.refreshCacheUserInfo(userBean)
        } catch (e: Exception) {
            LogX.e("DataStoreManager: 保存用户对象失败", e)
        }
    }

    /**
     * 获取完整的用户对象（同步方式，用于兼容）
     *
     * @return 用户对象，如果不存在或解析失败则返回null
     */
    fun getUserObject(): UserBean? = runBlocking {
        try {
            val userJson = getString(KEY_USER_OBJECT)
            if (userJson.isNullOrBlank()) {
                LogX.d("DataStoreManager: 用户对象不存在")
                return@runBlocking null
            }

            val userBean = userBeanAdapter.fromJson(userJson)
            userBean
        } catch (e: Exception) {
            LogX.e("DataStoreManager: 获取用户对象失败", e)
            null
        }
    }

    /**
     * 获取用户对象 Flow
     *
     * @return 用户对象的Flow
     */
    fun getUserObjectFlow(): Flow<UserBean?> {
        return getStringFlow(KEY_USER_OBJECT).map { userJson ->
            try {
                if (userJson.isNullOrBlank()) {
                    null
                } else {
                    userBeanAdapter.fromJson(userJson)
                }
            } catch (e: Exception) {
                LogX.e("DataStoreManager: 解析用户对象失败", e)
                null
            }
        }
    }

    /**
     * 更新用户对象的部分字段
     *
     * @param updateAction 更新操作，接收当前用户对象并返回更新后的对象
     */
    suspend fun updateUserObject(updateAction: (UserBean?) -> UserBean?) {
        try {
            val currentUser = getUserObject()
            val updatedUser = updateAction(currentUser)
            saveUserObject(updatedUser)
            LogX.d("DataStoreManager: 用户对象更新完成")
        } catch (e: Exception) {
            LogX.e("DataStoreManager: 更新用户对象失败", e)
        }
    }

    /**
     * 清除用户数据（增强版）
     * 清除所有用户相关数据，包括用户对象和登录配置
     */
    suspend fun clearAllUserData() {
        dataStore.edit { preferences ->
            // 清除基本用户信息
            preferences.remove(USER_ID_KEY)
            preferences.remove(ACCESS_TOKEN_KEY)
            preferences.remove(REFRESH_TOKEN_KEY)

            // 清除用户对象
            preferences.remove(stringPreferencesKey(KEY_USER_OBJECT))

            // 清除登录配置对象
            preferences.remove(stringPreferencesKey(KEY_ANCHOR_CONFIG))

            // 清除融云相关
            preferences.remove(stringPreferencesKey(KEY_RONG_YUN_TOKEN))
            preferences.remove(stringPreferencesKey(KEY_RONG_YUN_APP_KEY))

            // 清除配置相关字段
            preferences.remove(intPreferencesKey("daily_duration"))
            preferences.remove(stringPreferencesKey("cdn_url"))
            preferences.remove(stringPreferencesKey("agora_app_id"))

            // 重置资料完善状态
            preferences.remove(booleanPreferencesKey(KEY_PROFILE_COMPLETED))
        }
        LogX.d("DataStoreManager: 所有用户数据已清除")
    }

    /**
     * 检查用户是否已登录
     * 基于用户对象和访问令牌的存在性判断
     *
     * @return true表示已登录，false表示未登录
     */
    suspend fun isUserLoggedIn(): Boolean {
        val userObject = getUserObject()
        val accessToken = getAccessToken()
        val isLoggedIn = userObject != null && !accessToken.isNullOrBlank()
        LogX.d("DataStoreManager: 用户登录状态检查 - $isLoggedIn")
        return isLoggedIn
    }

    /**
     * 检查用户是否已登录（同步方式）
     */
    fun isUserLoggedInSync(): Boolean = runBlocking { isUserLoggedIn() }

    // ==================== 登录配置对象存储方法 ====================

    /**
     * 保存登录配置对象
     * 登录成功后调用此方法保存 AnchorConfigBean 对象
     *
     * @param authConfig 登录配置对象
     */
    suspend fun saveAnchorConfigBean(authConfig: AnchorConfigBean?) {
        try {
            if (authConfig == null) {
                // 如果传入null，则清除配置对象
                putString(KEY_ANCHOR_CONFIG, null)
                LogX.d("DataStoreManager: 清除登录配置对象")
                return
            }

            // 将配置对象序列化为JSON字符串
            val configJson = authConfigAdapter.toJson(authConfig)
            putString(KEY_ANCHOR_CONFIG, configJson)

            // 同时保存常用配置字段到单独的key中，便于快速访问
            putInt("daily_duration", authConfig.daily_duration)
            putString("cdn_url", authConfig.cdn)
            putString("agora_app_id", authConfig.agoraConfig.appID)

            LogX.d("DataStoreManager: 登录配置对象保存成功 - CDN: ${authConfig.cdn}, Agora AppID: ${authConfig.agoraConfig.appID}")
        } catch (e: Exception) {
            LogX.e("DataStoreManager: 保存登录配置对象失败", e)
        }
    }

    suspend fun saveCurrentLanguage(language: String) {
        putString(KEY_LANGUAGE, language)
    }

    fun getCurrentLanguage(): String = runBlocking { getString(KEY_LANGUAGE) } ?: Locale.getDefault().language

    suspend fun saveAppId(appId: String) {
        putString(KEY_APP_ID, appId)
    }

    fun getAppId(): String = runBlocking { getString(KEY_APP_ID) } ?: ""

    /**
     * 获取登录配置对象
     *
     * @return 登录配置对象，如果不存在或解析失败则返回null
     */
    suspend fun getAnchorConfigBean(): AnchorConfigBean? {
        return try {
            val configJson = getString(KEY_ANCHOR_CONFIG)
            if (configJson.isNullOrBlank()) {
                LogX.d("DataStoreManager: 登录配置对象不存在")
                return null
            }

            val authConfig = authConfigAdapter.fromJson(configJson)
            LogX.d("DataStoreManager: 登录配置对象获取成功 - CDN: ${authConfig?.cdn}")
            authConfig
        } catch (e: Exception) {
            LogX.e("DataStoreManager: 获取登录配置对象失败", e)
            null
        }
    }

    /**
     * 获取登录配置对象（同步方式，用于兼容）
     *
     * @return 登录配置对象，如果不存在或解析失败则返回null
     */
    fun getAnchorConfigBeanSync(): AnchorConfigBean? = runBlocking { getAnchorConfigBean() }

    /**
     * 获取登录配置对象 Flow
     *
     * @return 登录配置对象的Flow
     */
    fun getAnchorConfigBeanFlow(): Flow<AnchorConfigBean?> {
        return getStringFlow(KEY_ANCHOR_CONFIG).map { configJson ->
            try {
                if (configJson.isNullOrBlank()) {
                    null
                } else {
                    authConfigAdapter.fromJson(configJson)
                }
            } catch (e: Exception) {
                LogX.e("DataStoreManager: 解析登录配置对象失败", e)
                null
            }
        }
    }

    /**
     * 更新登录配置对象的部分字段
     *
     * @param updateAction 更新操作，接收当前配置对象并返回更新后的对象
     */
    suspend fun updateAnchorConfigBean(updateAction: (AnchorConfigBean?) -> AnchorConfigBean?) {
        try {
            val currentConfig = getAnchorConfigBean()
            val updatedConfig = updateAction(currentConfig)
            saveAnchorConfigBean(updatedConfig)
            LogX.d("DataStoreManager: 登录配置对象更新完成")
        } catch (e: Exception) {
            LogX.e("DataStoreManager: 更新登录配置对象失败", e)
        }
    }

    // ==================== 登录成功统一处理方法 ====================

    /**
     * 登录成功后的统一数据保存方法
     * 保存用户对象、访问令牌、登录配置等所有登录相关数据
     *
     * @param authBean 登录响应对象
     */
    suspend fun saveLoginData(authBean: com.mobile.anchor.app.data.model.AuthBean) {
        try {
            // 保存访问令牌
            saveTokens(authBean.accessToken)

            // 保存用户对象
            authBean.anchor?.let { userBean ->
                saveUserObject(userBean)
                // 同时保存用户ID到单独的key中
                putString(KEY_USER_ID, userBean.id)

                saveAppId(userBean.appId.toString())
            }

            // 保存登录配置
            authBean.config?.let { config ->
                saveAnchorConfigBean(config)
            }

            LogX.d("DataStoreManager: 登录数据保存完成")
        } catch (e: Exception) {
            LogX.e("DataStoreManager: 保存登录数据失败", e)
        }
    }

    // ==================== 用户注册状态检查 ====================
    suspend fun saveReviewObject(reviewBean: ReviewBean?) {
        runCatching {
            if (reviewBean == null) {
                // 如果传入null，则清除审核信息
                putString(KEY_REVIEW_INFO, null)
                LogX.d("DataStoreManager: 清除审核信息")
                return
            }

            // 将审核信息序列化为JSON字符串
            val reviewJson = reviewBeanAdapter.toJson(reviewBean)
            putString(KEY_REVIEW_INFO, reviewJson)

            LogX.d("DataStoreManager: 审核信息保存成功 - 审核状态: ${reviewBean.stat}")
        }
    }

    fun getReviewObject(): ReviewBean? = runBlocking {
        runCatching {
            val reviewJson = getString(KEY_REVIEW_INFO)
            if (reviewJson.isNullOrBlank()) {
                LogX.d("DataStoreManager: 审核信息不存在")
            }

            val reviewBean = reviewBeanAdapter.fromJson(reviewJson)
            LogX.d("DataStoreManager: 审核信息获取成功 - 审核状态: ${reviewBean?.stat}")
            reviewBean
        }.getOrNull()
    }

    suspend fun saveIsolatedConfig(it: IsolatedConfigBean) {
        putString("isolated_config", JsonUtils.toJson(it))
    }

    fun getIsolatedConfig(): IsolatedConfigBean? {
        return runBlocking {
            val configJson = getString("isolated_config")
            if (configJson.isNullOrBlank()) {
                LogX.d("DataStoreManager: 隔离配置不存在")
                return@runBlocking null
            }

            val config = JsonUtils.fromJson(configJson, IsolatedConfigBean::class.java)
            LogX.d("DataStoreManager: 隔离配置获取成功 - 隔离状态: ${config?.update_type}")
            config
        }
    }

    suspend fun saveVideoConfig(it: MutableList<RecordTimeBean>) {
        putString("record_time_config", it.toJson())
    }

    fun getVideoConfig(): MutableList<RecordTimeBean>? {
        return runBlocking {
            val configJson = getString("record_time_config")
            if (configJson.isNullOrBlank()) {
                LogX.d("DataStoreManager: videoConfig不存在")
                return@runBlocking null
            }
            val config = MoshiUtil.listFromJson<RecordTimeBean>(configJson)
            config
        }
    }

    /**
     * 检查用户注册状态并返回导航目标
     *
     * @return 导航目标：
     *   - "build_profile" 需要完善资料
     *   - "home" 可以进入主页面
     */
    suspend fun checkUserRegistrationStatus(): String {
        val userObject = getUserObject()

        return when {
            userObject == null -> {
                LogX.w("DataStoreManager: 用户对象不存在，需要重新登录")
                "login"
            }

            userObject.isRegistration -> {
                LogX.d("DataStoreManager: 用户处于注册状态(stat=0)，需要完善资料")
                "build_profile"
            }

            !userObject.isVerified -> {
                LogX.d("DataStoreManager: 用户注册未完成(stat=${userObject.stat})，需要完善资料")
                "build_profile"
            }

            else -> {
                LogX.d("DataStoreManager: 用户注册已完成(stat=${userObject.stat})，可以进入主页面")
                "home"
            }
        }
    }

    /**
     * 检查用户注册状态（同步方式）
     */
    fun checkUserRegistrationStatusSync(): String = runBlocking { checkUserRegistrationStatus() }

    // ==================== 货币设置相关方法 ====================

    /**
     * 保存当前货币设置
     */
    suspend fun saveCurrency(currency: String) {
        putString(KEY_CURRENCY, currency)
        LogX.d("DataStoreManager: 货币设置保存成功 - $currency")
    }

    /**
     * 获取当前货币设置
     */
    suspend fun getCurrency(): String {
        return getString(KEY_CURRENCY, Constants.Currency.DEFAULT) ?: Constants.Currency.DEFAULT
    }

    /**
     * 获取当前货币设置（同步方式）
     */
    fun getCurrencySync(): String = runBlocking { getCurrency() }

    /**
     * 获取当前货币设置 Flow
     */
    fun getCurrencyFlow(): Flow<String> {
        return getStringFlow(KEY_CURRENCY, Constants.Currency.DEFAULT).map {
            it ?: Constants.Currency.DEFAULT
        }
    }

    // ==================== 全局通知设置相关方法 ====================

    /**
     * 保存全局通知开关状态
     */
    suspend fun setGlobalNotificationEnabled(enabled: Boolean) {
        putBoolean(KEY_GLOBAL_NOTIFICATION_ENABLED, enabled)
        LogX.d("DataStoreManager: 全局通知开关设置为 - $enabled")
    }

    /**
     * 获取全局通知开关状态
     */
    suspend fun isGlobalNotificationEnabled(): Boolean {
        return getBoolean(KEY_GLOBAL_NOTIFICATION_ENABLED, true) // 默认开启
    }

    /**
     * 获取全局通知开关状态（同步方式）
     */
    fun isGlobalNotificationEnabledSync(): Boolean = runBlocking {
        isGlobalNotificationEnabled()
    }

    /**
     * 获取全局通知开关状态 Flow
     */
    fun getGlobalNotificationEnabledFlow(): Flow<Boolean> {
        return getBooleanFlow(KEY_GLOBAL_NOTIFICATION_ENABLED, true)
    }
}
