package com.mobile.anchor.app.ui.screens.blacklist

import com.mobile.anchor.app.data.model.RelationAction
import com.mobile.anchor.app.data.model.RelationList
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.BodyParams
import com.mobile.anchor.app.data.service.UserApiService
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.ui.lce.PageState
import com.mobile.anchor.app.ui.viewmodels.BaseViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 黑名单页面ViewModel
 */
class BlacklistViewModel : BaseViewModel() {

    private val userApiService: UserApiService by NetDelegates()

    private val _uiState = MutableStateFlow(BlacklistUiState())
    val uiState: StateFlow<BlacklistUiState> = _uiState.asStateFlow()

    private var currentCursor = ""
    private var isLoadingMore = false

    companion object {
        private const val PAGE_SIZE = 20
    }

    /**
     * 加载黑名单列表
     */
    fun loadBlacklist(isRefresh: Boolean = false) {
        if (isLoadingMore && !isRefresh) return

        if (isRefresh) {
            currentCursor = ""
            _uiState.value = _uiState.value.copy(pageState = PageState.Loading)
        } else {
            isLoadingMore = true
        }

        ktHttpRequest {
            val response =
                userApiService.getRelationList(RelationList.BLOCK.value, currentCursor).await()
            response?.let { pageResponse ->
                val userList = pageResponse.records ?: emptyList()

                if (isRefresh) {
                    _uiState.value = _uiState.value.copy(
                        blacklist = userList,
                        pageState = if (userList.isEmpty()) PageState.Empty else PageState.Success,
                        error = null
                    )
                } else {
                    val currentList = _uiState.value.blacklist
                    _uiState.value = _uiState.value.copy(
                        blacklist = currentList + userList,
                        pageState = PageState.Success,
                        error = null
                    )
                }

                // 更新游标和hasMore状态
                currentCursor = pageResponse.cursor
                // 判断是否还有更多数据：返回数据数量等于页面大小且cursor不为空
                val hasMore = userList.size >= PAGE_SIZE && !pageResponse.cursor.isNullOrEmpty()
                _uiState.value = _uiState.value.copy(hasMore = hasMore)
                isLoadingMore = false
                LogX.d("黑名单列表加载成功，共 ${userList.size} 条数据，cursor: ${pageResponse.cursor}, hasMore: $hasMore")
            }
        }
    }

    /**
     * 从黑名单移除用户
     */
    fun removeFromBlacklist(userId: String) {
        ktHttpRequest {
            // 使用 relationOperation 接口取消拉黑
            userApiService.relationOperation(
                RelationAction.UNBLOCK.value, BodyParams.relationOpParamsBody(userId)
            ).await()

            // 从本地列表中移除
            val currentList = _uiState.value.blacklist
            val updatedList = currentList.filter { it.id != userId }
            _uiState.value = _uiState.value.copy(
                blacklist = updatedList,
                pageState = if (updatedList.isEmpty()) PageState.Empty else PageState.Success
            )

            LogX.d("用户已从黑名单移除: $userId")
        }
    }

    /**
     * 加载更多数据
     */
    fun loadMore() {
        if (currentCursor.isNotEmpty() && !isLoadingMore && _uiState.value.hasMore) {
            loadBlacklist(isRefresh = false)
        }
    }

    /**
     * 刷新数据
     */
    fun refresh() {
        loadBlacklist(isRefresh = true)
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * 黑名单页面UI状态
 */
data class BlacklistUiState(
    val blacklist: List<UserBean> = emptyList(),
    val pageState: PageState = PageState.Default,
    val error: String? = null,
    val hasMore: Boolean = true
)
