package com.mobile.anchor.app.ui.screens.mine

import android.content.Context
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.painter.ColorPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import com.bdc.android.library.extension.finish
import com.bdc.android.library.utils.ToastUtil
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.Quadruple
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.data.network.ApiResult
import com.mobile.anchor.app.extension.buildImageUrl
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.manager.FileUploadManager
import com.mobile.anchor.app.ui.components.AnchorButton
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTabRow
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.BottomSelectDialog
import com.mobile.anchor.app.ui.components.PrimaryGradient
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.viewmodels.UserViewModel
import com.mobile.anchor.app.utils.ComposeImagePickerUtils
import com.mobile.anchor.app.utils.ContextHolder.context
import com.mobile.anchor.app.utils.rememberImagePickerConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import sh.calvin.reorderable.ReorderableCollectionItemScope
import sh.calvin.reorderable.ReorderableItem
import sh.calvin.reorderable.rememberReorderableLazyGridState

/**
 * 上传状态数据类
 */
data class UploadingItem(
    val id: String = java.util.UUID.randomUUID().toString(),
    val mediaType: UserBean.AlbumBean.MediaType,
    val isUploading: Boolean = true,
    val uploadedUrl: String? = null,
    val resourceType: Int = 1 // 1=图片，2=视频
)

/**
 * 相册管理页面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun AlbumManageScreen(
    viewModel: UserViewModel = viewModel()
) {
    val context = LocalContext.current
    val tabs = listOf(
        stringResource(R.string.album_manage_common), stringResource(R.string.album_manage_private)
    )
    val coroutineScope = rememberCoroutineScope()
    val pagerState = rememberPagerState(initialPage = 0, pageCount = { tabs.size })
    val userBean by DataStoreManager.getUserObjectFlow().collectAsState(null)

    // 相册数据状态
    val commonPhotos = remember { mutableStateListOf<UserBean.AlbumBean>() }
    val privatePhotos = remember { mutableStateListOf<UserBean.AlbumBean>() }

    // 上传状态列表
    val uploadingItems = remember { mutableStateListOf<UploadingItem>() }

    // UI状态
    var showMediaPickerDialog by remember { mutableStateOf(false) }
    var isUploading by remember { mutableStateOf(false) }
    var isSubmitting by remember { mutableStateOf(false) }
    var uploadError by remember { mutableStateOf<String?>(null) }
    var currentPhotoUri by remember { mutableStateOf<Uri?>(null) }

    // 预览状态
    var showMediaPreview by remember { mutableStateOf(false) }
    var previewMediaList by remember { mutableStateOf<List<UserBean.AlbumBean>>(emptyList()) }
    var previewInitialIndex by remember { mutableStateOf(0) }

    // 内容变更状态跟踪
    var hasCommonPhotosChanged by remember { mutableStateOf(false) }
    var hasPrivatePhotosChanged by remember { mutableStateOf(false) }
    var showSubmitButton by remember { mutableStateOf(false) }

    // 跟踪是否有服务器图片被删除
    var hasServerPhotosDeleted by remember { mutableStateOf(false) }

    // 当前选中的标签页类型
    val currentMediaType =
        if (pagerState.currentPage == 0) UserBean.AlbumBean.MediaType.COMMON else UserBean.AlbumBean.MediaType.PRIVATE // 1=Common, 2=Private

    // 图片选择配置 - 支持图片和视频
    val imagePickerConfig = rememberImagePickerConfig(
        showCamera = true, showGallery = true, showGooglePhotos = false
    )

    var pendingImagePickerType by remember { mutableStateOf(ComposeImagePickerUtils.ImagePickerType.GALLERY) }

    // 相机拍照启动器
    val cameraLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        if (success && currentPhotoUri != null) {
            // 立即添加占位项
            val uploadingItem = UploadingItem(
                mediaType = currentMediaType, resourceType = 1
            )
            uploadingItems.add(uploadingItem)

            handleUpload(
                context, coroutineScope, currentPhotoUri, currentMediaType, 1
            ) { success, result ->
                // 移除占位项
                uploadingItems.removeAll { it.id == uploadingItem.id }

                if (success) {
                    val albumBean = UserBean.AlbumBean(
                        media_type = currentMediaType.value, url = result, resource_type = 1, // 图片
                        media_status = 0, // 未审核
                        sort = if (currentMediaType == UserBean.AlbumBean.MediaType.COMMON) commonPhotos.size else privatePhotos.size
                    )

                    // 直接添加到对应的photos列表
                    if (currentMediaType == UserBean.AlbumBean.MediaType.COMMON) {
                        commonPhotos.add(albumBean)
                        hasCommonPhotosChanged = true // 标记内容已变更
                    } else {
                        privatePhotos.add(albumBean)
                        hasPrivatePhotosChanged = true // 标记内容已变更
                    }
                    LogX.d("添加图片到photos列表: $albumBean")
                } else {
                    uploadError = context.getString(R.string.upload_failed)
                }
            }
            uploadError = null
        }
        showMediaPickerDialog = false
    }

    // 单个媒体选择启动器（使用系统photo picker）
    val galleryLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        if (uri != null) {
            val resourceType = getResourceTypeFromUri(context, uri)

            // 立即添加占位项
            val uploadingItem = UploadingItem(
                mediaType = currentMediaType, resourceType = resourceType
            )
            uploadingItems.add(uploadingItem)

            uploadError = null

            handleUpload(
                context, coroutineScope, uri, currentMediaType, resourceType
            ) { success, result ->

                // 移除占位项
                uploadingItems.removeAll { it.id == uploadingItem.id }
                LogX.d("移除占位项: $uploadingItem $success $result")

                if (success) {
                    val albumBean = UserBean.AlbumBean(
                        media_type = currentMediaType.value,
                        url = result,
                        resource_type = resourceType,
                        media_status = 0, // 未审核
                        sort = if (currentMediaType == UserBean.AlbumBean.MediaType.COMMON) commonPhotos.size else privatePhotos.size
                    )

                    // 直接添加到对应的photos列表
                    if (currentMediaType == UserBean.AlbumBean.MediaType.COMMON) {
                        commonPhotos.add(albumBean)
                        hasCommonPhotosChanged = true // 标记内容已变更
                    } else {
                        privatePhotos.add(albumBean)
                        hasPrivatePhotosChanged = true // 标记内容已变更
                    }
                    LogX.d("添加媒体到photos列表: $albumBean")
                } else {
                    uploadError = result
                }
            }
        }
        showMediaPickerDialog = false
    }

    // 多媒体选择启动器（Android 13+ Photo Picker API）
    val multipleMediaPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.PickMultipleVisualMedia()
    ) { uris ->
        if (uris.isNotEmpty()) {
            uploadError = null

            // 为每个选中的媒体文件立即添加占位项
            val uploadingItemsList = mutableListOf<UploadingItem>()
            uris.forEach { uri ->
                val resourceType = getResourceTypeFromUri(context, uri)
                val uploadingItem = UploadingItem(
                    mediaType = currentMediaType, resourceType = resourceType
                )
                uploadingItems.add(uploadingItem)
                uploadingItemsList.add(uploadingItem)
            }

            // 批量上传选中的媒体文件
            coroutineScope.launch {
                uris.forEachIndexed { index, uri ->
                    val resourceType = getResourceTypeFromUri(context, uri)
                    val uploadingItem = uploadingItemsList[index]

                    handleUpload(
                        context, coroutineScope, uri, currentMediaType, resourceType
                    ) { success, result ->
                        // 移除对应的占位项
                        uploadingItems.removeAll { it.id == uploadingItem.id }

                        if (success) {
                            val albumBean = UserBean.AlbumBean(
                                media_type = currentMediaType.value,
                                url = result,
                                resource_type = resourceType,
                                media_status = 0, // 未审核
                                sort = if (currentMediaType == UserBean.AlbumBean.MediaType.COMMON) commonPhotos.size else privatePhotos.size
                            )

                            // 直接添加到对应的photos列表
                            if (currentMediaType == UserBean.AlbumBean.MediaType.COMMON) {
                                commonPhotos.add(albumBean)
                                hasCommonPhotosChanged = true // 标记内容已变更
                            } else {
                                privatePhotos.add(albumBean)
                                hasPrivatePhotosChanged = true // 标记内容已变更
                            }
                        } else {
                            uploadError = result
                        }
                    }
                }
            }
        }
        showMediaPickerDialog = false
    }

    // Google Photos启动器（支持多选）
    val photoPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == android.app.Activity.RESULT_OK) {
            val uris = mutableListOf<Uri>()

            // 处理单选或多选结果
            result.data?.let { data ->
                if (data.clipData != null) {
                    // 多选
                    for (i in 0 until data.clipData!!.itemCount) {
                        data.clipData!!.getItemAt(i).uri?.let { uri ->
                            uris.add(uri)
                        }
                    }
                } else if (data.data != null) {
                    // 单选
                    uris.add(data.data!!)
                }
            }

            if (uris.isNotEmpty()) {
                uploadError = null

                // 为每个选中的媒体文件立即添加占位项
                val uploadingItemsList = mutableListOf<UploadingItem>()
                uris.forEach { uri ->
                    val resourceType = getResourceTypeFromUri(context, uri)
                    val uploadingItem = UploadingItem(
                        mediaType = currentMediaType, resourceType = resourceType
                    )
                    uploadingItems.add(uploadingItem)
                    uploadingItemsList.add(uploadingItem)
                }

                // 批量上传选中的媒体文件
                coroutineScope.launch {
                    uris.forEachIndexed { index, uri ->
                        val resourceType = getResourceTypeFromUri(context, uri)
                        val uploadingItem = uploadingItemsList[index]

                        handleUpload(
                            context, coroutineScope, uri, currentMediaType, resourceType
                        ) { success, result ->
                            // 移除对应的占位项
                            uploadingItems.removeAll { it.id == uploadingItem.id }

                            if (success) {
                                val albumBean = UserBean.AlbumBean(
                                    media_type = currentMediaType.value,
                                    url = result,
                                    resource_type = resourceType,
                                    media_status = 0, // 未审核
                                    sort = if (currentMediaType == UserBean.AlbumBean.MediaType.COMMON) commonPhotos.size else privatePhotos.size
                                )

                                // 直接添加到对应的photos列表
                                if (currentMediaType == UserBean.AlbumBean.MediaType.COMMON) {
                                    commonPhotos.add(albumBean)
                                    hasCommonPhotosChanged = true // 标记内容已变更
                                } else {
                                    privatePhotos.add(albumBean)
                                    hasPrivatePhotosChanged = true // 标记内容已变更
                                }
                                LogX.d("添加Google Photos到photos列表: $albumBean")
                            } else {
                                uploadError = result
                            }
                        }
                    }
                }
            }
        }
        showMediaPickerDialog = false
    }

    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            LogX.d("权限申请成功，继续执行图片选择操作:")
            ComposeImagePickerUtils.executeImagePickerAction(
                type = pendingImagePickerType,
                context = context,
                cameraLauncher = cameraLauncher,
                multipleMediaPickerLauncher = multipleMediaPickerLauncher,
                photoPickerLauncher = photoPickerLauncher,
                onUriCreated = { uri -> currentPhotoUri = uri })
        } else {
            uploadError = context.getString(R.string.permission_denied)
            showMediaPickerDialog = false
        }
    }

    // 初始化数据 - 确保正确显示userBean中的media_list
    LaunchedEffect(userBean) {
        userBean?.media_list?.let { mediaList ->
            // 清空现有数据
            commonPhotos.clear()
            privatePhotos.clear()

            // 按类型分组并按sort排序
            val commonList =
                mediaList.filter { it.media_type == UserBean.AlbumBean.MediaType.COMMON.value }
                    .sortedBy { it.sort }
            val privateList =
                mediaList.filter { it.media_type == UserBean.AlbumBean.MediaType.PRIVATE.value }
                    .sortedBy { it.sort }

            commonPhotos.addAll(commonList)
            privatePhotos.addAll(privateList)
        }
    }

    AnchorScaffold(
        topBar = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.statusBarsPadding()
            ) {
                AnchorTopBar(
                    "", onNavigationClick = { context.finish() }, modifier = Modifier.width(50.dp)
                )
                // 标签页
                AnchorTabRow(
                    tabs = tabs,
                    selectedTabIndex = pagerState.currentPage,
                    onTabSelected = { index ->
                        coroutineScope.launch {
                            pagerState.animateScrollToPage(index)
                        }
                    },
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .weight(1f),
                    backgroundColor = Color.Transparent
                )
            }
        }) { paddingValues ->
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            HorizontalPager(
                state = pagerState,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = paddingValues.calculateTopPadding())
            ) { page ->
                when (page) {
                    0 -> AlbumTabContent(
                        photos = commonPhotos,
                        uploadingItems = uploadingItems,
                        currentMediaType = UserBean.AlbumBean.MediaType.COMMON,
                        hasChanged = hasCommonPhotosChanged,
                        isSubmitting = isSubmitting,
                        uploadError = uploadError,
                        userBean = userBean,
                        commonPhotos = commonPhotos,
                        privatePhotos = privatePhotos,
                        onSubmit = {
                            handleSubmit(
                                commonPhotos = commonPhotos.filter { !it.isRejected },
                                privatePhotos = privatePhotos,
                                viewModel = viewModel,
                                onSubmittingChange = { isSubmitting = it },
                                onError = { uploadError = it },
                                onSuccess = {
                                    ToastUtil.show(context.getString(R.string.album_updated_successfully))
                                    // 重置变更状态
                                    hasCommonPhotosChanged = false
                                    hasPrivatePhotosChanged = false
                                    hasServerPhotosDeleted = false
                                    // 重新获取用户信息以更新数据
                                    viewModel.fetchUserInfo()
                                    context.finish()
                                })
                        },
                        onReorder = { fromIndex, toIndex ->
                            handleReorder(
                                photos = commonPhotos,
                                fromIndex = fromIndex,
                                toIndex = toIndex,
                                mediaType = UserBean.AlbumBean.MediaType.COMMON,
                                onContentChanged = { hasCommonPhotosChanged = true })
                        },
                        onDelete = { item ->
                            handleDelete(
                                item = item,
                                photos = commonPhotos,
                                commonPhotos = commonPhotos,
                                privatePhotos = privatePhotos,
                                onServerPhotoDeleted = { hasServerPhotosDeleted = true },
                                onError = { uploadError = it })
                            hasCommonPhotosChanged = true // 标记内容已变更
                        },
                        onMediaClick = { albumBean, index ->
                            previewMediaList = commonPhotos
                            previewInitialIndex = index
                            showMediaPreview = true
                        },
                        onShowSubmitButton = {
                            showSubmitButton = it
                        })

                    1 -> AlbumTabContent(
                        photos = privatePhotos,
                        uploadingItems = uploadingItems,
                        currentMediaType = UserBean.AlbumBean.MediaType.PRIVATE,
                        hasChanged = hasPrivatePhotosChanged,
                        isSubmitting = isSubmitting,
                        uploadError = uploadError,
                        userBean = userBean,
                        commonPhotos = commonPhotos,
                        privatePhotos = privatePhotos,
                        onSubmit = {
                            handleSubmit(
                                commonPhotos = commonPhotos,
                                privatePhotos = privatePhotos.filter { !it.isRejected },
                                viewModel = viewModel,
                                onSubmittingChange = { isSubmitting = it },
                                onError = { uploadError = it },
                                onSuccess = {
                                    // 显示成功提示
                                    ToastUtil.show(context.getString(R.string.album_updated_successfully))
                                    // 重置变更状态
                                    hasCommonPhotosChanged = false
                                    hasPrivatePhotosChanged = false
                                    hasServerPhotosDeleted = false
                                    // 重新获取用户信息以更新数据
                                    viewModel.fetchUserInfo()
                                    context.finish()
                                })
                        },
                        onReorder = { fromIndex, toIndex ->
                            handleReorder(
                                photos = privatePhotos,
                                fromIndex = fromIndex,
                                toIndex = toIndex,
                                mediaType = UserBean.AlbumBean.MediaType.PRIVATE,
                                onContentChanged = { hasPrivatePhotosChanged = true })
                        },
                        onDelete = { item ->
                            handleDelete(
                                item = item,
                                photos = privatePhotos,
                                commonPhotos = commonPhotos,
                                privatePhotos = privatePhotos,
                                onServerPhotoDeleted = { hasServerPhotosDeleted = true },
                                onError = { uploadError = it })
                            hasPrivatePhotosChanged = true // 标记内容已变更
                        },
                        onMediaClick = { albumBean, index ->
                            previewMediaList = privatePhotos
                            previewInitialIndex = index
                            showMediaPreview = true
                        },
                        onShowSubmitButton = {
                            showSubmitButton = it
                        })
                }
            }

            val targetBottomPadding = if (showSubmitButton) 120.dp else 50.dp
            val animatedBottomPadding by animateDpAsState(
                targetValue = targetBottomPadding,
                animationSpec = tween(durationMillis = 300),
                label = "fabPadding"
            )

            // 浮动添加按钮 - 始终显示
            FloatingActionButton(
                onClick = { showMediaPickerDialog = true },
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(
                        end = 32.dp, bottom = animatedBottomPadding
                    )
                    .size(56.dp),
                containerColor = Color.Transparent,
                contentColor = Color.White
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            brush = PrimaryGradient, shape = CircleShape
                        ), contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加照片",
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }

    // 媒体选择弹框
    BottomSelectDialog(
        visible = showMediaPickerDialog,
        title = stringResource(R.string.select_media),
        options = ComposeImagePickerUtils.getImagePickerOptions(imagePickerConfig),
        onDismiss = { showMediaPickerDialog = false },
        onOptionSelected = { index, _ ->
            pendingImagePickerType =
                ComposeImagePickerUtils.getImagePickerType(index, imagePickerConfig)
            when (pendingImagePickerType) {
                ComposeImagePickerUtils.ImagePickerType.CAMERA -> {
                    // 相机拍照
                    ComposeImagePickerUtils.launchCamera(
                        context = context,
                        cameraLauncher = cameraLauncher,
                        permissionLauncher = permissionLauncher,
                        onUriCreated = { uri -> currentPhotoUri = uri })
                }

                ComposeImagePickerUtils.ImagePickerType.GALLERY -> {
                    // 相册选择（支持多选和视频）
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                        // Android 13+ 使用新的 Photo Picker API（支持多选）
                        checkAndRequestMediaPermissions(
                            context = context, permissionLauncher = permissionLauncher
                        ) {
                            multipleMediaPickerLauncher.launch(
                                androidx.activity.result.PickVisualMediaRequest(
                                    ActivityResultContracts.PickVisualMedia.ImageAndVideo
                                )
                            )
                        }
                    } else {
                        // Android 12 及以下使用传统方式（单选）
                        checkAndRequestMediaPermissions(
                            context = context, permissionLauncher = permissionLauncher
                        ) {
                            galleryLauncher.launch("*/*") // 支持所有媒体类型（图片和视频）
                        }
                    }
                }

                ComposeImagePickerUtils.ImagePickerType.GOOGLE_PHOTOS -> {
                    // Google Photos选择（支持多选）
                    checkAndRequestMediaPermissions(
                        context = context, permissionLauncher = permissionLauncher
                    ) {
                        val intent = android.content.Intent(
                            android.content.Intent.ACTION_PICK,
                            android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                        ).apply {
                            type = "*/*" // 支持图片和视频
                            putExtra(android.content.Intent.EXTRA_ALLOW_MULTIPLE, true) // 支持多选
                            setPackage("com.google.android.apps.photos")
                        }

                        // 如果Google Photos不可用，回退到系统相册
                        if (intent.resolveActivity(context.packageManager) == null) {
                            intent.setPackage(null)
                        }

                        photoPickerLauncher.launch(intent)
                    }
                }
            }
            showMediaPickerDialog = false
        })

    // 媒体预览弹框
    if (showMediaPreview) {
        MediaPreviewScreen(
            mediaList = previewMediaList,
            initialIndex = previewInitialIndex,
            onDismiss = { showMediaPreview = false })
    }
}

/**
 * 相册标签页内容
 */
@Composable
private fun AlbumTabContent(
    photos: MutableList<UserBean.AlbumBean>,
    uploadingItems: List<UploadingItem>,
    currentMediaType: UserBean.AlbumBean.MediaType,
    hasChanged: Boolean,
    isSubmitting: Boolean,
    uploadError: String?,
    userBean: UserBean?,
    commonPhotos: List<UserBean.AlbumBean>,
    privatePhotos: List<UserBean.AlbumBean>,
    onSubmit: () -> Unit,
    onReorder: (Int, Int) -> Unit,
    onDelete: (UserBean.AlbumBean) -> Unit = {},
    onShowSubmitButton: (Boolean) -> Unit = {},
    onMediaClick: (UserBean.AlbumBean, Int) -> Unit = { _, _ -> }
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 合并显示已有照片和上传中的占位项
        val currentUploadingItems = uploadingItems.filter { it.mediaType == currentMediaType }
        val totalItemsCount = photos.size + currentUploadingItems.size
        if (totalItemsCount == 0) {
            // 空状态
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .weight(1f)
                    .padding(horizontal = 20.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = if ((commonPhotos.size + privatePhotos.size) <= 0) stringResource(
                        R.string.album_empty_tips, 5
                    ) else stringResource(R.string.no_data),
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    fontSize = 16.sp
                )
            }
        } else {
            // 照片网格 - 使用Calvin-LL/Reorderable库优化拖拽排序体验
            val lazyGridState = rememberLazyGridState()
            val reorderableLazyGridState =
                rememberReorderableLazyGridState(lazyGridState) { from, to ->
                    // 只处理已有照片的拖拽，不处理上传中的占位项
                    if (from.index < photos.size && to.index < photos.size) {
                        onReorder(from.index, to.index)
                    }
                }

            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                state = lazyGridState,
                contentPadding = PaddingValues(16.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier.weight(1f)
            ) {
                // 显示已有照片
                itemsIndexed(photos, key = { _, item ->
                    // 使用组合key确保唯一性：服务器数据用id，本地数据用url+hashCode
                    if (item.id != 0) "server_${item.id}" else "local_${item.url.hashCode()}_${item.media_type}_${item.sort}"
                }) { index, albumBean ->
                    ReorderableItem(
                        reorderableLazyGridState,
                        key = if (albumBean.id != 0) "server_${albumBean.id}" else "local_${albumBean.url.hashCode()}_${albumBean.media_type}_${albumBean.sort}"
                    ) { isDragging ->
                        ReorderableMediaItem(
                            albumBean = albumBean,
                            isDragging = isDragging,
                            onDelete = { onDelete(albumBean) },
                            onClick = { onMediaClick(albumBean, index) },
                            reorderableItemScope = this // 传递ReorderableCollectionItemScope
                        )
                    }
                }

                // 显示上传中的占位项（不可拖拽）
                items(currentUploadingItems, key = { it.id }) { uploadingItem ->
                    UploadingMediaItem(uploadingItem = uploadingItem)
                }
            }
        }

        // 上传错误提示
        uploadError?.let { error ->
            Text(
                text = error,
                color = Color.Red,
                fontSize = 12.sp,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
            )
        }

        // 提交按钮显示逻辑 - 添加第一次上传的特殊处理
        val shouldShowSubmitButton = when {
            // 移除isSubmitting条件，提交时按钮应该保持显示但变成loading状态
            !hasChanged -> false // 内容未变更时不显示
            // 如果删除完了，检查是否有服务器图片被删除
//            photos.isEmpty() -> hasServerPhotosDeleted
            else -> {
                // 检查是否为第一次上传（用户原本没有任何照片）
                val originalCommonPhotos =
                    userBean?.media_list?.filter { it.media_type == UserBean.AlbumBean.MediaType.COMMON.value }
                        ?: emptyList()
                val originalPrivatePhotos =
                    userBean?.media_list?.filter { it.media_type == UserBean.AlbumBean.MediaType.PRIVATE.value }
                        ?: emptyList()
                val isFirstTimeUpload =
                    originalCommonPhotos.isEmpty() && originalPrivatePhotos.isEmpty()

//                if (isFirstTimeUpload) {
                // 第一次上传需要至少5张照片或视频
                val totalCurrentPhotos =
                    commonPhotos.filter { !it.isRejected }.size + privatePhotos.filter { !it.isRejected }.size
                totalCurrentPhotos >= 5
//                } else {
                // 非第一次上传，有内容变更就显示提交按钮
//                    true
//                }
            }
        }

        onShowSubmitButton.invoke(shouldShowSubmitButton)

        if (shouldShowSubmitButton) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AnchorButton(
                    text = stringResource(R.string.submit),
                    onClick = onSubmit,
                    enabled = !isSubmitting,
                    isLoading = isSubmitting,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 30.dp)
                )
            }
        }
    }
}

/**
 * 可重新排序的媒体项 - 使用Calvin-LL/Reorderable库
 */
@Composable
private fun ReorderableMediaItem(
    albumBean: UserBean.AlbumBean,
    isDragging: Boolean,
    onDelete: (UserBean.AlbumBean) -> Unit,
    onClick: () -> Unit = {},
    reorderableItemScope: ReorderableCollectionItemScope
) {
    val elevation by animateDpAsState(
        targetValue = if (isDragging) 8.dp else 0.dp, label = "elevation"
    )
    val scale by animateFloatAsState(
        targetValue = if (isDragging) 1.05f else 1f, label = "scale"
    )
    val alpha by animateFloatAsState(
        targetValue = if (isDragging) 0.9f else 1f, label = "alpha"
    )

    with(reorderableItemScope) {
        Box(
            modifier = Modifier
                .shadow(
                    elevation = elevation, shape = RoundedCornerShape(12.dp)
                )
                .scale(scale)
                .alpha(alpha)
                .draggableHandle() // 在ReorderableScope中使用draggableHandle
        ) {
            MediaItem(
                albumBean = albumBean,
                isDragging = isDragging,
                onDelete = { onDelete(albumBean) },
                onClick = onClick
            )
        }
    }
}

/**
 * 媒体项（支持图片和视频）
 */
@Composable
private fun MediaItem(
    albumBean: UserBean.AlbumBean,
    isDragging: Boolean = false,
    modifier: Modifier = Modifier,
    onDelete: () -> Unit = {},
    onClick: () -> Unit = {}
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .clip(RoundedCornerShape(12.dp))
            .background(
                if (isDragging) Color.Gray.copy(alpha = 0.3f) else Color.Transparent
            )
            .clickable { onClick() }) {
        // 媒体内容
        if (albumBean.resource_type == 2) {
            // 视频缩略图 - 使用专门的VideoThumbnail组件
            VideoThumbnail(
                videoUrl = albumBean.url,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        } else {
            // 图片 - 使用AsyncImage
            AsyncImage(
                model = albumBean.url.buildImageUrl(),
                contentDescription = "图片",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
                placeholder = ColorPainter(Color.Gray),
                error = ColorPainter(Color.Gray)
            )
        }

        // 视频标识
        if (albumBean.resource_type == 2) {
            Box(
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(32.dp)
                    .background(
                        Color.Black.copy(alpha = 0.6f), CircleShape
                    ), contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = "视频",
                    tint = Color.White,
                    modifier = Modifier.size(16.dp)
                )
            }
        }

        // 审核状态标识 - 移到左上角，半透明背景
        Box(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(4.dp)
                .background(
                    Color.Black.copy(alpha = 0.6f), // 半透明背景
                    RoundedCornerShape(4.dp)
                )
                .padding(horizontal = 6.dp, vertical = 3.dp)
        ) {
            Text(
                text = when (albumBean.media_status) {
                    0 -> stringResource(R.string.not_submitted)
                    1 -> stringResource(R.string.reviewing)
                    2 -> stringResource(R.string.approved)
                    3, 5 -> stringResource(R.string.rejected)
                    else -> stringResource(R.string.unknow)
                }, color = when (albumBean.media_status) {
                    0 -> Color.Cyan
                    1 -> Color.Yellow
                    2 -> Color.Green
                    3, 5 -> Color.Red
                    else -> Color.White
                }, fontSize = 10.sp, fontWeight = FontWeight.Medium
            )
        }

        // 删除按钮 - 右上角
        Box(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(4.dp)
                .size(24.dp)
                .background(
                    Color.Black.copy(alpha = 0.6f), // 半透明背景
                    CircleShape
                )
                .clickable { onDelete() }, contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "删除",
                tint = Color.White,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 上传中的媒体项组件
 */
@Composable
private fun UploadingMediaItem(
    uploadingItem: UploadingItem
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .clip(RoundedCornerShape(12.dp))
            .background(Color.Gray) // 纯色占位背景
    ) {
        // 加载动画 - 确保在中央显示
        CircularProgressIndicator(
            modifier = Modifier
                .size(32.dp)
                .align(Alignment.Center), color = Primary, // 使用主题色
            strokeWidth = 3.dp
        )

        // 上传类型标识
        if (uploadingItem.resourceType == 2) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(4.dp)
                    .background(
                        Color.Black.copy(alpha = 0.6f), RoundedCornerShape(4.dp)
                    )
                    .padding(horizontal = 6.dp, vertical = 3.dp)
            ) {
                Text(
                    text = stringResource(R.string.video),
                    color = Color.White,
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 处理提交逻辑 - 合并commonPhotos和privatePhotos一起提交
 */
private fun handleSubmit(
    commonPhotos: List<UserBean.AlbumBean>,
    privatePhotos: List<UserBean.AlbumBean>,
    viewModel: UserViewModel,
    onSubmittingChange: (Boolean) -> Unit,
    onError: (String) -> Unit,
    onSuccess: () -> Unit
) {
    // 合并两个数组，重新设置sort值
    val allMediaList = mutableListOf<UserBean.AlbumBean>()

    // 添加commonPhotos，按media_type=1独立计数（从0开始）
    commonPhotos.forEachIndexed { index, albumBean ->
        allMediaList.add(albumBean.copy(sort = index))
    }

    // 添加privatePhotos，按media_type=2独立计数（从0开始）
    privatePhotos.forEachIndexed { index, albumBean ->
        allMediaList.add(albumBean.copy(sort = index))
    }

    if (allMediaList.isEmpty()) {
        onError(context.getString(R.string.no_photos_to_submit))
        return
    }

    onSubmittingChange(true)
    viewModel.updateAlbum(allMediaList) { success ->
        onSubmittingChange(false)
        if (success) {
            onSuccess()
        } else {
            onError(context.getString(R.string.submit_failed))
        }
    }
}

/**
 * 处理拖拽排序（仅本地排序，不调用API）
 * 按media_type分别计算sort值
 */
private fun handleReorder(
    photos: MutableList<UserBean.AlbumBean>,
    fromIndex: Int,
    toIndex: Int,
    mediaType: UserBean.AlbumBean.MediaType,
    onContentChanged: () -> Unit = {}
) {
    if (fromIndex == toIndex || fromIndex < 0 || toIndex < 0 || fromIndex >= photos.size || toIndex >= photos.size) {
        return
    }

    // 执行排序
    val item = photos.removeAt(fromIndex)
    photos.add(toIndex, item)

    // 按media_type分别更新sort值（每个media_type独立计数，从0开始）
    photos.forEachIndexed { index, albumBean ->
        // 确保只更新当前media_type的sort值
        if (albumBean.media_type == mediaType.value) {
            val updatedItem = albumBean.copy(sort = index)
            photos[index] = updatedItem
        }
    }

    // 标记内容已变更，触发提交按钮显示
    onContentChanged()
}

/**
 * 处理删除逻辑（增加最小数量限制）
 */
private fun handleDelete(
    item: UserBean.AlbumBean,
    photos: MutableList<UserBean.AlbumBean>,
    commonPhotos: List<UserBean.AlbumBean>,
    privatePhotos: List<UserBean.AlbumBean>,
    onServerPhotoDeleted: () -> Unit = {},
    onError: (String) -> Unit = {}
) {
    // 检查删除后是否少于5张
    val totalPhotosCount =
        commonPhotos.filter { !it.isRejected }.size + privatePhotos.filter { !it.isRejected }.size
    if (totalPhotosCount <= 5 && !item.isRejected) {
        onError(context.getString(R.string.album_needs_to_retain_at_least_5_photos))
        LogX.w("删除失败：相册总数($totalPhotosCount)少于等于5张，无法删除")
        return
    }

    // 从photos列表中移除
    if (photos.contains(item)) {
        photos.remove(item)

        // 检查删除的图片是否有ID（服务器存在的图片）
        if (item.id > 0) {
            onServerPhotoDeleted()
        } else {
            LogX.d("删除的是本地图片，无需提交")
        }
    } else {
        LogX.w("要删除的项目不在当前列表中")
    }
}

/**
 * 处理媒体上传
 */
private fun handleUpload(
    context: Context,
    coroutineScope: kotlinx.coroutines.CoroutineScope,
    uri: Uri?,
    mediaType: UserBean.AlbumBean.MediaType,
    resourceType: Int,
    onUploadResult: (Boolean, String) -> Unit,
) {
    LogX.d("开始上传媒体文件: $uri")

    if (checkUploadLimitExceeded(context, mediaType, resourceType) { message ->
            onUploadResult.invoke(false, message)
            ToastUtil.show(message)
        }) {
        return
    }

    uri?.let {
        coroutineScope.launch {
            val fileType =
                if (resourceType == 1) FileUploadManager.FileType.IMAGE else FileUploadManager.FileType.VIDEO
            FileUploadManager.upload(
                context, uri, fileType = fileType
            ).collect { result ->
                when (result) {
                    is ApiResult.Success -> {
                        onUploadResult.invoke(true, result.getDataOrNull()?.accessUrl ?: "")
                    }

                    is ApiResult.Error -> {
                        onUploadResult.invoke(false, result.message)
                    }

                    is ApiResult.Loading -> {
                        // 保持加载状态
                    }
                }
            }
        }
    }
}

private fun checkUploadLimitExceeded(
    context: Context, mediaType: UserBean.AlbumBean.MediaType, resourceType: Int, // 1=图片，2=视频
    onLimitExceeded: (String) -> Unit
): Boolean {
    val fileType =
        if (resourceType == 1) FileUploadManager.FileType.IMAGE else FileUploadManager.FileType.VIDEO

    val (commonPhotos, privatePhotos) = DataStoreManager.getUserObject()?.media_list?.let { mediaList ->
        // 按类型分组并按sort排序
        val commonList =
            mediaList.filter { it.media_type == UserBean.AlbumBean.MediaType.COMMON.value }
                .sortedBy { it.sort }
        val privateList =
            mediaList.filter { it.media_type == UserBean.AlbumBean.MediaType.PRIVATE.value }
                .sortedBy { it.sort }
        commonList to privateList
    } ?: Pair(emptyList(), emptyList())

    // 统一封装获取当前使用的 photo/video 列表 和 限制
    val isCommon = mediaType == UserBean.AlbumBean.MediaType.COMMON
    val currentMediaList = if (isCommon) commonPhotos else privatePhotos
    val currentAlbumName = context.getString(
        if (isCommon) R.string.album_type_public else R.string.album_type_private
    )
    val (commonPhotoLimit, commonVideoLimit, privatePhotoLimit, privateVideoLimit) = DataStoreManager.getAnchorConfigBeanSync()
        ?.let {
            Quadruple(
                it.anchor_profile_photo_max_count,
                it.anchor_profile_video_max_count,
                it.anchor_secret_photo_max_count,
                it.anchor_secret_video_max_count
            )
        } ?: Quadruple(12, 3, 12, 3)

    val currentPhotoLimit = if (isCommon) commonPhotoLimit else privatePhotoLimit
    val currentVideoLimit = if (isCommon) commonVideoLimit else privateVideoLimit

// 当前资源数量（只取未被拒绝的）
    val currentResourceCount =
        currentMediaList.filter { !it.isRejected && it.resource_type == resourceType }.size

    val limitExceeded = when (fileType) {
        FileUploadManager.FileType.IMAGE -> currentResourceCount >= currentPhotoLimit
        FileUploadManager.FileType.VIDEO -> currentResourceCount >= currentVideoLimit
        else -> false
    }

    if (limitExceeded) {
        val message = context.getString(
            R.string.upload_limit_description,
            currentAlbumName,
            currentPhotoLimit,
            currentVideoLimit
        )
        onLimitExceeded(message)
        return true
    }
    return false
}

/**
 * 检查并请求媒体权限（支持图片和视频）
 */
private fun checkAndRequestMediaPermissions(
    context: Context,
    permissionLauncher: androidx.activity.compose.ManagedActivityResultLauncher<Array<String>, Map<String, Boolean>>,
    action: () -> Unit
) {
    val permissions = mutableListOf<String>()

    // 检查相机权限
    if (androidx.core.content.ContextCompat.checkSelfPermission(
            context, android.Manifest.permission.CAMERA
        ) != android.content.pm.PackageManager.PERMISSION_GRANTED
    ) {
        permissions.add(android.Manifest.permission.CAMERA)
    }

    // 检查存储权限（支持图片和视频）
    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
        // Android 13+ 使用新的媒体权限
        if (androidx.core.content.ContextCompat.checkSelfPermission(
                context, android.Manifest.permission.READ_MEDIA_IMAGES
            ) != android.content.pm.PackageManager.PERMISSION_GRANTED
        ) {
            permissions.add(android.Manifest.permission.READ_MEDIA_IMAGES)
        }
        if (androidx.core.content.ContextCompat.checkSelfPermission(
                context, android.Manifest.permission.READ_MEDIA_VIDEO
            ) != android.content.pm.PackageManager.PERMISSION_GRANTED
        ) {
            permissions.add(android.Manifest.permission.READ_MEDIA_VIDEO)
        }
    } else {
        // Android 12 及以下使用存储权限
        if (androidx.core.content.ContextCompat.checkSelfPermission(
                context, android.Manifest.permission.READ_EXTERNAL_STORAGE
            ) != android.content.pm.PackageManager.PERMISSION_GRANTED
        ) {
            permissions.add(android.Manifest.permission.READ_EXTERNAL_STORAGE)
        }
    }

    if (permissions.isNotEmpty()) {
        permissionLauncher.launch(permissions.toTypedArray())
    } else {
        action()
    }
}

/**
 * 视频缩略图组件
 */
@Composable
private fun VideoThumbnail(
    videoUrl: String, modifier: Modifier = Modifier, contentScale: ContentScale = ContentScale.Crop
) {
    val context = LocalContext.current
    var thumbnailBitmap by remember { mutableStateOf<android.graphics.Bitmap?>(null) }
    var isLoading by remember { mutableStateOf(true) }
    var hasError by remember { mutableStateOf(false) }

    // 生成视频缩略图
    LaunchedEffect(videoUrl) {
        try {
            isLoading = true
            hasError = false

            // 使用协程在后台线程生成缩略图
            withContext(Dispatchers.IO) {
                try {
                    val bitmap = generateVideoThumbnail(videoUrl)

                    if (bitmap != null) {
                        thumbnailBitmap = bitmap
                    } else {
                        hasError = true
                    }
                } catch (e: Exception) {
                    hasError = true
                }
            }
        } catch (e: Exception) {
            hasError = true
        } finally {
            isLoading = false
        }
    }

    // 显示缩略图或占位图
    Box(modifier = modifier) {
        when {
            isLoading -> {
                // 加载中显示纯色占位图
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Gray)
                )
            }

            hasError || thumbnailBitmap == null -> {
                // 错误或无缩略图时显示纯色占位图
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Gray)
                )
            }

            else -> {
                // 显示生成的缩略图
                if (thumbnailBitmap != null) {
                    Image(
                        bitmap = thumbnailBitmap!!.asImageBitmap(),
                        contentDescription = "视频缩略图",
                        modifier = Modifier.fillMaxSize(),
                        contentScale = contentScale
                    )
                } else {
                    // 备用纯色占位图
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.Gray)
                    )
                    LogX.d("VideoThumbnail - 显示备用占位图")
                }
            }
        }

        // 加载指示器
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.3f)),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp), strokeWidth = 2.dp, color = Color.White
                )
            }
        }
    }
}


/**
 * 生成视频缩略图
 */
private fun generateVideoThumbnail(videoUrl: String): android.graphics.Bitmap? {
    return try {
        val retriever = android.media.MediaMetadataRetriever()
        try {
            if (videoUrl.startsWith("http")) {
                // 网络视频URL
                retriever.setDataSource(videoUrl, HashMap<String, String>())
            } else {
                // 本地视频文件
                retriever.setDataSource(videoUrl)
            }
            // 获取第1秒的帧
            retriever.getFrameAtTime(1_000_000, android.media.MediaMetadataRetriever.OPTION_CLOSEST)
        } catch (e: Exception) {
            LogX.e("generateVideoThumbnail - 生成缩略图失败: ${e.message}")
            null
        } finally {
            try {
                retriever.release()
            } catch (e: Exception) {
                LogX.e("generateVideoThumbnail - 释放retriever失败: ${e.message}")
            }
        }
    } catch (e: Exception) {
        LogX.e("generateVideoThumbnail - 整体处理失败: ${e.message}")
        null
    }
}

/**
 * 根据URI获取资源类型
 */
private fun getResourceTypeFromUri(context: Context, uri: Uri): Int {
    val mimeType = context.contentResolver.getType(uri) ?: ""
    return when {
        mimeType.startsWith("image/") -> 1 // 图片
        mimeType.startsWith("video/") -> 2 // 视频
        else -> 1 // 默认为图片
    }
}

@Preview
@Composable
fun AlbumManageScreenPreview() {
    AnchorTheme {
        AlbumManageScreen()
    }
}
