package com.mobile.anchor.app.ui.screens.callhistory

import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import com.bdc.android.library.extension.jump
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.CallHistoryItemBean
import com.mobile.anchor.app.extension.toShowDiamond
import com.mobile.anchor.app.ui.activities.UserDetailActivity
import com.mobile.anchor.app.ui.theme.Primary
import kotlinx.coroutines.flow.distinctUntilChanged


/**
 * 黑名单页面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterialApi::class)
@Composable
fun CallHistoryScreen() {
    val viewModel = viewModel<CallHistoryViewModel>()

    val historyList = remember { viewModel.historyList }
    val isRefreshing by remember { viewModel.isRefreshing }
    val isLoadingMore by remember { viewModel.isLoadingMore }
    val hasMoreData by remember { viewModel.hasMoreData }

    // LazyColumn 状态
    val listState = rememberLazyListState()

    // 首次加载数据
    LaunchedEffect(Unit) {
        viewModel.refreshCallHistoryList()
    }

    // 监听滚动到底部自动加载更多
    LaunchedEffect(listState) {
        snapshotFlow {
            val layoutInfo = listState.layoutInfo
            val visibleItemsInfo = layoutInfo.visibleItemsInfo
            if (layoutInfo.totalItemsCount == 0) {
                false
            } else {
                val lastVisibleItem = visibleItemsInfo.lastOrNull()
                lastVisibleItem?.index == layoutInfo.totalItemsCount - 1
            }
        }.distinctUntilChanged().collect { isAtBottom ->
            if (isAtBottom && hasMoreData && !isLoadingMore && !isRefreshing) {
                viewModel.loadMoreCallHistoryList()
            }
        }
    }

    // 下拉刷新状态
    val pullRefreshState = rememberPullRefreshState(
        refreshing = isRefreshing, onRefresh = { viewModel.refreshCallHistoryList() })
    Box(
        modifier = Modifier
            .fillMaxSize()
            .pullRefresh(pullRefreshState)
    ) {
        if (historyList.isEmpty() && !isRefreshing) {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                item {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = stringResource(id = R.string.tip_no_content),
                            color = Color(0xFF9E9E9E),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Normal
                        )
                    }
                }
            }
        } else {
            LazyColumn(
                state = listState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(historyList) { item ->
                    CallHistoryItem(
                        item = item,
                    )
                }

                // 加载更多指示器
                if (isLoadingMore) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp), color = Color.White
                            )
                        }
                    }
                }

                // 没有更多数据提示
                if (!hasMoreData && historyList.isNotEmpty()) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(id = R.string.label_no_more_data),
                                color = Color(0xFF9E9E9E),
                                fontSize = 14.sp
                            )
                        }
                    }
                }
            }
        }

        // 下拉刷新指示器
        PullRefreshIndicator(
            refreshing = isRefreshing,
            state = pullRefreshState,
            modifier = Modifier.align(Alignment.TopCenter)
        )
    }
}

/**
 * 黑名单用户项
 */
@Composable
private fun CallHistoryItem(item: CallHistoryItemBean) {
    val viewModel = viewModel<CallHistoryViewModel>()
    val context = LocalContext.current
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 16.dp)
            .clickable {
                context.jump(UserDetailActivity::class.java, Bundle().apply {
                    putString("id", item.userId.toString())
                })
            }, verticalAlignment = Alignment.CenterVertically
    ) {
        // 头像
        AsyncImage(
            model = item.userAvatar,
            contentDescription = "Avatar",
            modifier = Modifier
                .size(dimensionResource(R.dimen.dp_48))
                .clip(CircleShape),
            contentScale = ContentScale.Crop,
            placeholder = painterResource(com.bdc.android.library.R.mipmap.ic_default_avatar),
            error = painterResource(com.bdc.android.library.R.mipmap.ic_default_avatar)
        )

        Spacer(modifier = Modifier.width(8.dp))

        // 用户信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = item.userNickName,
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )

            Row(verticalAlignment = Alignment.CenterVertically) {
                Image(
                    painter = painterResource(if (item.callStat == 2) R.mipmap.ic_call_status_suc else R.mipmap.ic_call_status_failed),
                    contentDescription = null
                )

                Spacer(modifier = Modifier.width(4.dp))
                if (item.callStat == 2) {
                    Text(
                        stringResource(R.string.tip_call_done),
                        color = Color(0xFF35B244),
                        fontSize = 12.sp
                    )
                    Spacer(Modifier.width(3.dp))
                    CallDurationText(item.callDurationSecond.toLong())
                } else {
                    Text(
                        stringResource(R.string.tip_call_cancel),
                        color = Color(0xFFF53D3D),
                        fontSize = 12.sp
                    )
                }
            }
        }

        Column(
            verticalArrangement = Arrangement.Center, horizontalAlignment = Alignment.End
        ) {
            if (item.coinTotal > 0) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = "+${item.coinTotal.toShowDiamond()}",
                        color = Primary,
                        fontSize = 14.sp,
                    )
                    Spacer(Modifier.width(3.dp))
                    Image(
                        painter = painterResource(R.mipmap.ic_coin),
                        modifier = Modifier.size(15.dp),
                        contentDescription = null
                    )
                }
            }

            Text(
                text = viewModel.translateDuration(item.connectAt.toLong()),
                color = Color(0xFF999999),
                fontSize = 12.sp,
            )
        }
    }
}

@Composable
fun CallDurationText(durationInSeconds: Long) {
    val hours = durationInSeconds / 3600
    val minutes = (durationInSeconds % 3600) / 60
    val seconds = durationInSeconds % 60

    val durationText = if (hours > 0) {
        String.format("%02d:%02d:%02d", hours, minutes, seconds)
    } else {
        String.format("%02d:%02d", minutes, seconds)
    }

    Text(
        text = durationText, color = Color(0xFF35B244), fontSize = 12.sp
    )
}
