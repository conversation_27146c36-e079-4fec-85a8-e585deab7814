package com.mobile.anchor.app.data.service

import com.bdc.android.library.ktnet.coroutines.Await
import com.mobile.anchor.app.data.model.UserBean
import retrofit2.http.GET
import retrofit2.http.Query

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025/6/9 17:40
 * @description : 好友相关API服务
 */
interface RelationApiService {

    /**
     * 获取好友列表（推荐/关注）
     * @param cursor 分页游标
     * @param size 每页数量
     * @param type 类型：1-推荐，3-关注
     */
    @GET("/api/v1/anchor/homepage")
    suspend fun getRecommendList(
        @Query("cursor") cursor: String = "",
        @Query("size") size: Int = 20,
        @Query("home_page_type") type: Int = 1
    ): Await<PageResponse<UserBean>?>
}