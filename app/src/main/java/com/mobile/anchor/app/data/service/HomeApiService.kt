package com.mobile.anchor.app.data.service

import com.bdc.android.library.ktnet.coroutines.Await
import com.mobile.anchor.app.data.model.DashboardBean
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/6/6 21:36
 * @description :
 */
interface HomeApiService {

    /**
     * 获取工作台数据
     */
    @GET("/api/v1/anchor/dashboard")
    suspend fun getDashboard(): Await<DashboardBean?>

    @GET("/api/v1/anchor/update/match")
    suspend fun updateMatch(): Await<Any?>

    @POST("/api/v1/anchor/update/work_mode")
    suspend fun updateWorkMode(@Body body: RequestBody): Await<Any?>
}