package com.mobile.anchor.app.i18n

import android.content.Context
import com.mobile.anchor.app.data.repository.CommonRepository
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.utils.toJson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hecha<PERSON>
 * @date: 2025/6/5 13:58
 * @description :
 */

data class I18nBean(
    val mapping: Map<String, String>?,
    val default_lang: String?,
    val lang: Map<String, Map<String, String>>,
    val md5: String?
)

object I18nChecker {

    private val repository = CommonRepository()

    suspend fun checkAndUpdateLanguage(context: Context): Boolean {
        return runCatching {
            withContext(Dispatchers.IO) {
                val result = repository.checkI18nInfo(I18nManager.getLangMd5(context))
                result.onSuccess {
                    LogX.d("Language info fetched: $it")
                    I18nManager.saveLangMd5(context, it?.md5 ?: "")
                    it?.let { config ->
                        config.lang.forEach { (langCode, langData) ->
                            I18nManager.saveLangJson(context, langCode, langData.toJson(), 1)
                        }
                    }
                }
                result.isSuccess && result.getOrNull() != null
            }
        }.getOrElse {
            LogX.e("Failed to update language: ${it.message}")
            false
        }
    }
}
