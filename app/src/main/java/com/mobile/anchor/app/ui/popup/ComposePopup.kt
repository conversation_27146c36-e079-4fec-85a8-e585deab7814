package com.mobile.anchor.app.ui.popup

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.text.input.KeyboardType
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.ui.components.BottomSelectDialog
import com.mobile.anchor.app.ui.components.ConfirmDialog
import com.mobile.anchor.app.ui.components.DatePickerDialog
import com.mobile.anchor.app.ui.components.InputDialog
import com.mobile.anchor.app.ui.components.LoadingDialog
import com.mobile.anchor.app.ui.components.UpdateDialog
import com.mobile.anchor.app.ui.components.UpdateProgressDialog
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.update.DownloadState
import com.mobile.anchor.app.update.UpdateState
import java.util.Calendar

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/13 18:08
 * @description : Compose弹框封装，支持在普通Activity中使用
 */
class ComposeDialogFragment : DialogFragment() {

    private var composeContent: (@Composable (dismiss: () -> Unit) -> Unit)? = null
    private var onDismissCallback: (() -> Unit)? = null

    companion object {
        fun newInstance(
            content: @Composable (dismiss: () -> Unit) -> Unit, onDismiss: (() -> Unit)? = null
        ): ComposeDialogFragment {
            return ComposeDialogFragment().apply {
                this.composeContent = content
                this.onDismissCallback = onDismiss
            }
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
                setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            // 取消 decor 限制
            WindowCompat.setDecorFitsSystemWindows(this, false)

            // 设置状态栏透明
            addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            statusBarColor = Color.TRANSPARENT
            navigationBarColor = Color.TRANSPARENT

            // 沉浸式隐藏 system bars（可选）
            WindowInsetsControllerCompat(this, decorView).apply {
                isAppearanceLightStatusBars = false // 白色内容
                isAppearanceLightNavigationBars = false
                hide(WindowInsetsCompat.Type.systemBars())
                systemBarsBehavior =
                    WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, R.style.AnchorDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                AnchorTheme {
                    composeContent?.invoke {
                        // 关闭DialogFragment
                        dismiss()
                    }
                }
            }
        }
    }

    override fun getTheme(): Int {
        return R.style.AnchorDialogTheme
    }

    override fun onDismiss(dialog: android.content.DialogInterface) {
        super.onDismiss(dialog)
        onDismissCallback?.invoke()
    }

    /**
     * 安全显示弹框
     */
    fun showSafely(activity: FragmentActivity, tag: String = "ComposeDialogFragment") {
        if (!activity.isFinishing && !activity.isDestroyed) {
            try {
                show(activity.supportFragmentManager, tag)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}

/**
 * ComposePopup工具类，提供静态方法调用
 */
object ComposePopup {

    /**
     * 显示确认弹框
     */
    @JvmStatic
    fun showConfirmDialog(
        context: Context,
        title: String = context.getString(R.string.reminder),
        content: String = "",
        confirmText: String = context.getString(R.string.rc_confirm),
        cancelText: String = context.getString(R.string.rc_cancel),
        showConfirmButton: Boolean = true,
        showCancelButton: Boolean = true,
        dismissOnBackPress: Boolean = true,
        dismissOnClickOutside: Boolean = true,
        autoDismiss: Boolean = true,
        onConfirm: () -> Unit = {},
        onCancel: () -> Unit = {},
        onDismiss: () -> Unit = {}
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity
        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                var visible by remember { mutableStateOf(true) }

                ConfirmDialog(
                    visible = visible,
                    title = title,
                    content = content,
                    confirmText = confirmText,
                    cancelText = cancelText,
                    showConfirmButton = showConfirmButton,
                    showCancelButton = showCancelButton,
                    dismissOnBackPress = dismissOnBackPress,
                    dismissOnClickOutside = dismissOnClickOutside,
                    onConfirm = {
                        onConfirm()
                        if (autoDismiss) {
                            dismiss()
                        }
                    },
                    onCancel = {
                        onCancel()
                        dismiss()
                    },
                    onDismiss = {
                        if (dismissOnBackPress || dismissOnClickOutside) {
                            onDismiss()
                            dismiss()
                        }
                    })
            }, onDismiss = onDismiss
        )

        activity?.let { dialogFragment.showSafely(it) }
        return dialogFragment
    }

    /**
     * 显示底部选择弹框
     */
    @JvmStatic
    fun showBottomSelectDialog(
        context: Context,
        title: String = "",
        options: List<String>,
        onOptionSelected: (Int, String) -> Unit,
        onDismiss: () -> Unit = {}
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null

        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                var visible by remember { mutableStateOf(true) }

                BottomSelectDialog(
                    visible = visible,
                    title = title,
                    options = options,
                    onOptionSelected = { index, option ->
                        onOptionSelected(index, option)
                        dismiss()
                    },
                    onDismiss = {
                        onDismiss()
                        dismiss()
                    })
            }, onDismiss = onDismiss
        )

        dialogFragment.showSafely(activity)
        return dialogFragment
    }

    /**
     * 显示输入弹框
     */
    @JvmStatic
    fun showInputDialog(
        context: Context,
        title: String = "",
        content: String = "",
        inputValue: String = "",
        inputPlaceholder: String = "",
        inputLabel: String = "",
        maxLength: Int = 0,
        keyboardType: KeyboardType = KeyboardType.Text,
        confirmText: String = "Confirm",
        cancelText: String = "Cancel",
        showCancelButton: Boolean = true,
        onConfirm: (String) -> Unit,
        onCancel: () -> Unit = {},
        onDismiss: () -> Unit = {},
        inputValidator: ((String) -> String?)? = null
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null

        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                var visible by remember { mutableStateOf(true) }
                var currentInputValue by remember { mutableStateOf(inputValue) }

                InputDialog(
                    visible = visible,
                    title = title,
                    content = content,
                    inputValue = currentInputValue,
                    inputPlaceholder = inputPlaceholder,
                    inputLabel = inputLabel,
                    maxLength = maxLength,
                    keyboardType = keyboardType,
                    confirmText = confirmText,
                    cancelText = cancelText,
                    showCancelButton = showCancelButton,
                    onInputChange = { currentInputValue = it },
                    onConfirm = { input ->
                        onConfirm(input)
                        dismiss()
                    },
                    onCancel = {
                        onCancel()
                        dismiss()
                    },
                    onDismiss = {
                        onDismiss()
                        dismiss()
                    },
                    inputValidator = inputValidator
                )
            }, onDismiss = onDismiss
        )

        dialogFragment.showSafely(activity)
        return dialogFragment
    }

    /**
     * 显示日期选择弹框
     */
    @JvmStatic
    fun showDatePickerDialog(
        context: Context,
        title: String = "Select Date",
        initialDate: Calendar? = null,
        onDateSelected: (Calendar) -> Unit,
        onDismiss: () -> Unit = {}
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null

        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                var visible by remember { mutableStateOf(true) }

                DatePickerDialog(
                    visible = visible,
                    title = title,
                    initialDate = initialDate,
                    onDateSelected = { date ->
                        onDateSelected(date)
                        dismiss()
                    },
                    onDismiss = {
                        onDismiss()
                        dismiss()
                    })
            }, onDismiss = onDismiss
        )

        dialogFragment.showSafely(activity)
        return dialogFragment
    }

    /**
     * 显示加载弹框
     */
    @JvmStatic
    fun showLoadingDialog(
        context: Context, message: String = "Loading...", cancelable: Boolean = false
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null

        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                LoadingDialog(
                    visible = true, message = message
                )
            }).apply {
            isCancelable = cancelable
        }

        dialogFragment.showSafely(activity)
        return dialogFragment
    }

    fun showUpdateDialog(
        context: Context,
        updateState: UpdateState,
        dismissOnBackPress: Boolean = true,
        dismissOnClickOutside: Boolean = true,
        onConfirm: () -> Unit = {},
        onCancel: () -> Unit = {},
        onDismiss: () -> Unit = {}
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null

        val dialogFragment = ComposeDialogFragment.newInstance(content = { dismiss ->
            var visible by remember { mutableStateOf(true) }
            val versionInfo = if (updateState is UpdateState.UpdateAvailable) {
                updateState.versionInfo
            } else null
            versionInfo?.let {
                UpdateDialog(visible = visible, versionInfo = versionInfo, onConfirm = {
                    onConfirm()
                    dismiss()
                    onDismiss()
                }, onCancel = {
                    onCancel()
                    dismiss()
                    onDismiss()
                }, onDismiss = {
                    if (dismissOnBackPress || dismissOnClickOutside) {
                        onDismiss()
                        onDismiss()
                    }
                })
            }
        })
        dialogFragment.showSafely(activity)
        return dialogFragment
    }

    fun showUpdateProgressDialog(
        context: Context,
        downloadState: DownloadState,
        dismissOnBackPress: Boolean = true,
        dismissOnClickOutside: Boolean = true,
        onCancel: () -> Unit = {},
        onRetry: () -> Unit = {},
        onDismiss: () -> Unit = {}
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null
        val dialogFragment = ComposeDialogFragment.newInstance(content = { dismiss ->
            var visible by remember { mutableStateOf(true) }
            UpdateProgressDialog(
                visible,
                downloadState,
                dismissOnBackPress,
                dismissOnClickOutside,
                onCancel = {
                    onCancel()
                    onDismiss()
                    dismiss()
                },
                onRetry = {
                    onRetry()
                },
                onDismiss = {
                    onDismiss()
                    dismiss()
                })
        })
        dialogFragment.showSafely(activity)
        return dialogFragment
    }

    /**
     * 显示自定义内容弹框
     */
    @JvmStatic
    fun showCustomDialog(
        context: Context, content: @Composable (dismiss: () -> Unit) -> Unit
    ): ComposeDialogFragment? {
        val activity = context as? FragmentActivity ?: return null

        val dialogFragment = ComposeDialogFragment.newInstance(
            content = { dismiss ->
                content(dismiss)
            })

        dialogFragment.showSafely(activity)
        return dialogFragment
    }
}
