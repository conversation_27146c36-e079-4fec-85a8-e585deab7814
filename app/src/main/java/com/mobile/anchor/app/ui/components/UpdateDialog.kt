package com.mobile.anchor.app.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.VersionBean
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.DisabledColor
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.theme.Surface

/**
 * 应用更新弹框组件
 * 统一的更新提示弹框，支持普通更新和强制更新
 */
@Composable
fun UpdateDialog(
    visible: Boolean,
    versionInfo: VersionBean,
    onDismiss: () -> Unit = {},
    onConfirm: () -> Unit = {},
    onCancel: () -> Unit = onDismiss
) {
    AnimatedVisibility(visible, enter = fadeIn(), exit = fadeOut()) {
        Box(
            modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
        ) {
            // 背景遮罩
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable {
                        if (!versionInfo.isUpdateRequired) {
                            onDismiss()
                        }
                    })

            Dialog(
                onDismissRequest = {
                    if (!versionInfo.isUpdateRequired) {
                        onDismiss()
                    }
                }, properties = DialogProperties(
                    dismissOnBackPress = !versionInfo.isUpdateRequired,
                    dismissOnClickOutside = !versionInfo.isUpdateRequired,
                    usePlatformDefaultWidth = false,
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
                ) {
                    // 弹框内容
                    Card(
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                            .padding(horizontal = 16.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Surface)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 24.dp, start = 24.dp, end = 24.dp, bottom = 10.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // 标题
                            Text(
                                text = stringResource(R.string.update_available_title),
                                color = Color.White,
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            // 版本信息
                            if (versionInfo.version_name.isNotEmpty()) {
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = stringResource(R.string.update_new_version),
                                        color = Color.White.copy(alpha = 0.7f),
                                        fontSize = 14.sp
                                    )
                                    Text(
                                        text = versionInfo.version_name,
                                        color = Primary,
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight.Medium
                                    )
                                }
                                Spacer(modifier = Modifier.height(8.dp))
                            }

                            // 文件大小
                            if (versionInfo.file_size > 0) {
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = stringResource(R.string.update_package_size),
                                        color = Color.White.copy(alpha = 0.7f),
                                        fontSize = 14.sp
                                    )
                                    Text(
                                        text = versionInfo.fileSizeFormatted,
                                        color = Color.White,
                                        fontSize = 14.sp
                                    )
                                }
                                Spacer(modifier = Modifier.height(16.dp))
                            }

                            // 更新内容
                            if (versionInfo.update_tips.isNotEmpty()) {
                                Text(
                                    text = stringResource(R.string.update_content),
                                    color = Color.White.copy(alpha = 0.7f),
                                    fontSize = 14.sp,
                                    modifier = Modifier.fillMaxWidth()
                                )
                                Spacer(modifier = Modifier.height(8.dp))

                                Text(
                                    text = versionInfo.update_tips,
                                    color = Color.White.copy(alpha = 0.9f),
                                    fontSize = 14.sp,
                                    lineHeight = 20.sp,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .verticalScroll(rememberScrollState())
                                )
                            }

                            Spacer(modifier = Modifier.height(24.dp))

                            // 按钮区域
                            when {
                                versionInfo.isUpdateRequired -> {
                                    // 强制更新只显示确认按钮
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.End
                                    ) {
                                        TextButton(onClick = onConfirm) {
                                            Text(
                                                text = stringResource(R.string.update_now),
                                                color = Primary,
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }
                                    }
                                }

                                else -> {
                                    // 普通更新显示两个按钮
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.End
                                    ) {
                                        // 取消按钮
                                        TextButton(onClick = onCancel) {
                                            Text(
                                                text = stringResource(R.string.update_later),
                                                fontSize = 14.sp,
                                                color = DisabledColor,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }

                                        // 确认按钮
                                        TextButton(onClick = onConfirm) {
                                            Text(
                                                text = stringResource(R.string.update_now),
                                                color = Primary,
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun UpdateDialogPreview() {
    AnchorTheme {
        UpdateDialog(
            visible = true, versionInfo = VersionBean(
                update_type = 1,
                version_name = "2.1.0",
                file_size = 25 * 1024 * 1024,
                update_tips = "1. 修复了一些已知问题\n2. 优化了用户体验\n3. 新增了一些功能"
            ), onDismiss = {}, onConfirm = {})
    }
}

@Preview
@Composable
fun ForceUpdateDialogPreview() {
    AnchorTheme {
        UpdateDialog(
            visible = true, versionInfo = VersionBean(
                update_type = 2,
                version_name = "2.1.0",
                file_size = 25 * 1024 * 1024,
                update_tips = "重要安全更新，请立即更新到最新版本"
            ), onDismiss = {}, onConfirm = {})
    }
}
