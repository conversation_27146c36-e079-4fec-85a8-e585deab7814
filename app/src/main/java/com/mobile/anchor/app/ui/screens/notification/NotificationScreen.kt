package com.mobile.anchor.app.ui.screens.notification

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bdc.android.library.extension.finish
import com.mobile.anchor.app.data.model.NotificationBean
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.AsyncImageComponent
import com.mobile.anchor.app.ui.components.StateView
import com.mobile.anchor.app.ui.lce.PageState
import com.mobile.anchor.app.ui.theme.Primary
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged

/**
 * 通知页面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterialApi::class)
@Composable
fun NotificationScreen(
    viewModel: NotificationViewModel = viewModel()
) {
    val context = LocalContext.current

    // 状态收集
    val notificationList by viewModel.notificationList.collectAsState()
    val isRefreshing by viewModel.isRefreshing.collectAsState()
    val isLoadingMore by viewModel.isLoadingMore.collectAsState()
    val viewState by viewModel.viewState.observeAsState(PageState.Default)

    val listState = rememberLazyListState()

    // 首次加载数据
    LaunchedEffect(Unit) {
        if (notificationList.isEmpty()) {
            viewModel.loadNotificationList(isRefresh = true)
        }
    }

    // 监听滚动到底部自动加载更多
    LaunchedEffect(listState, notificationList.size) {
        snapshotFlow { listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }.distinctUntilChanged()
            .collectLatest { index ->
                if (index != null && index >= notificationList.size - 3 && !isLoadingMore && viewModel.canLoadMore()) {
                    viewModel.loadMoreNotifications()
                }
            }
    }

    AnchorScaffold(
        topBar = {
            AnchorTopBar(
                title = "Notifications", onNavigationClick = { context.finish() }
            )
        }) { paddingValues ->

        // 下拉刷新状态
        val pullRefreshState = rememberPullRefreshState(
            refreshing = isRefreshing, onRefresh = { viewModel.refreshNotificationList() })

        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .pullRefresh(pullRefreshState)
        ) {
            when {
                // 显示状态视图（加载中、错误、空状态）
                notificationList.isEmpty() -> {
                    StateView(
                        state = if (isRefreshing) PageState.Loading else viewState,
                        loadingMessage = "Loading notifications...",
                        emptyTitle = "No notifications",
                        emptyMessage = "Notifications will appear here",
                        errorTitle = "Load failed",
                        errorMessage = "Failed to load notifications",
                        onRetry = {
                            viewModel.refreshNotificationList()
                        })
                }
                // 正常列表状态
                else -> {
                    LazyColumn(
                        state = listState,
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        items(notificationList.size) { index ->
                            NotificationItem(
                                notification = notificationList[index], onClick = {
                                    viewModel.markAsRead(notificationList[index].id)
                                })
                        }

                        // 加载更多指示器
                        if (isLoadingMore) {
                            item {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator(
                                        color = Primary, modifier = Modifier.size(24.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }

            // 下拉刷新指示器
            PullRefreshIndicator(
                refreshing = isRefreshing,
                state = pullRefreshState,
                modifier = Modifier.align(Alignment.TopCenter)
            )
        }
    }
}

/**
 * 通知项组件
 */
@Composable
private fun NotificationItem(
    notification: NotificationBean, onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2A2A2A)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 头像
            Box {
                AsyncImageComponent(
                    imageUrl = notification.getHeadFileName() ?: "",
                    modifier = Modifier
                        .size(53.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop,
                    contentDescription = "Avatar"
                )

                // 未读标识
                if (true) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(Primary, CircleShape)
                            .align(Alignment.TopEnd)
                    )
                }
            }

            Spacer(modifier = Modifier.width(12.dp))

            // 内容区域
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 昵称
                Text(
                    text = notification.getNickName(),
                    color = Color.White,
                    fontSize = 15.sp,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                Spacer(modifier = Modifier.height(4.dp))

                // 时间
                Text(
                    text = notification.createTime,
                    color = Color(0xFF999999),
                    fontSize = 14.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                // 如果有内容，显示内容
                if (notification.content.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = notification.content,
                        color = Color(0xFFCCCCCC),
                        fontSize = 13.sp,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }
}
