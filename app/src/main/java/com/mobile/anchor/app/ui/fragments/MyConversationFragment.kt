package com.mobile.anchor.app.ui.fragments

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeVisibleAnimation
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.utils.ToastUtil
import com.mobile.anchor.app.R
import com.mobile.anchor.app.ui.popup.ComposePopup
import com.mobile.anchor.app.ui.viewmodels.UserRequestEvent
import com.mobile.anchor.app.ui.viewmodels.UserViewModel
import com.mobile.anchor.app.utils.Constants
import com.opensource.svgaplayer.SVGAImageView
import io.rong.imkit.conversation.ConversationFragment
import io.rong.imkit.conversation.extension.InputMode
import io.rong.imkit.widget.adapter.BaseAdapter
import io.rong.imkit.widget.adapter.ViewHolder

/**
 * Author:Lxf
 * Create on:2024/9/14
 * Description:
 */
class MyConversationFragment : ConversationFragment() {

    private val viewModel by viewModels<UserViewModel>()
    private var targetId: String? = null

    fun setTargetId(id: String?) {
        this.targetId = id
    }

    private var supportMeView: View? = null

    @SuppressLint("ClickableViewAccessibility")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        supportMeView = view.findViewById<View>(R.id.rl_support_me)
        mRefreshLayout.setOnTouchListener { v, event ->
            closeExtensionPanel()
            false
        }

        mAdapter.setItemClickListener(object : BaseAdapter.OnItemClickListener {
            override fun onItemClick(view: View?, holder: ViewHolder?, position: Int) {
                closeExtensionPanel()
            }

            override fun onItemLongClick(view: View?, holder: ViewHolder?, position: Int): Boolean {
                return false
            }
        })

        initViewEvents()

        supportMeView?.click {
            ComposePopup.showConfirmDialog(
                this.requireContext(), "", getString(R.string.support_me_tips), onConfirm = {
                    viewModel.requestSupportMe(targetId ?: "")
                })
        }
    }

    override fun onResume() {
        super.onResume()
        if (targetId != Constants.RONG_YUN_ID_SYSTEM) {
            viewModel.fetchSupportMeStatus(targetId ?: "")
        }
    }

    private fun initViewEvents() {
        viewModel.pageEvents.observeEvent(this) { event ->
            when (event) {
                is UserRequestEvent.FetchSupportMeStatus -> {
                    supportMeView?.apply {
                        makeVisibleAnimation(event.status.loverShow.isLover == 1)
//                        if (event.status.loverShow.isLover == 1) {
//                            findViewById<SVGAImageView>(R.id.svga)?.startAnimation()
//                        }
                    }
                }

                is UserRequestEvent.FetchUserListFailed -> TODO()
                is UserRequestEvent.RequestSupport -> {
                    ToastUtil.show(getString(R.string.send_successful))
                    supportMeView?.makeVisibleAnimation(event.result)
                }
            }
        }
    }

    private fun closeExtensionPanel() {
        mRongExtensionViewModel?.collapseExtensionBoard()
        val inputMode = mRongExtensionViewModel.inputModeLiveData.value
        if (inputMode == InputMode.EmoticonMode) {
            mRongExtensionViewModel.inputModeLiveData.postValue(InputMode.TextInput)
        }
    }
}