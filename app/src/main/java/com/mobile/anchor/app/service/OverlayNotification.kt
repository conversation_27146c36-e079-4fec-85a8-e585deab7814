package com.mobile.anchor.app.service

import android.app.Activity
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import com.mobile.anchor.app.MainActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.utils.ContextHolder

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/7/19 11:32
 * @description :
 */
object OverlayNotification {

    const val NOTIFICATION_ID = 1
    const val CHANNEL_ID = "overlay_window_channel"

    /**
     * 创建前台服务通知
     */
    fun createNotification(): Notification {
        // 创建通知渠道（Android 8.0及以上需要）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                ContextHolder.context.getString(R.string.anchor_service_running),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = ContextHolder.context.getString(R.string.keep_anchor_service_running)
                setShowBadge(false)
            }

            val notificationManager =
                ContextHolder.context.getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }

        // 创建打开应用的PendingIntent
        val pendingIntent = PendingIntent.getActivity(
            ContextHolder.context,
            0,
            Intent(ContextHolder.context, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        // 构建通知
        return NotificationCompat.Builder(ContextHolder.context, CHANNEL_ID)
            .setContentTitle(ContextHolder.context.getString(R.string.anchor_service_running))
            .setContentText(ContextHolder.context.getString(R.string.click_to_return_to_the_application))
            .setSmallIcon(R.mipmap.ic_launcher).setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW).setCategory(Notification.CATEGORY_SERVICE)
            .build()
    }

    fun notify(activity: Activity) {
        // 创建打开通话界面的PendingIntent
        val callIntent = Intent(ContextHolder.context, activity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val callPendingIntent = PendingIntent.getActivity(
            ContextHolder.context,
            0,
            callIntent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        val updatedNotification = NotificationCompat.Builder(ContextHolder.context, CHANNEL_ID)
            .setContentTitle(ContextHolder.context.getString(R.string.calling))
            .setContentText(ContextHolder.context.getString(R.string.click_to_return_to_the_application))
            .setSmallIcon(R.mipmap.ic_launcher).setContentIntent(callPendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(Notification.CATEGORY_CALL) // 如果是通话推荐改成这个
            .build()

        val notificationManager =
            ContextHolder.context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, updatedNotification)
    }

    fun reset() {

    }
}