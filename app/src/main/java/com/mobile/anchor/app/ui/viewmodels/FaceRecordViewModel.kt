package com.mobile.anchor.app.ui.viewmodels

import android.content.Context
import android.net.Uri
import androidx.camera.view.PreviewView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.viewModelScope
import com.mobile.anchor.app.data.model.FaceRecordUiState
import com.mobile.anchor.app.data.model.VideoRecordState
import com.mobile.anchor.app.data.network.ApiResult
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.BodyParams
import com.mobile.anchor.app.data.service.UserApiService
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.manager.FileUploadManager
import com.mobile.anchor.app.utils.CameraXVideoRecorder
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 人脸录制ViewModel
 * 管理录制状态、相机控制、文件上传等功能
 */
class FaceRecordViewModel : BaseViewModel() {

    private val userApiService: UserApiService by NetDelegates()

    private var videoRecorder: CameraXVideoRecorder? = null
    private var currentVideoUri: Uri? = null
    private var videoPositionUpdaterJob: Job? = null

    // UI状态
    private val _uiState = MutableStateFlow(FaceRecordUiState())
    val uiState: StateFlow<FaceRecordUiState> = _uiState.asStateFlow()

    /**
     * 初始化相机录制器
     */
    fun initializeCamera(context: Context, lifecycleOwner: LifecycleOwner) {
        videoRecorder = CameraXVideoRecorder(context, lifecycleOwner)

        // 检查权限
        val hasPermission = videoRecorder?.hasPermissions() ?: false
        _uiState.value = _uiState.value.copy(hasPermission = hasPermission)

        // 如果有权限，立即设置相机预览
        if (hasPermission) {
            LogX.d("权限已授予，准备设置相机预览")
        }
    }

    /**
     * 获取所需权限
     */
    fun getRequiredPermissions(): Array<String> {
        return videoRecorder?.getRequiredPermissions() ?: emptyArray()
    }

    /**
     * 权限授予后的回调
     */
    fun onPermissionsGranted() {
        _uiState.value = _uiState.value.copy(hasPermission = true)
        LogX.d("权限已授予，更新UI状态")
    }

    /**
     * 设置相机预览
     */
    fun setupCameraPreview(previewView: PreviewView) {
        viewModelScope.launch {
            try {
                videoRecorder?.setupCamera(previewView)
                LogX.d("相机预览设置成功")
            } catch (e: Exception) {
                LogX.e("相机预览设置失败", e)
                _uiState.value = _uiState.value.copy(
                    errorMessage = "相机初始化失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 开始录制
     */
    fun startRecording() {
        videoRecorder?.startRecording(onRecordingStarted = { uri ->
            currentVideoUri = uri
            _uiState.value = _uiState.value.copy(
                recordState = VideoRecordState.Recording(), errorMessage = null
            )

            // 监听录制时长
            observeRecordingDuration()
        }, onRecordingFinished = { finalUri ->
            // 更新为最终的视频URI
            currentVideoUri = finalUri
            val duration = getCurrentRecordingDuration()
            LogX.d("录制完成，最终URI: $finalUri, 时长: $duration")

            // 延迟一下确保文件完全写入
            viewModelScope.launch {
                kotlinx.coroutines.delay(500)
                _uiState.value = _uiState.value.copy(
                    recordState = VideoRecordState.RecordCompleted(finalUri, duration)
                )
            }
        }, onError = { error ->
            _uiState.value = _uiState.value.copy(
                errorMessage = error, recordState = VideoRecordState.BeforeRecord
            )
        })
    }

    /**
     * 停止录制
     */
    fun stopRecording() {
        val duration = getCurrentRecordingDuration()

        if (duration < 10) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "录制时间不能少于10秒"
            )
            return
        }

        // 只停止录制，状态切换由录制完成回调处理
        videoRecorder?.stopRecording()
    }

    /**
     * 暂停录制
     */
    fun pauseRecording() {
        videoRecorder?.pauseRecording()
    }

    /**
     * 恢复录制
     */
    fun resumeRecording() {
        videoRecorder?.resumeRecording()
    }

    /**
     * 重新开始录制
     */
    fun restartRecording() {
        currentVideoUri = null
        _uiState.value = _uiState.value.copy(
            recordState = VideoRecordState.BeforeRecord, errorMessage = null
        )
    }

    /**
     * 上传录制的视频
     */
    fun uploadVideo(context: Context) {
        val videoUri = currentVideoUri ?: return

        _uiState.value = _uiState.value.copy(isUploading = true, uploadProgress = 0f)

        viewModelScope.launch {
            FileUploadManager.upload(context, videoUri, fileType = FileUploadManager.FileType.VIDEO)
                .collect { result ->
                    when (result) {
                        is ApiResult.Loading -> {
                            // 可以在这里更新上传进度，如果API支持的话
                        }

                        is ApiResult.Success -> {
                            result.getDataOrNull().let { data ->
                                userApiService.updateProfile(
                                    BodyParams.updateProfileBody(
                                        avatar = DataStoreManager.getUserObject()?.avatar ?: "",
                                        birthdayAt = DataStoreManager.getUserObject()?.birthday_at?.toLong()
                                            ?: 0L,
                                        gender = DataStoreManager.getUserObject()?.gender ?: 0,
                                        nickname = DataStoreManager.getUserObject()?.nickname ?: "",
                                        firebaseToken = "",
                                        videoFile = data?.accessUrl ?: "",
                                        updateType = 0
                                    )
                                ).await()
                            }
                            _uiState.value = _uiState.value.copy(
                                isUploading = false, uploadProgress = 1f, completed = true
                            )
                            LogX.d("视频上传成功: ${result.data}")
                            // 可以在这里处理上传成功的逻辑，比如导航到其他页面
                        }

                        is ApiResult.Error -> {
                            _uiState.value = _uiState.value.copy(
                                isUploading = false,
                                uploadProgress = 0f,
                                errorMessage = result.message ?: "上传失败"
                            )
                            LogX.e("视频上传失败: ${result.message}")
                        }
                    }
                }
        }
    }

    /**
     * 播放录制的视频
     */
    fun playVideo() {
        val currentState = _uiState.value.recordState
        if (currentState is VideoRecordState.RecordCompleted) {
            _uiState.value = _uiState.value.copy(
                recordState = currentState.copy(isPlaying = true)
            )
            // 开始更新播放位置
            startVideoPositionUpdater()
        }
    }

    /**
     * 暂停视频播放
     */
    fun pauseVideo() {
        val currentState = _uiState.value.recordState
        if (currentState is VideoRecordState.RecordCompleted) {
            _uiState.value = _uiState.value.copy(
                recordState = currentState.copy(isPlaying = false)
            )
            // 停止更新播放位置
            stopVideoPositionUpdater()
        }
    }

    /**
     * 停止视频播放
     */
    fun stopVideo() {
        val currentState = _uiState.value.recordState
        if (currentState is VideoRecordState.RecordCompleted) {
            _uiState.value = _uiState.value.copy(
                recordState = currentState.copy(
                    isPlaying = false, currentPlayPosition = 0
                )
            )
        }
    }

    /**
     * 更新视频播放位置
     */
    fun updateVideoPosition(position: Int) {
        val currentState = _uiState.value.recordState
        if (currentState is VideoRecordState.RecordCompleted) {
            _uiState.value = _uiState.value.copy(
                recordState = currentState.copy(currentPlayPosition = position)
            )
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    /**
     * 监听录制时长
     */
    private fun observeRecordingDuration() {
        viewModelScope.launch {
            videoRecorder?.recordingDuration?.collect { duration ->
                val currentState = _uiState.value.recordState
                if (currentState is VideoRecordState.Recording) {
                    _uiState.value = _uiState.value.copy(
                        recordState = currentState.copy(duration = duration)
                    )
                }
            }
        }

        // 监听暂停状态
        viewModelScope.launch {
            videoRecorder?.isPaused?.collect { isPaused ->
                val currentState = _uiState.value.recordState
                if (currentState is VideoRecordState.Recording) {
                    _uiState.value = _uiState.value.copy(
                        recordState = currentState.copy(isPaused = isPaused)
                    )
                }
            }
        }
    }

    /**
     * 开始视频位置更新器
     */
    private fun startVideoPositionUpdater() {
        stopVideoPositionUpdater()
        videoPositionUpdaterJob = viewModelScope.launch {
            while (true) {
                val currentState = _uiState.value.recordState
                if (currentState is VideoRecordState.RecordCompleted && currentState.isPlaying) {
                    val newPosition = (currentState.currentPlayPosition + 1) % currentState.duration
                    _uiState.value = _uiState.value.copy(
                        recordState = currentState.copy(currentPlayPosition = newPosition)
                    )
                    delay(1000)
                } else {
                    break
                }
            }
        }
    }

    /**
     * 停止视频位置更新器
     */
    private fun stopVideoPositionUpdater() {
        videoPositionUpdaterJob?.cancel()
        videoPositionUpdaterJob = null
    }

    /**
     * 获取当前录制时长
     */
    private fun getCurrentRecordingDuration(): Int {
        return when (val state = _uiState.value.recordState) {
            is VideoRecordState.Recording -> state.duration
            else -> 0
        }
    }

    /**
     * 释放资源
     */
    override fun onCleared() {
        super.onCleared()
        stopVideoPositionUpdater()
        videoRecorder?.release()
    }
}
