package com.mobile.anchor.app.ui.viewmodels

import androidx.lifecycle.viewModelScope
import com.bdc.android.library.ktnet.tryAwait
import com.mobile.anchor.app.data.model.BankBean
import com.mobile.anchor.app.data.model.ReferralProfitBean
import com.mobile.anchor.app.data.model.RevenueBean
import com.mobile.anchor.app.data.model.SettlementBean
import com.mobile.anchor.app.data.model.WalletBean
import com.mobile.anchor.app.data.model.WalletCurrencyBean
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.BodyParams
import com.mobile.anchor.app.data.service.UserApiService
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.lce.PageState
import com.mobile.anchor.app.utils.Constants
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale


/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/13 11:18
 * @description :
 */
class WalletViewModel : BaseViewModel() {

    private val userApiService: UserApiService by NetDelegates()

    private val _uiState = MutableStateFlow(WalletUiState())
    val uiState: StateFlow<WalletUiState> = _uiState.asStateFlow()

    private val _walletInfo = MutableStateFlow<WalletBean?>(null)
    val walletInfo: StateFlow<WalletBean?> = _walletInfo.asStateFlow()

    private val _bankList = MutableStateFlow<List<BankBean>>(emptyList())
    val bankList: StateFlow<List<BankBean>> = _bankList.asStateFlow()
    private val _bankInfo = MutableStateFlow<BankBean?>(null)
    val bankInfo: StateFlow<BankBean?> = _bankInfo.asStateFlow()

    private val _walletCurrencyBean = MutableStateFlow<WalletCurrencyBean?>(null)
    val walletCurrencyBean: StateFlow<WalletCurrencyBean?> = _walletCurrencyBean.asStateFlow()

    // 验证码相关状态
    private val _verificationCode = MutableStateFlow("")
    val verificationCode: StateFlow<String> = _verificationCode.asStateFlow()

    private val _isCodeSending = MutableStateFlow(false)
    val isCodeSending: StateFlow<Boolean> = _isCodeSending.asStateFlow()

    private val _timeRemaining = MutableStateFlow(0)
    val timeRemaining: StateFlow<Int> = _timeRemaining.asStateFlow()

    private val _canResendCode = MutableStateFlow(true)
    val canResendCode: StateFlow<Boolean> = _canResendCode.asStateFlow()

    // 倒计时任务
    private var countDownJob: Job? = null

    // Balance标签页 - 收入记录相关状态
    private val _revenueList = MutableStateFlow<List<RevenueBean>>(emptyList())
    val revenueList: StateFlow<List<RevenueBean>> = _revenueList.asStateFlow()

    // Settlement标签页 - 结算记录相关状态
    private val _settlementList = MutableStateFlow<List<SettlementBean>>(emptyList())
    val settlementList: StateFlow<List<SettlementBean>> = _settlementList.asStateFlow()

    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing.asStateFlow()

    private val _isLoadingMore = MutableStateFlow(false)
    val isLoadingMore: StateFlow<Boolean> = _isLoadingMore.asStateFlow()

    private val _selectedPeriod = MutableStateFlow(7) // 7天或30天
    val selectedPeriod: StateFlow<Int> = _selectedPeriod.asStateFlow()

    // 货币设置
    private val _currencyList = MutableStateFlow<List<WalletCurrencyBean>>(emptyList())
    val currencyList: StateFlow<List<WalletCurrencyBean>> = _currencyList.asStateFlow()

    private val _currentCurrency = MutableStateFlow(Constants.Currency.DEFAULT)
    val currentCurrency: StateFlow<String> = _currentCurrency.asStateFlow()

    // 分页相关
    private var revenueCursor = ""
    private var settlementCursor = ""
    private var revenueHasMore = true
    private var settlementHasMore = true

    private val _referralProfitList = MutableStateFlow<List<ReferralProfitBean>>(emptyList())
    val referralProfitList: StateFlow<List<ReferralProfitBean>> = _referralProfitList.asStateFlow()
    private val _referralProfitCursor = MutableStateFlow("")

    fun getWalletInfo() {
        ktHttpRequest {
            val response = userApiService.getWalletInfo().await()
            response?.let {
                LogX.d("获取钱包信息成功")
                _walletInfo.value = it
            }
        }
    }

    fun getBankList() {
        ktHttpRequest {
            val response = userApiService.getBankList().await()
            response?.let {
                LogX.d("获取银行列表成功")
                _bankList.value = it
            }
        }
    }

    fun getBankInfo() {
        ktHttpRequest {
            val response = userApiService.getBankInfo(DataStoreManager.getUserId() ?: "").await()
            response?.let {
                LogX.d("获取银行信息成功")
                _bankInfo.value = it
            }
        }
    }

    fun bindBank(bankId: Int, cardNum: String, realName: String, verificationCode: String) {
        ktHttpRequest {
            userApiService.bindBank(
                BodyParams.bindBankBody(
                    bankId = bankId, cardNum = cardNum, realName = realName, code = verificationCode
                )
            ).tryAwait {
                _uiState.value = _uiState.value.copy(bindBankState = PageState.Error(it))
            }

            _uiState.value = _uiState.value.copy(bindBankState = PageState.Success)
            DataStoreManager.updateUserObject { currentUser ->
                currentUser?.copy(bank_id = bankId)
            }
            LogX.d("绑定银行卡成功")
        }
    }

    /**
     * 更新验证码输入
     */
    fun updateVerificationCode(code: String) {
        _verificationCode.value = code
    }

    /**
     * 获取验证码
     */
    fun getVerificationCode() {
        val userId = DataStoreManager.getUserId() ?: ""
        if (userId.isEmpty()) {
            LogX.e("用户ID为空，无法获取验证码")
            return
        }

        _isCodeSending.value = true
        LogX.d("开始获取验证码")

        ktHttpRequest {
            val response = userApiService.getEmailCode(
                BodyParams.codeBody(
                    email = DataStoreManager.getUserObject()?.login_email ?: "" // 使用用户ID作为邮箱
                )
            ).await()
            response?.let { data ->
                LogX.d("获取验证码成功")
                _isCodeSending.value = false
                _timeRemaining.value = 60 // 60秒倒计时
                _canResendCode.value = false
                startCountdown()
            }
        }
    }

    /**
     * 开始倒计时
     */
    private fun startCountdown() {
        // 取消之前的倒计时
        countDownJob?.cancel()
        countDownJob = viewModelScope.launch {
            var currentTime = _timeRemaining.value
            while (currentTime > 0) {
                delay(1000)
                currentTime -= 1
                _timeRemaining.value = currentTime
                _canResendCode.value = currentTime <= 0
            }
        }
    }

    fun getSettlementList() {
        ktHttpRequest {
            val response = userApiService.getSettlementList().await()
            response?.let {
                LogX.d("获取结算列表成功")
            }
        }
    }

    fun withdraw(coin: Int) {
        ktHttpRequest {
            val response =
                userApiService.applyWithdraw(BodyParams.applyWithdrawBody(coin)).tryAwait {
                    _uiState.value = _uiState.value.copy(withdrawState = PageState.Error(it))
                }
            response?.let {
                LogX.d("提现成功")
                _uiState.value = _uiState.value.copy(withdrawState = PageState.Success)
            }
        }
    }

    /**
     * 切换时间周期
     */
    fun switchPeriod(days: Int) {
        if (_selectedPeriod.value != days) {
            _selectedPeriod.value = days
            loadRevenueList(isRefresh = true)
        }
    }

    /**
     * 根据天数计算时间范围
     * @param days 天数（7或30）
     * @return Pair<开始时间戳(秒), 结束时间戳(秒)>
     */
    private fun calculateTimeRange(days: Int): Pair<Long, Long> {
        val calendar = Calendar.getInstance()
        val endTime = calendar.timeInMillis // 当前时间（毫秒）

        // 往前推指定天数
        calendar.add(Calendar.DAY_OF_MONTH, -days)
        val startTime = calendar.timeInMillis// 开始时间（毫秒）

        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        LogX.d("开始时间: ${dateFormat.format(Date(startTime * 1000))}")
        LogX.d("结束时间: ${dateFormat.format(Date(endTime * 1000))}")

        return Pair(startTime, endTime)
    }

    /**
     * 加载收入记录列表 (Balance标签页)
     */
    fun loadRevenueList(
        isRefresh: Boolean = false
    ) {
        if (isRefresh) {
            revenueCursor = ""
            revenueHasMore = true
            _isRefreshing.value = true
        } else {
            if (!revenueHasMore || _isLoadingMore.value) return
            _isLoadingMore.value = true
        }

        // 根据选择的周期计算时间范围
        val (startTime, endTime) = calculateTimeRange(_selectedPeriod.value)

        ktHttpRequest {
            val response = userApiService.getRevenueList(
                startTime, endTime, cursor = revenueCursor, size = Constants.GLOBAL_PAGE_SIZE
            ).await()

            response?.let { data ->
                val newList = data.records ?: emptyList()
                if (isRefresh) {
                    _revenueList.value = newList
                } else {
                    _revenueList.value = _revenueList.value + newList
                }

                if (newList.isEmpty()) {
                    LogX.d("获取收入记录成功，但列表为空")
                }

                revenueCursor = data.cursor
                revenueHasMore = newList.size >= Constants.GLOBAL_PAGE_SIZE

                if (_revenueList.value.isNullOrEmpty()) {
                    viewModelScope.launch {
                        delay(500) // 等待状态更新
                        setupEmptyState()
                    }
                }
                LogX.d("获取收入记录成功，共${newList.size}条")
            } ?: run {
                LogX.e("获取收入记录失败")
            }

            if (isRefresh) {
                _isRefreshing.value = false
            } else {
                _isLoadingMore.value = false
            }
        }
    }

    /**
     * 加载结算记录列表 (Settlement标签页)
     */
    fun loadSettlementList(isRefresh: Boolean = false) {
        if (isRefresh) {
            settlementCursor = ""
            settlementHasMore = true
            _isRefreshing.value = true
        } else {
            if (!settlementHasMore || _isLoadingMore.value) return
            _isLoadingMore.value = true
        }

        ktHttpRequest {
            val response = userApiService.getSettlementList(
            ).await()

            response?.let {
                if (isRefresh) {
                    _settlementList.value = response.records ?: emptyList()
                } else {
                    _settlementList.value =
                        _settlementList.value + (response.records ?: emptyList())
                }

                settlementCursor = response.cursor
                settlementHasMore = (response.records?.size ?: 0) >= Constants.GLOBAL_PAGE_SIZE

                if (_settlementList.value.isNullOrEmpty()) {
                    viewModelScope.launch {
                        delay(500) // 等待状态更新
                        setupEmptyState()
                    }
                }
                LogX.d("获取结算记录成功，共${response.records?.size}条")
            }
            if (isRefresh) {
                _isRefreshing.value = false
            } else {
                _isLoadingMore.value = false
            }
        }
    }

    /**
     * 刷新收入记录
     */
    fun refreshRevenueList() {
        loadRevenueList(isRefresh = true)
    }

    /**
     * 加载更多收入记录
     */
    fun loadMoreRevenue() {
        loadRevenueList(isRefresh = false)
    }

    /**
     * 刷新结算记录
     */
    fun refreshSettlementList() {
        loadSettlementList(isRefresh = true)
    }

    /**
     * 加载更多结算记录
     */
    fun loadMoreSettlement() {
        loadSettlementList(isRefresh = false)
    }

    /**
     * 根据标签页索引加载对应数据
     */
    fun loadDataForTab(tabIndex: Int) {
        when (tabIndex) {
            0 -> loadRevenueList(isRefresh = true)
            1 -> loadSettlementList(isRefresh = true)
        }
    }


    fun getWithdrawInfo() {
        ktHttpRequest {
            val response = userApiService.getWithdrawInfo().await()
            response?.let {
                LogX.d("获取提现信息成功")
                _walletCurrencyBean.value = it
                _currentCurrency.value = it.currency
            }
        }
    }

    // ==================== 货币设置相关方法 ====================

    /**
     * 加载当前货币设置
     */
    fun loadCurrentCurrency() {
        viewModelScope.launch {
            DataStoreManager.getCurrencyFlow().collect { currency ->
                _currentCurrency.value = currency
            }
        }
    }

    /**
     * 切换货币
     */
    fun switchCurrency(currency: WalletCurrencyBean) {
        viewModelScope.launch {
            DataStoreManager.saveCurrency(currency.currency)
            _walletCurrencyBean.update {
                it?.copy(
                    currency = currency.currency, to_currency_money = currency.to_currency_money
                )
            }
            _currentCurrency.value = currency.currency

            userApiService.setCurrency(
                BodyParams.setupCurrencyBody(currency.currency)
            ).tryAwait {
                LogX.e("切换货币成功: ${it}")
            }
        }
    }

    /**
     * 获取支持的货币列表
     */
    fun getSupportedCurrencies() {
        ktHttpRequest {
            val response = userApiService.getCurrencyList().await()
            response?.let {
                LogX.d("获取支持的货币列表成功")
                _currencyList.value = it.list_v2 ?: emptyList()
            }
        }
    }

    /**
     * 获取货币显示名称
     */
    fun getCurrencyDisplayName(currency: String): String {
        return Constants.Currency.getDisplayName(currency)
    }

    /**
     * 获取货币符号
     */
    fun getCurrencySymbol(currency: String): String {
        return Constants.Currency.getSymbol(currency)
    }

    fun getReferralProfitList(refresh: Boolean = true) {
        if (refresh) {
            _referralProfitCursor.value = ""
        }
        _isRefreshing.value = refresh
        _isLoadingMore.value = !refresh
        ktHttpRequest {
            val response = userApiService.getReferralProfitList(
                cursor = _referralProfitCursor.value, size = Constants.GLOBAL_PAGE_SIZE
            ).tryAwait {
                LogX.e("获取推荐收益失败: $it")
            }
            response?.let {
                LogX.d("获取推荐收益成功")
                _isRefreshing.value = false
                _isLoadingMore.value = false
                if (refresh) {
                    _referralProfitList.value = it.records ?: emptyList()
                } else {
                    _referralProfitList.value =
                        _referralProfitList.value + (it.records ?: emptyList())
                }
                _referralProfitCursor.value = it.cursor
            }
        }
    }

    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        countDownJob?.cancel()
    }
}

data class WalletUiState(
    val isRefreshing: Boolean = false,
    val isLoadingMore: Boolean = false,
    val selectedPeriod: Int = 7,
    val revenueList: List<RevenueBean> = emptyList(),
    val settlementList: List<SettlementBean> = emptyList(),
    val bindBankState: PageState = PageState.Default,
    val withdrawState: PageState = PageState.Default,
    val currencyList: List<WalletCurrencyBean> = emptyList(),
    val currentCurrency: String = Constants.Currency.DEFAULT,
    val viewState: PageState = PageState.Default
)

