package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

/**
 * 钱包交易记录数据模型
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class WalletTransactionBean(
    @Json(name = "id") val id: String = "",
    @<PERSON><PERSON>(name = "changeType") val changeType: Int = 0, // 变更类型：0-充值，1-通话消费，2-礼物消费，3-提现等
    @<PERSON><PERSON>(name = "afterNum") val afterNum: Int = 0, // 变更后余额
    @<PERSON><PERSON>(name = "beforeNum") val beforeNum: Int = 0, // 变更前余额
    @Json(name = "changeNum") val changeNum: Int = 0, // 变更数量
    @Json(name = "anchorId") val anchorId: String = "",
    @<PERSON><PERSON>(name = "anchorNickName") val anchorNickName: String = "",
    @<PERSON>son(name = "userCode") val userCode: String = "",
    @Json(name = "message") val message: String = "",
    @<PERSON><PERSON>(name = "createTime") val createTime: String = "",
    @<PERSON>son(name = "updateTime") val updateTime: String = ""
) : Parcelable {
    
    /**
     * 获取交易类型描述
     */
    fun getTransactionTypeDesc(): String {
        return when (changeType) {
            0 -> "Recharge"
            1 -> "Video Call"
            2 -> "Gift"
            3 -> "Withdraw"
            else -> "Other"
        }
    }
    
    /**
     * 是否为收入
     */
    val isIncome: Boolean get() = changeNum > 0
    
    /**
     * 是否为支出
     */
    val isExpense: Boolean get() = changeNum < 0
}
