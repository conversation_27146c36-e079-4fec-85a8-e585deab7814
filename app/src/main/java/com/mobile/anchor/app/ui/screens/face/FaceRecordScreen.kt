package com.mobile.anchor.app.ui.screens.face

import android.widget.VideoView
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bdc.android.library.extension.jumpThenFinish
import com.mobile.anchor.app.MainActivity
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.FaceRecordUiState
import com.mobile.anchor.app.data.model.VideoRecordState.BeforeRecord
import com.mobile.anchor.app.data.model.VideoRecordState.RecordCompleted
import com.mobile.anchor.app.data.model.VideoRecordState.Recording
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.activities.BankBindActivity
import com.mobile.anchor.app.ui.components.AnchorButton
import com.mobile.anchor.app.ui.components.AnchorOutlinedButton
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.viewmodels.FaceRecordViewModel
import kotlinx.coroutines.delay

/**
 * 人脸录制页面
 * 支持录制前预览、录制中控制、录制后操作三个状态
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FaceRecordScreen(
    viewModel: FaceRecordViewModel = viewModel()
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val uiState by viewModel.uiState.collectAsState()

    // 权限请求
    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            viewModel.onPermissionsGranted()
        }
    }

    // 初始化相机和权限检查
    LaunchedEffect(Unit) {
        viewModel.initializeCamera(context, lifecycleOwner)
        if (!uiState.hasPermission) {
            permissionLauncher.launch(viewModel.getRequiredPermissions())
        }
    }

    // 权限获得后自动设置相机预览
    LaunchedEffect(uiState.hasPermission) {
        if (uiState.hasPermission) {
            // 延迟一下确保相机初始化完成
            delay(500)
        }
    }

    // 错误提示
    uiState.errorMessage?.let { error ->
        LaunchedEffect(error) {
            // 可以显示Toast或Snackbar
            delay(3000)
            viewModel.clearError()
        }
    }

    uiState.completed?.let { completed ->
        LaunchedEffect(completed) {
            if (completed) {
                DataStoreManager.getUserObject()?.bank_id?.let {
                    if (it <= 0) {
                        context.jumpThenFinish(BankBindActivity::class.java)
                    } else {
                        context.jumpThenFinish(MainActivity::class.java)
                    }
                }
            }
        }
    }

    FaceRecordScreenContent(
        uiState,
        viewModel::setupCameraPreview,
        viewModel::startRecording,
        viewModel::pauseRecording,
        viewModel::resumeRecording,
        viewModel::stopRecording,
        viewModel::restartRecording,
        { viewModel.uploadVideo(context) },
        viewModel::playVideo,
        viewModel::pauseVideo,
        viewModel::stopVideo,
    )

}


@Composable
private fun FaceRecordScreenContent(
    uiState: FaceRecordUiState,
    setupCameraPreview: (PreviewView) -> Unit = {},
    startRecording: () -> Unit = {},
    pauseRecording: () -> Unit = {},
    resumeRecording: () -> Unit = {},
    stopRecording: () -> Unit = {},
    restartRecording: () -> Unit = {},
    uploadVideo: () -> Unit = {},
    playVideo: () -> Unit = {},
    pauseVideo: () -> Unit = {},
    stopVideo: () -> Unit = {}
) {
    AnchorScaffold(
        topBar = {
            AnchorTopBar(
                title = stringResource(R.string.facial_recording)
            )
        }, containerColor = Color.Transparent
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding()
        ) {
            // 根据录制状态显示不同的背景
            when (val state = uiState.recordState) {
                is RecordCompleted -> {
                    // 录制完成后显示视频播放器
                    VideoPlayerView(
                        modifier = Modifier.fillMaxSize(),
                        videoUri = state.videoUri,
                        isPlaying = state.isPlaying,
                        onPlayPause = { if (state.isPlaying) pauseVideo() else playVideo() })
                }

                else -> {
                    // 其他状态显示相机预览
                    CameraPreviewView(
                        modifier = Modifier.fillMaxSize(),
                        hasPermission = uiState.hasPermission,
                        onPreviewReady = { previewView ->
                            setupCameraPreview(previewView)
                        })
                }
            }

            // 根据录制状态显示不同的UI
            when (val state = uiState.recordState) {
                is BeforeRecord -> {
                    BeforeRecordState(
                        modifier = Modifier.align(Alignment.BottomCenter), onStartRecord = {
                            startRecording()
                        })
                }

                is Recording -> {
                    RecordingState(
                        modifier = Modifier.align(Alignment.BottomCenter),
                        duration = state.duration,
                        isPaused = state.isPaused,
                        onPause = { pauseRecording() },
                        onResume = { resumeRecording() },
                        onStop = { stopRecording() })
                }

                is RecordCompleted -> {
                    // 播放按钮放在中央
                    PlayButton(
                        modifier = Modifier.align(Alignment.Center),
                        isPlaying = state.isPlaying,
                        onPlayPause = { if (state.isPlaying) pauseVideo() else playVideo() })

                    // 控制按钮和信息放在底部
                    RecordCompletedState(
                        modifier = Modifier.align(Alignment.BottomCenter),
                        duration = state.duration,
                        currentPosition = state.currentPlayPosition,
                        onCancel = { restartRecording() },
                        onUpload = {
                            uploadVideo()
                        },
                        isUploading = uiState.isUploading
                    )
                }
            }

            // 错误提示
            uiState.errorMessage?.let { error ->
                Card(
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.Red.copy(alpha = 0.9f))
                ) {
                    Text(
                        text = error,
                        modifier = Modifier.padding(16.dp),
                        color = Color.White,
                        fontSize = 14.sp
                    )
                }
            }
        }
    }
}

/**
 * 视频播放器组件
 */
@Composable
private fun VideoPlayerView(
    modifier: Modifier = Modifier,
    videoUri: android.net.Uri,
    isPlaying: Boolean,
    onPlayPause: () -> Unit
) {
    AndroidView(
        factory = { context ->
        VideoView(context).apply {
            // 设置错误监听器
            setOnErrorListener { _, what, extra ->
                LogX.e(
                    "VideoPlayerView", "播放错误: what=$what, extra=$extra, uri=$videoUri"
                )
                false // 返回false让系统处理错误
            }

            // 设置准备监听器
            setOnPreparedListener { mediaPlayer ->
                mediaPlayer.isLooping = true

                // 先跳到第一帧显示缩略图
                seekTo(1)

                if (isPlaying) {
                    start()
                } else {
                    // 如果不播放，暂停在第一帧作为缩略图
                    pause()
                }
            }

            // 设置完成监听器
            setOnCompletionListener {
                LogX.d("VideoPlayerView", "视频播放完成")
            }

            // 设置点击监听器
            setOnClickListener {
                onPlayPause()
            }

            // 设置视频URI并准备显示第一帧
            LogX.d("VideoPlayerView", "设置视频URI: $videoUri")
            setVideoURI(videoUri)

            // 请求焦点以确保能够显示视频帧
            requestFocus()
        }
    }, update = { videoView ->
        if (isPlaying && !videoView.isPlaying) {
            LogX.d("VideoPlayerView", "开始播放")
            videoView.start()
        } else if (!isPlaying && videoView.isPlaying) {
            LogX.d("VideoPlayerView", "暂停播放")
            videoView.pause()
            // 暂停时保持在当前帧，不回到开头
        }
    }, modifier = modifier
    )
}

/**
 * 相机预览组件
 */
@Composable
private fun CameraPreviewView(
    modifier: Modifier = Modifier, hasPermission: Boolean, onPreviewReady: (PreviewView) -> Unit
) {
    if (hasPermission) {
        AndroidView(
            factory = { context ->
                PreviewView(context).apply {
                    scaleType = PreviewView.ScaleType.FILL_CENTER
                    onPreviewReady(this)
                }
            }, modifier = modifier
        )
    } else {
        // 权限未授予时显示黑色背景
        Box(
            modifier = modifier.background(Color.Black), contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.camera_permission_is_required_to_use_recording_function), color = Color.White, fontSize = 16.sp
            )
        }
    }
}

/**
 * 录制前UI - 显示录制按钮和提示文字
 */
@Composable
private fun BeforeRecordState(
    modifier: Modifier = Modifier, onStartRecord: () -> Unit
) {
    Column(
        modifier = modifier.padding(bottom = 50.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        // 提示文字
        Column(
            horizontalAlignment = Alignment.Start, verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = stringResource(R.string.record_video_reminder),
                color = Color.Red,
                fontSize = 14.sp,
                modifier = Modifier
                    .background(
                        Color.Black.copy(alpha = 0.6f), RoundedCornerShape(8.dp)
                    )
                    .padding(horizontal = 16.dp, vertical = 6.dp)
            )
        }

        // 录制按钮 - 根据效果图样式
        IconButton(
            onClick = onStartRecord,
            modifier = Modifier
                .size(80.dp)
                .background(Primary, CircleShape)
        ) {
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .background(Color.White, CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .size(50.dp)
                        .background(Primary, CircleShape)
                )
            }
        }
    }
}

/**
 * 录制中UI - 显示时长、暂停/恢复、停止按钮
 */
@Composable
private fun RecordingState(
    modifier: Modifier = Modifier,
    duration: Int,
    isPaused: Boolean,
    onPause: () -> Unit,
    onResume: () -> Unit,
    onStop: () -> Unit
) {
    Column(
        modifier = modifier.padding(bottom = 50.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        // 录制时长显示
        Card(
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.7f)
            )
        ) {
            Row(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 录制指示点
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(Color.Red, CircleShape)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = formatDuration(duration),
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }

        // 停止按钮 - 方形样式
        IconButton(
            onClick = onStop,
            modifier = Modifier
                .size(80.dp)
                .background(Primary, CircleShape),
            enabled = duration >= 10 // 至少录制10秒才能停止
        ) {
            Box(
                modifier = Modifier
                    .size(30.dp)
                    .background(Color.White, RoundedCornerShape(4.dp))
            )
        }

        // 最少录制时长提示
        Text(
            text = stringResource(R.string.at_least_10_seconds_of_recording_is_required),
            color = Color.White,
            fontSize = 12.sp,
            modifier = Modifier
                .background(
                    Color.Black.copy(alpha = 0.6f), RoundedCornerShape(12.dp)
                )
                .padding(horizontal = 12.dp, vertical = 6.dp)
        )
    }
}

/**
 * 中央播放按钮组件
 */
@Composable
private fun PlayButton(
    modifier: Modifier = Modifier, isPlaying: Boolean, onPlayPause: () -> Unit
) {
    Box(
        modifier = modifier
            .size(100.dp)
            .background(
                Color.Black.copy(alpha = 0.7f), CircleShape
            )
            .clickable { onPlayPause() }, contentAlignment = Alignment.Center
    ) {
        if (isPlaying) {
            // 暂停图标 - 两个竖条
            Row(
                horizontalArrangement = Arrangement.spacedBy(6.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .width(10.dp)
                        .height(30.dp)
                        .background(Color.White, RoundedCornerShape(3.dp))
                )
                Box(
                    modifier = Modifier
                        .width(10.dp)
                        .height(30.dp)
                        .background(Color.White, RoundedCornerShape(3.dp))
                )
            }
        } else {
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = "播放",
                tint = Color.White,
                modifier = Modifier.size(50.dp)
            )
        }
    }
}

/**
 * 录制完成UI - 显示时长信息、取消、上传按钮
 */
@Composable
private fun RecordCompletedState(
    modifier: Modifier = Modifier,
    duration: Int,
    currentPosition: Int,
    onCancel: () -> Unit,
    onUpload: () -> Unit,
    isUploading: Boolean
) {
    Column(
        modifier = modifier.padding(bottom = 50.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {

        // 录制时长显示
        Row(
            verticalAlignment = Alignment.CenterVertically, modifier = Modifier
                .background(
                    Color.Black.copy(alpha = 0.7f), RoundedCornerShape(8.dp)
                )
                .padding(horizontal = 16.dp, vertical = 8.dp)
        ) {
            Text(
                text = formatDuration(currentPosition), color = Color.White, fontSize = 14.sp
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "/", color = Color.White, fontSize = 14.sp
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = formatDuration(duration), color = Color.White, fontSize = 14.sp
            )
        }

        // 操作按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(50.dp),
            modifier = Modifier.padding(horizontal = 50.dp)
        ) {
            // 取消按钮
            AnchorOutlinedButton(
                text = stringResource(R.string.cancel),
                onClick = onCancel,
                enabled = !isUploading,
                modifier = Modifier.width(110.dp)
            )

            // 上传按钮
            AnchorButton(
                text = if (isUploading) stringResource(R.string.uploading) else stringResource(R.string.upload),
                onClick = onUpload,
                enabled = !isUploading,
                isLoading = isUploading,
                modifier = Modifier.widthIn(min = 100.dp)
            )
        }
    }
}

@Preview
@Composable
fun FaceRecordScreenPreview() {
    AnchorTheme {
        // 预览播放按钮在中央的效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
        ) {
            // 中央播放按钮
            PlayButton(
                modifier = Modifier.align(Alignment.Center), isPlaying = false, onPlayPause = { })

            // 底部控制区域
            RecordCompletedState(
                modifier = Modifier.align(Alignment.BottomCenter),
                duration = 30,
                currentPosition = 15,
                onCancel = { },
                onUpload = { },
                isUploading = false
            )
        }
    }
}

@Preview
@Composable
fun PlayButtonPreview() {
    AnchorTheme {
        Column(
            modifier = Modifier.padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 播放状态
            PlayButton(
                isPlaying = false, onPlayPause = { })

            // 暂停状态
            PlayButton(
                isPlaying = true, onPlayPause = { })
        }
    }
}


/**
 * 格式化时长显示
 */
private fun formatDuration(seconds: Int): String {
    val minutes = seconds / 60
    val remainingSeconds = seconds % 60
    return String.format("%02d:%02d", minutes, remainingSeconds)
}
