package com.mobile.anchor.app.update

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.content.FileProvider
import com.mobile.anchor.app.R
import com.mobile.anchor.app.logger.LogX
import java.io.File

/**
 * 更新通知管理器
 * 处理应用更新过程中的通知显示
 */
class UpdateNotificationManager(private val context: Context) {

    companion object {
        private const val CHANNEL_ID = "app_update_channel"
        private const val CHANNEL_NAME = "APP_UPDATE"
        private const val NOTIFICATION_ID = 1001
    }

    private val notificationManager =
        context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

    init {
        createNotificationChannel()
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_LOW
            ).apply {
                description =
                    context.getString(R.string.application_update_download_progress_notification)
                setShowBadge(false)
                setSound(null, null)
            }
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 显示下载开始通知
     */
    fun showDownloadStartNotification(fileName: String) {
        if (!UpdatePermissionManager.checkNotificationPermission(context)) {
            LogX.w("没有通知权限，无法显示通知")
            return
        }

        val notification = NotificationCompat.Builder(context, CHANNEL_ID).setContentTitle("Anchor")
            .setContentText(context.getString(R.string.preparing_to_download, fileName)).setSmallIcon(R.mipmap.ic_launcher)
            .setOngoing(true).setAutoCancel(false).setProgress(0, 0, true).build()

        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    /**
     * 更新下载进度通知
     */
    fun updateDownloadProgress(progress: DownloadProgress) {
        if (!UpdatePermissionManager.checkNotificationPermission(context)) {
            return
        }

        val notification =
            NotificationCompat.Builder(context, CHANNEL_ID).setContentTitle(context.getString(R.string.anchor_downloading))
                .setContentText("${progress.progressText} - ${progress.sizeText}")
                .setSmallIcon(R.mipmap.ic_launcher).setOngoing(true).setAutoCancel(false)
                .setProgress(100, progress.progress, false).build()

        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    /**
     * 显示下载完成通知
     */
    fun showDownloadCompleteNotification(filePath: String, fileName: String) {
        if (!UpdatePermissionManager.checkNotificationPermission(context)) {
            return
        }

        val file = File(filePath)
        val installIntent = createInstallIntent(file)

        val pendingIntent = if (installIntent != null) {
            PendingIntent.getActivity(
                context,
                0,
                installIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
            )
        } else null

        val notification =
            NotificationCompat.Builder(context, CHANNEL_ID).setContentTitle(context.getString(R.string.download_completed))
                .setContentText(context.getString(R.string.click_to_install, fileName)).setSmallIcon(R.mipmap.ic_launcher)
                .setOngoing(false).setAutoCancel(true).apply {
                    if (pendingIntent != null) {
                        setContentIntent(pendingIntent)
                    }
                }.build()

        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    /**
     * 显示下载失败通知
     */
    fun showDownloadFailedNotification(error: String) {
        if (!UpdatePermissionManager.checkNotificationPermission(context)) {
            return
        }

        val notification =
            NotificationCompat.Builder(context, CHANNEL_ID).setContentTitle(context.getString(R.string.download_failed))
                .setContentText(error).setSmallIcon(R.mipmap.ic_launcher).setOngoing(false)
                .setAutoCancel(true).build()

        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    /**
     * 取消通知
     */
    fun cancelNotification() {
        notificationManager.cancel(NOTIFICATION_ID)
    }

    /**
     * 创建安装Intent
     */
    private fun createInstallIntent(file: File): Intent? {
        return try {
            val intent = Intent(Intent.ACTION_VIEW)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

            val apkUri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                FileProvider.getUriForFile(
                    context, "${context.packageName}.fileprovider", file
                )
            } else {
                android.net.Uri.fromFile(file)
            }

            intent.setDataAndType(apkUri, "application/vnd.android.package-archive")

            // 授权给所有可能的安装器
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val resolveLists = context.packageManager.queryIntentActivities(
                    intent, android.content.pm.PackageManager.MATCH_DEFAULT_ONLY
                )
                for (resolveInfo in resolveLists) {
                    val packageName = resolveInfo.activityInfo.packageName
                    context.grantUriPermission(
                        packageName,
                        apkUri,
                        Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                    )
                }
            }

            intent
        } catch (e: Exception) {
            LogX.e("创建安装Intent失败", e)
            null
        }
    }
}
