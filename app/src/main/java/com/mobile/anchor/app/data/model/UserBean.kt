package com.mobile.anchor.app.data.model

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Serializable

/**
 * 登录相关数据模型
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class UserBean(
    val login_email: String = "",
    val languages: List<LanguageBean> = emptyList(),
    val stat: Int = 0,//0注册 1已填写信息 2审核通过 3 审核不通过
    val isGuild: Boolean = false,
    val isDisable: Boolean = false,
    val registerTime: String = "",
    val lastLoginTime: String = "",
    @Json(name = "country") val country: CountryBean? = null,
    @Json(name = "media_list") val media_list: List<AlbumBean>? = null,
    @Json(name = "id") val id: String = "",
    @<PERSON><PERSON>(name = "version") val version: Int = 0,
    @Json(name = "app_id") val appId: Int = 0,
    @Json(name = "package_name") val packageName: String = "",
    @Json(name = "fansCount") val fansCount: String = "0",
    @Json(name = "followCount") val followCount: String = "0",
    @Json(name = "nickname") val nickname: String = "",
    @Json(name = "gender") val gender: Int = 0,
    @Json(name = "avatar") val avatar: String = "",
    @Json(name = "showAvatar") val showAvatar: String = "",
    @Json(name = "rongcloudToken") val rongcloudToken: String = "",
    @Json(name = "rongcloudAppID") val rongcloudAppID: String = "",
    @Json(name = "registerAt") val registerAt: String = "",
    @Json(name = "dnd_expire") val dndExpire: Long = 0,
    @Json(name = "age") val age: Int = 0,
    @Json(name = "balance") val balance: String = "",
    @Json(name = "status") val status: String = OnlineStatus.ONLINE.value,//1在线 2通话中 3离线 4勿扰
    @Json(name = "interactionsAt") val interactionsAt: String = "",
    @Json(name = "showRelation") val showRelation: String = "",//1关注 2被关注 3相互关注 4拉黑 5被拉黑
    @Json(name = "userCountry") val userCountry: CountryBean? = null,
    @Json(name = "firebase_token") val firebaseToken: String = "",
    @Json(name = "signature") val signature: String = "",
    @Json(name = "firstNoteContent") val firstNoteContent: String = "",
    @Json(name = "isRead") val isRead: String = "",
    @Json(name = "loginTimes") val loginTimes: Int = 0,
    @Json(name = "discountRate") val discountRate: Int = 0,
    @Json(name = "country_code") val countryCode: Int = 0,
    @Json(name = "is_vip") val isVip: Boolean = false,
    @Json(name = "anchor_level") val levelConfig: LevelConfigBean? = null,
    @Json(name = "level_config") val userLevelConfig: LevelConfigBean? = null, //获取用户信息返回的字段
    @Json(name = "user_vip") val userVip: UserVIPBean? = null,
    @Json(name = "register_update") val registerUpdate: Int = 0,
    @Json(name = "is_ad") val isAd: Boolean = false,
    @Json(name = "enable_notify") val enableNotify: Boolean = false,
    @Json(name = "bind_google") val bindGoogle: Boolean = false,
    @Json(name = "avail_sign") val availSign: Boolean = false,
    @Json(name = "birthday_at") val birthday_at: String = "",
    @Json(name = "bank_id") val bank_id: Int = 0,
    @Json(name = "finish_stat") val finish_stat: Int = 0,//0认证中 1认证成功 2认证失败
    @Json(name = "lang") val lang: String = "",
) : Parcelable {

    val isRegistration: Boolean get() = stat == 0

    val isVerified: Boolean get() = finish_stat == 1

    val isFollowed: Boolean get() = showRelation == "1" || showRelation == "3"

    val isBlocked: Boolean get() = showRelation == "4"

    @Parcelize
    @JsonClass(generateAdapter = true)
    data class UserVIPBean(
        @Json(name = "uid") val uid: Int = 0,
        @Json(name = "expire_at") val expireAt: Long = 0,
        @Json(name = "level") val level: Int = 0,
        @Json(name = "is_vip") val isVip: Boolean = false

    ) : Parcelable

    @Parcelize
    @JsonClass(generateAdapter = true)
    data class LevelConfigBean(
        @Json(name = "title") val title: String = "",
        @Json(name = "level") val level: Int = 0,
        @Json(name = "icon") val icon: String = "",
        @Json(name = "avatar_frame") val avatarFrame: String = "",
        @Json(name = "begin") val begin: Int = 0,
        @Json(name = "end") val end: Int = 0,
        @Json(name = "uid") val uid: Long = 0,
        @Json(name = "call_price") val call_price: Int = 0,
        @Json(name = "min_price") val min_price: Int = 0,
        @Json(name = "max_price") val max_price: Int = 0
    ) : Parcelable

    @Parcelize
    @JsonClass(generateAdapter = true)
    data class LanguageBean(
        val id: Int = 0,
        val language: String = "",
        val langKey: String = "",
        val value: String = "",
        val is_select: Boolean = false
    ) : Parcelable

    @Parcelize
    @JsonClass(generateAdapter = true)
    data class AlbumBean(
        val id: Int = 0,
        val media_type: Int = 0,//1个人资料,2私密相册信息
        val url: String = "", val resource_type: Int = 0,//1图片2视频
        val media_status: Int = 0,//审核状态 1未审核 2人审成功 3人审不成功 4机审成功 5机审不成功
        val sort: Int = 0, //顺序
    ) : Parcelable {
        val isReviewing: Boolean get() = media_status == 1
        val isApproved: Boolean get() = media_status == 2
        val isRejected: Boolean get() = media_status == 3 || media_status == 5

        enum class MediaType(val value: Int) {
            COMMON(1), PRIVATE(2)
        }
    }
}

@Parcelize
@JsonClass(generateAdapter = true)
data class CountryBean(
    val id: String = "", val title: String = "", val code: String = ""
) : Parcelable

/**
 * 登录响应
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class AuthBean(
    val accessToken: String,
    val accessExpire: Long,
    val refreshAfter: Long,
    val config: AnchorConfigBean?,
    val anchor: UserBean?
) : Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class AnchorConfigBean(
    val daily_duration: Int, val cdn: String, val agoraConfig: AgoraConfigBean,
    val withdrawMinCoin: Int,
    val withdrawMaxCoin: Int?,
    val update_type: Int,//0 不更新 1提示更新 2强制更新
    val update_url: String,
    val update_tips: String,
    val videoCallPriceMinLimit: Int,
    val videoCallPriceMaxLimit: Int,
    val list_param: String,
    val detail_photo_param: String,
    val thumb_param: String,
    val anchor_profile_photo_max_count: Int,//公开相册图片限制数量
    val anchor_profile_video_max_count: Int,//公开相册视频限制数量
    val anchor_secret_photo_max_count: Int,//私密相册图片限制数量
    val anchor_secret_video_max_count: Int,//私密相册视频限制数量
) : Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class AgoraConfigBean(val appID: String) : Parcelable


/**
 * 登录UI状态
 */
data class LoginUiState(
    val email: String = "",
    val isEmailValid: Boolean = false,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isCodeSent: Boolean = false,
    val isGoogleLoading: Boolean = false,
    val googleLoginSuccess: Boolean = false,
    val googleBindSuccess: Boolean = false
)

/**
 * 邮箱验证UI状态
 */
data class EmailVerificationUiState(
    val email: String = "", // 移除硬编码的邮箱
    val verificationCode: String = "",
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val timeRemaining: Int = 300, // 5分钟倒计时
    val canResendCode: Boolean = false,
    val isLoginSuccessful: Boolean = false
)

/**
 * 更新用户资料响应
 */
@Serializable
data class UpdateProfileResponse(
    val success: Boolean = true, val message: String = ""
)
