package com.mobile.anchor.app.ui.screens.settings

import android.content.Context
import android.content.Intent
import androidx.lifecycle.viewModelScope
import com.bdc.android.library.cache.CacheManager
import com.mobile.anchor.app.data.model.VersionBean
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.repository.CommonRepository
import com.mobile.anchor.app.data.service.UserApiService
import com.mobile.anchor.app.lifecycle.AppLifecycleObserver
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.socket.WebSocketManager
import com.mobile.anchor.app.ui.activities.LoginActivity
import com.mobile.anchor.app.ui.viewmodels.BaseViewModel
import com.mobile.anchor.app.utils.ContextHolder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.math.pow

/**
 * 设置页面ViewModel
 */
class SettingsViewModel : BaseViewModel() {

    private val userApiService: UserApiService by NetDelegates()

    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

    init {
        calculateCacheSize()
        // 初始化悬浮窗开关状态
        loadOverlayWindowState()
        // 初始化全局通知开关状态
        loadGlobalNotificationState()
    }

    /**
     * 加载悬浮窗开关状态
     */
    private fun loadOverlayWindowState() {
        val isEnabled = AppLifecycleObserver.isMessageBadgeEnabled()
        _uiState.value = _uiState.value.copy(isFloatingWindowEnabled = isEnabled)
    }

    /**
     * 切换悬浮窗开关状态
     */
    fun toggleFloatingWindow(enabled: Boolean) {
        AppLifecycleObserver.setMessageBadgeEnabled(enabled)
        _uiState.value = _uiState.value.copy(isFloatingWindowEnabled = enabled)
    }

    /**
     * 加载全局通知开关状态
     */
    private fun loadGlobalNotificationState() {
        viewModelScope.launch {
            try {
                val isEnabled = DataStoreManager.isGlobalNotificationEnabled()
                _uiState.value = _uiState.value.copy(isGlobalNotificationEnabled = isEnabled)
            } catch (e: Exception) {
                LogX.e("SettingsViewModel, 加载全局通知状态失败: ${e.message}", e)
            }
        }
    }

    /**
     * 切换全局通知开关状态
     */
    fun toggleGlobalNotification(enabled: Boolean) {
        viewModelScope.launch {
            try {
                DataStoreManager.setGlobalNotificationEnabled(enabled)
                _uiState.value = _uiState.value.copy(isGlobalNotificationEnabled = enabled)
                LogX.d("SettingsViewModel", "全局通知开关设置为: $enabled")
            } catch (e: Exception) {
                LogX.e("SettingsViewModel, 设置全局通知开关失败: ${e.message}", e)
            }
        }
    }

    /**
     * 计算缓存大小
     */
    private fun calculateCacheSize() {
        viewModelScope.launch {
            try {
                val cacheSize = withContext(Dispatchers.IO) {
                    getCacheSize()
                }
                _uiState.value = _uiState.value.copy(cacheSize = formatFileSize(cacheSize))
            } catch (e: Exception) {
                LogX.e("计算缓存大小失败", e)
                _uiState.value = _uiState.value.copy(cacheSize = "0 B")
            }
        }
    }

    /**
     * 获取缓存大小（字节）
     */
    private fun getCacheSize(): Long {
        var totalSize = 0L
        try {
            // 获取应用的缓存目录
            val context = ContextHolder.context.applicationContext

            var internalCacheSize = 0L
            var externalCacheSize = 0L
            var tempFilesSize = 0L

            // 内部缓存目录
            context.cacheDir?.let { cacheDir ->
                internalCacheSize = getFolderSize(cacheDir)
                totalSize += internalCacheSize
            }

            // 外部缓存目录
            context.externalCacheDir?.let { externalCacheDir ->
                externalCacheSize = getFolderSize(externalCacheDir)
                totalSize += externalCacheSize
            }

            // 只计算临时文件和缓存相关文件，不包括整个files目录
            context.filesDir?.let { filesDir ->
                val tempFiles = filesDir.listFiles()?.filter { file ->
                    file.name.contains("temp", ignoreCase = true) || file.name.contains(
                        "cache", ignoreCase = true
                    ) || file.name.endsWith(".tmp") || file.name.endsWith(".log") || file.name.startsWith(
                        "image_cache"
                    ) || file.name.startsWith("video_cache")
                } ?: emptyList()

                tempFilesSize = tempFiles.sumOf {
                    if (it.isDirectory) getFolderSize(it) else it.length()
                }
                totalSize += tempFilesSize
            }

            LogX.d(
                "缓存大小计算详情: \n 内部缓存: ${formatFileSize(internalCacheSize)} \n 外部缓存: ${
                    formatFileSize(
                        externalCacheSize
                    )
                } \n 临时文件: ${formatFileSize(tempFilesSize)} \n 总计: ${formatFileSize(totalSize)}"
            )
        } catch (e: Exception) {
            LogX.e("获取缓存大小失败", e)
        }
        return totalSize
    }

    /**
     * 计算文件夹大小
     */
    private fun getFolderSize(folder: File): Long {
        var size = 0L
        try {
            if (folder.exists() && folder.isDirectory) {
                folder.listFiles()?.forEach { file ->
                    size += if (file.isDirectory) {
                        getFolderSize(file)
                    } else {
                        file.length()
                    }
                }
            }
        } catch (e: Exception) {
            LogX.e("计算文件夹大小失败: ${folder.path}", e)
        }
        return size
    }

    /**
     * 格式化文件大小
     */
    private fun formatFileSize(size: Long): String {
        if (size <= 0) return "0 B"

        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        val digitGroups = (Math.log10(size.toDouble()) / Math.log10(1024.0)).toInt()

        return String.format(
            "%.2f %s", size / 1024.0.pow(digitGroups.toDouble()), units[digitGroups]
        )
    }

    /**
     * 清理缓存
     */
    fun clearCache(context: Context) {
        viewModelScope.launch {
            try {
                // 显示清理中状态
                _uiState.value = _uiState.value.copy(isLoading = true)

                withContext(Dispatchers.IO) {
                    var deletedSize = 0L

                    // 清理内部缓存目录内容
                    context.cacheDir?.let { cacheDir ->
                        if (cacheDir.exists()) {
                            val sizeBefore = getFolderSize(cacheDir)
                            deleteFolderContents(cacheDir)
                            val sizeAfter = getFolderSize(cacheDir)
                            deletedSize += (sizeBefore - sizeAfter)
                            LogX.d("内部缓存目录清理完成，删除: ${formatFileSize(sizeBefore - sizeAfter)}")
                        }
                    }

                    // 清理外部缓存目录内容
                    context.externalCacheDir?.let { externalCacheDir ->
                        if (externalCacheDir.exists()) {
                            val sizeBefore = getFolderSize(externalCacheDir)
                            deleteFolderContents(externalCacheDir)
                            val sizeAfter = getFolderSize(externalCacheDir)
                            deletedSize += (sizeBefore - sizeAfter)
                            LogX.d("外部缓存目录清理完成，删除: ${formatFileSize(sizeBefore - sizeAfter)}")
                        }
                    }

                    // 清理临时文件
                    context.filesDir?.let { filesDir ->
                        val tempFiles = filesDir.listFiles()?.filter { file ->
                            file.name.contains(
                                "temp", ignoreCase = true
                            ) || file.name.contains(
                                "cache", ignoreCase = true
                            ) || file.name.endsWith(".tmp") || file.name.endsWith(".log") || file.name.startsWith(
                                "image_cache"
                            ) || file.name.startsWith("video_cache")
                        } ?: emptyList()

                        tempFiles.forEach { file ->
                            val sizeBefore =
                                if (file.isDirectory) getFolderSize(file) else file.length()
                            if (file.isDirectory) {
                                deleteFolderContents(file)
                                file.delete()
                            } else {
                                file.delete()
                            }
                            deletedSize += sizeBefore
                        }
                        LogX.d(
                            "临时文件清理完成，删除: ${
                                formatFileSize(tempFiles.sumOf {
                                    if (it.isDirectory) getFolderSize(
                                        it
                                    ) else it.length()
                                })
                            }"
                        )
                    }

                    // 清理MMKV缓存
                    LogX.d("MMKV缓存清理完成")
                    LogX.d("总共删除缓存: ${formatFileSize(deletedSize)}")
                }

                LogX.d("所有缓存清理完成")

                // 重新计算缓存大小
                calculateCacheSize()

                // 清除加载状态
                _uiState.value = _uiState.value.copy(isLoading = false)

            } catch (e: Exception) {
                LogX.e("清理缓存失败", e)
                _uiState.value =
                    _uiState.value.copy(isLoading = false, error = "清理缓存失败: ${e.message}")
                // 即使失败也要重新计算缓存大小
                calculateCacheSize()
            }
        }
    }

    /**
     * 删除文件夹内容（保留文件夹本身）
     */
    private fun deleteFolderContents(folder: File) {
        try {
            if (folder.exists() && folder.isDirectory) {
                folder.listFiles()?.forEach { child ->
                    if (child.isDirectory) {
                        deleteFolderContents(child)
                        child.delete() // 删除空文件夹
                    } else {
                        child.delete() // 删除文件
                    }
                }
            }
        } catch (e: Exception) {
            LogX.e("删除文件夹内容失败: ${folder.path}", e)
        }
    }

    /**
     * 退出登录
     */
    fun logout(context: Context, onLogoutComplete: () -> Unit) {
        ktHttpRequest {
            try {
                // 清除DataStore中的用户数据
                DataStoreManager.clearUserData()

                WebSocketManager.getInstance().release()

                LogX.d("用户数据已清除")

                // 重启到登录页面
                val intent = Intent(context, LoginActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                }
                context.startActivity(intent)

                onLogoutComplete()

                userApiService.userLogout().await()
                // 清除用户数据和令牌
                DataStoreManager.clear()
            } catch (e: Exception) {
                LogX.e("退出登录失败", e)
                // 即使失败也要执行回调
                onLogoutComplete()
            }
        }
    }
}

/**
 * 设置页面UI状态
 */
data class SettingsUiState(
    val cacheSize: String = "0 B",
    val isLoading: Boolean = false,
    val error: String? = null,
    val isFloatingWindowEnabled: Boolean = true,
    val isGlobalNotificationEnabled: Boolean = true,
)
