package com.mobile.anchor.app.utils

import android.content.Context
import android.widget.ImageView
import com.bdc.android.library.imageloader.ImageLoader
import com.luck.picture.lib.engine.ImageEngine
import kotlin.let

class GlideImageEngine : ImageEngine {
    override fun loadImage(context: Context?, url: String?, imageView: ImageView?) {
        if (imageView != null) {
            context?.let { ImageLoader.with(it) }?.load(url)?.into(imageView)
        }
    }

    override fun loadImage(
        context: Context?, imageView: ImageView?, url: String?, maxWidth: Int, maxHeight: Int
    ) {
        if (imageView != null) {
            context?.let { ImageLoader.with(it) }?.load(url)?.override(maxWidth, maxHeight)
                ?.into(imageView)
        }
    }

    override fun loadAlbumCover(context: Context?, url: String?, imageView: ImageView?) {
        if (imageView != null) {
            context?.let { ImageLoader.with(it) }?.load(url)?.into(imageView)
        }
    }

    override fun loadGridImage(context: Context?, url: String?, imageView: ImageView?) {
        if (imageView != null) {
            context?.let { ImageLoader.with(it) }?.load(url)?.into(imageView)
        }
    }

    override fun pauseRequests(context: Context?) {
    }

    override fun resumeRequests(context: Context?) {
    }
}