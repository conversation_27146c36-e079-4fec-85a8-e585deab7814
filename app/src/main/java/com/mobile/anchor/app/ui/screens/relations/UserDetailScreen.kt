package com.mobile.anchor.app.ui.screens.relations

import android.os.Bundle
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bdc.android.library.extension.jump
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.OnlineStatus
import com.mobile.anchor.app.data.model.RelationAction
import com.mobile.anchor.app.data.model.UserBean
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.AsyncImageComponent
import com.mobile.anchor.app.ui.components.BottomSelectDialog
import com.mobile.anchor.app.ui.components.CircleAvatar
import com.mobile.anchor.app.ui.components.OnlineStatusText
import com.mobile.anchor.app.ui.components.StateView
import com.mobile.anchor.app.ui.conversation.MyRongConversationActivity
import com.mobile.anchor.app.ui.lce.PageState
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Background
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.theme.Red
import com.mobile.anchor.app.ui.view.LevelLabelView
import io.rong.imlib.model.Conversation

/**
 * 好友详情页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserDetailScreen(
    userId: String, viewModel: RelationViewModel = viewModel()
) {
    val userDetail by viewModel.userDetail.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val viewState by viewModel.viewState.observeAsState(PageState.Default)

    var showActionDialog by remember { mutableStateOf(false) }

    // 加载用户详情
    LaunchedEffect(userId) {
        if (userId.isNotEmpty()) {
            viewModel.getUserDetail(userId)
        }
    }

    // 清理数据
    DisposableEffect(Unit) {
        onDispose {
            viewModel.clearUserDetail()
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 背景图片（头像高斯模糊）
        AsyncImageComponent(
            imageUrl = userDetail?.showAvatar,
            contentDescription = "Background",
            modifier = Modifier
                .fillMaxHeight(0.5F)
                .blur(20.dp),
            contentScale = ContentScale.Crop,
            placeholder = R.mipmap.ic_default_avatar,
            error = R.mipmap.ic_default_avatar
        )

        AnchorScaffold(
            topBar = {
                AnchorTopBar(
                    title = userDetail?.nickname ?: "",
                    containerColor = Color.Transparent,
                    actions = {
                        IconButton(onClick = {
                            showActionDialog = true
                        }) {
                            Icon(
                                imageVector = Icons.Default.MoreVert,
                                contentDescription = "更多",
                                tint = Color.White
                            )
                        }
                    })
            }, containerColor = Color.Transparent
        ) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                when {
                    isLoading -> {
                        StateView(
                            state = PageState.Loading,
                            loadingMessage = stringResource(R.string.loading_user_details)
                        )
                    }

                    userDetail == null -> {
                        StateView(
                            state = PageState.Error(Exception(stringResource(R.string.user_not_found))),
                            errorTitle = stringResource(R.string.user_not_found),
                            errorMessage = stringResource(R.string.the_user_you_re_looking_for_doesn_t_exist),
                            onRetry = {
                                viewModel.getUserDetail(userId)
                            })
                    }

                    else -> {
                        UserDetailContent(
                            user = userDetail!!, relationAction = { p0, p1 ->
                                viewModel.relateUser(
                                    p0, p1
                                )
                            }, modifier = Modifier
                                .fillMaxHeight(0.6f)
                                .align(Alignment.BottomCenter)
                        )
                    }
                }
            }
        }
    }

    ActionSelectDialog(
        visible = showActionDialog,
        user = userDetail,
        onDismiss = { showActionDialog = false },
        onActionSelected = { action ->
            userDetail?.let { user ->
                when (action) {
                    "Block" -> {
                        viewModel.blockUser(user.id, true)
                    }

                    "Unblock" -> {
                        viewModel.blockUser(user.id, false)
                    }
                }
            }
            showActionDialog = false
        })
}

@Composable
private fun UserDetailContent(
    user: UserBean, relationAction: (RelationAction, String) -> Unit, modifier: Modifier = Modifier
) {
    ConstraintLayout(
        modifier = modifier.background(Background)
    ) {
        val (avatar, nickname, level, country, status, id, follow, action, button) = createRefs()

        CircleAvatar(
            imageUrl = user.showAvatar,
            contentDescription = "用户头像",
            modifier = Modifier
                .constrainAs(avatar) {
                    start.linkTo(parent.start, margin = 20.dp)
                }
                .size(120.dp)
                .offset(y = (-60).dp)
                .clip(CircleShape))

        // 用户名
        Text(
            text = user.nickname,
            color = Color.White,
            fontSize = 24.sp,
            maxLines = 1,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.constrainAs(nickname) {
                start.linkTo(avatar.start, margin = 0.dp)
                top.linkTo(avatar.bottom, margin = -50.dp)
            })

        AndroidView(modifier = Modifier.constrainAs(level) {
            top.linkTo(nickname.top)
            bottom.linkTo(nickname.bottom)
            start.linkTo(nickname.end, margin = 5.dp)
        }, factory = { ctx ->
            LevelLabelView(ctx)
        }, update = {
            it.current = user.userLevelConfig?.level ?: 1
        })

        Text(
            user.userCountry?.code ?: "",
            style = MaterialTheme.typography.labelSmall,
            color = Color.White,
            modifier = Modifier
                .constrainAs(country) {
                    start.linkTo(level.end, 5.dp)
                    top.linkTo(level.top)
                    bottom.linkTo(level.bottom)
                }
                .background(
                    color = Color(0xff422574), shape = RoundedCornerShape(50)
                )
                .padding(horizontal = 6.dp, vertical = 2.dp))

        OnlineStatusText(
            OnlineStatus.fromValue(user.status), modifier = Modifier.constrainAs(status) {
                start.linkTo(country.end, margin = 5.dp)
                top.linkTo(country.top)
                bottom.linkTo(country.bottom)
            })

        Text(
            text = "ID: ${user.id}",
            color = Color.White.copy(alpha = 0.7f),
            fontSize = 14.sp,
            modifier = Modifier.constrainAs(id) {
                start.linkTo(nickname.start)
                top.linkTo(nickname.bottom, margin = 5.dp)
            })

        // 关注/粉丝数据
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .constrainAs(follow) {
                    top.linkTo(id.bottom, margin = 10.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }, horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            FollowStatsItem(
                title = stringResource(R.string.followers),
                modifier = Modifier.weight(1F),
                count = user.followCount,
                background = R.mipmap.bg_detail_follower
            )
            Spacer(Modifier.width(10.dp))
            FollowStatsItem(
                title = stringResource(R.string.following),
                modifier = Modifier.weight(1F),
                count = user.fansCount,
                background = R.mipmap.bg_detail_following
            )
        }

        IconButton(onClick = {
            relationAction.invoke(
                if (user.isFollowed) RelationAction.UNFOLLOW else RelationAction.FOLLOW, user.id
            )
        }, modifier = Modifier
            .constrainAs(action) {
                bottom.linkTo(nickname.bottom)
                top.linkTo(nickname.top)
                end.linkTo(parent.end, margin = 10.dp)
            }
            .offset(y = (-50).dp)) {
            Icon(
                imageVector = if (user.isFollowed) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                contentDescription = null,
                tint = if (user.isFollowed) Red else Color.White,
                modifier = Modifier.size(40.dp)
            )
        }

        val activity = LocalActivity.current
        Button(
            onClick = {
            activity?.jump(MyRongConversationActivity::class.java, Bundle().apply {
                putString("targetId", user.id)
                putString(
                    "ConversationType", Conversation.ConversationType.PRIVATE.name
                )
            })
        },
            modifier = Modifier
                .constrainAs(button) {
                    start.linkTo(parent.start, margin = 16.dp)
                    end.linkTo(parent.end, margin = 16.dp)
                    bottom.linkTo(parent.bottom, 20.dp)
                }
                .fillMaxWidth()
                .padding(bottom = 20.dp, start = 16.dp, end = 16.dp)
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Primary
            ),
            shape = RoundedCornerShape(28.dp)) {
            Image(
                painter = painterResource(R.mipmap.ic_anchor_message),
                contentDescription = "发送消息",
                modifier = Modifier.size(24.dp),
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = stringResource(R.string.send_message),
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun FollowStatsItem(
    modifier: Modifier = Modifier, title: String, count: String, background: Int
) {
    Card(
        modifier = modifier.height(75.dp), colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ), shape = RoundedCornerShape(12.dp)
    ) {
        Box {
            Image(
                painter = painterResource(background),
                contentDescription = "",
                modifier = Modifier
                    .fillMaxSize()
                    .align(Alignment.CenterEnd)
            )
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(start = 16.dp),
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = count,
                    color = Color.White,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = title, color = Color.White.copy(alpha = 0.7f), fontSize = 12.sp
                )
            }
        }
    }
}


@Composable
private fun ActionSelectDialog(
    visible: Boolean, user: UserBean?, onDismiss: () -> Unit, onActionSelected: (String) -> Unit
) {
    // 根据用户的拉黑状态决定显示的选项
    val options = if (user?.isBlocked == true) {
        // 已拉黑，显示取消拉黑选项
        listOf("Unblock")
    } else {
        // 未拉黑，显示拉黑选项
        listOf("Block")
    }

    BottomSelectDialog(
        visible = visible,
        title = stringResource(R.string.more),
        options = options,
        onDismiss = onDismiss,
        onOptionSelected = { _, action ->
            onActionSelected(action)
        })
}

@Preview
@Composable
fun UserDetailContentPreview() {
    AnchorTheme {
        UserDetailContent(
            modifier = Modifier.fillMaxHeight(), user = UserBean(
                avatar = "https://api.dicebear.com/7.x/micah/png?seed=test",
                nickname = "Nickname",
                id = "123456",
                fansCount = "100",
                followCount = "99"
            ), relationAction = { _, _ -> })
    }
}
