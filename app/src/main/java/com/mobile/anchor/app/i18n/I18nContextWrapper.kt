package com.mobile.anchor.app.i18n

import android.content.Context
import android.content.ContextWrapper
import android.content.res.Resources
import com.mobile.anchor.app.logger.LogX

/**
 * Copyright 2025 武汉摆渡船科技有限公司
 * @author: Hechao
 * @date: 2025/6/25 20:52
 * @description: 基于DataStore的Context包装器
 */
class I18nContextWrapper(base: Context) : ContextWrapper(base) {

    init {
        LogX.d("LangContextWrapper", "LangContextWrapper created for: ${base.javaClass.simpleName}")
    }

    override fun getResources(): Resources {
        val baseResources = super.getResources()
        return object : Resources(
            baseResources.assets,
            baseResources.displayMetrics,
            baseResources.configuration
        ) {

            /**
             * 处理转义序列，将字面字符串转换为实际的转义字符
             */
            private fun processEscapeSequences(text: String): String {
                return text
                    .replace("\\n", "\n")        // 换行符
                    .replace("\\t", "\t")        // 制表符
                    .replace("\\r", "\r")        // 回车符
                    .replace("\\\"", "\"")       // 双引号
                    .replace("\\'", "'")         // 单引号
                    .replace("\\\\", "\\")       // 反斜杠
            }

            /**
             * 通用的字符串翻译方法
             */
            private fun getTranslatedString(id: Int, defaultValue: String): String {
                return try {
                    val key = baseResources.getResourceEntryName(id)
                    if (I18nManager.has(key)) {
                        val translation = I18nManager.get(key)
                        val processedTranslation = processEscapeSequences(translation)
                        processedTranslation
                    } else {
                        defaultValue
                    }
                } catch (e: Exception) {
                    LogX.e(
                        "LangContextWrapper",
                        "Failed to get translation for resource id $id: ${e.message}"
                    )
                    defaultValue
                }
            }

            override fun getResourceEntryName(resId: Int): String =
                baseResources.getResourceEntryName(resId)

            override fun getString(id: Int): String {
                val defaultValue = baseResources.getString(id)
                return getTranslatedString(id, defaultValue)
            }

            override fun getString(id: Int, vararg formatArgs: Any?): String {
                val key = try {
                    getResourceEntryName(id)
                } catch (e: Exception) {
                    "unknown_$id"
                }

                return try {
                    // 优先从I18n管理器获取翻译
                    if (I18nManager.has(key)) {
                        val template = I18nManager.get(key)
                        // 使用String.format处理格式化参数
                        val formattedText = String.format(template, *formatArgs)
                        val processedText = processEscapeSequences(formattedText)
                        processedText
                    } else {
                        // 回退到系统资源
                        baseResources.getString(id, *formatArgs)
                    }
                } catch (e: Exception) {
                    // 发生异常时回退到系统资源
                    LogX.e("LangContextWrapper", "🔄 Format error for '$key': ${e.message}")
                    baseResources.getString(id, *formatArgs)
                }
            }

            // 重写getText方法，这个方法在XML解析时经常被调用
            override fun getText(id: Int): CharSequence {
                val defaultValue = baseResources.getText(id).toString()
                return getTranslatedString(id, defaultValue)
            }

            override fun getText(id: Int, def: CharSequence?): CharSequence {
                val defaultValue =
                    baseResources.getText(id, def)?.toString() ?: def?.toString() ?: ""
                return getTranslatedString(id, defaultValue)
            }

            // 重写更多可能被 XML 解析器调用的方法
            override fun getStringArray(id: Int): Array<String> {
                return baseResources.getStringArray(id)
            }

            override fun getTextArray(id: Int): Array<CharSequence> {
                return baseResources.getTextArray(id)
            }

            override fun getQuantityString(id: Int, quantity: Int): String {
                return baseResources.getQuantityString(id, quantity)
            }

            override fun getQuantityString(
                id: Int, quantity: Int, vararg formatArgs: Any?
            ): String {
                return baseResources.getQuantityString(id, quantity, *formatArgs)
            }

            override fun getQuantityText(id: Int, quantity: Int): CharSequence {
                return baseResources.getQuantityText(id, quantity)
            }

            // 重写所有其他重要的 Resources 方法，直接委托给 baseResources
            override fun getValue(
                id: Int, outValue: android.util.TypedValue?, resolveRefs: Boolean
            ) {
                baseResources.getValue(id, outValue, resolveRefs)
            }

            override fun getValueForDensity(
                id: Int, density: Int, outValue: android.util.TypedValue?, resolveRefs: Boolean
            ) {
                baseResources.getValueForDensity(id, density, outValue, resolveRefs)
            }

            override fun obtainTypedArray(id: Int): android.content.res.TypedArray {
                return baseResources.obtainTypedArray(id)
            }

            override fun getDimension(id: Int): Float {
                return baseResources.getDimension(id)
            }

            override fun getDimensionPixelOffset(id: Int): Int {
                return baseResources.getDimensionPixelOffset(id)
            }

            override fun getDimensionPixelSize(id: Int): Int {
                return baseResources.getDimensionPixelSize(id)
            }

            override fun getColor(id: Int): Int {
                return baseResources.getColor(id)
            }

            override fun getColorStateList(id: Int): android.content.res.ColorStateList {
                return baseResources.getColorStateList(id)
            }

            override fun getDrawable(id: Int): android.graphics.drawable.Drawable {
                return baseResources.getDrawable(id)
            }

            override fun getBoolean(id: Int): Boolean {
                return baseResources.getBoolean(id)
            }

            override fun getInteger(id: Int): Int {
                return baseResources.getInteger(id)
            }

            override fun getIntArray(id: Int): IntArray {
                return baseResources.getIntArray(id)
            }

            override fun getXml(id: Int): android.content.res.XmlResourceParser {
                return baseResources.getXml(id)
            }

            override fun openRawResource(id: Int): java.io.InputStream {
                return baseResources.openRawResource(id)
            }

            override fun openRawResource(
                id: Int, value: android.util.TypedValue?
            ): java.io.InputStream {
                return baseResources.openRawResource(id, value)
            }

            override fun openRawResourceFd(id: Int): android.content.res.AssetFileDescriptor {
                return baseResources.openRawResourceFd(id)
            }

            override fun getResourceName(resid: Int): String {
                return baseResources.getResourceName(resid)
            }

            override fun getResourcePackageName(resid: Int): String {
                return baseResources.getResourcePackageName(resid)
            }

            override fun getResourceTypeName(resid: Int): String {
                return baseResources.getResourceTypeName(resid)
            }

            override fun getIdentifier(name: String?, defType: String?, defPackage: String?): Int {
                return baseResources.getIdentifier(name, defType, defPackage)
            }
        }
    }
}