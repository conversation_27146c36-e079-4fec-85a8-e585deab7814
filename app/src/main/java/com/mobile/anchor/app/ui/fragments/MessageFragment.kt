package com.mobile.anchor.app.ui.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import com.mobile.anchor.app.ui.screens.message.MessageScreen
import com.mobile.anchor.app.ui.theme.AnchorTheme

/**
 * 消息Fragment
 * 使用Compose实现UI
 */
class MessageFragment : Fragment() {

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                AnchorTheme {
                    MessageScreen()
                }
            }
        }
    }

    companion object {
        fun newInstance(): MessageFragment {
            return MessageFragment()
        }
    }
}
