package com.mobile.anchor.app.ui.conversation

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import com.mobile.anchor.app.ui.screens.followfans.FollowFansScreen
import com.mobile.anchor.app.ui.theme.AnchorTheme

/**
 * 好友Fragment
 * 使用Compose实现UI
 */
class FollowOrFansFragment : Fragment() {
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                AnchorTheme {
                    FollowFansScreen()
                }
            }
        }
    }
    
    companion object {
        fun newInstance(): FollowOrFansFragment {
            return FollowOrFansFragment()
        }
    }
}
