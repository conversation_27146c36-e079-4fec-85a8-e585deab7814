package com.mobile.anchor.app.ui.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.mobile.anchor.app.ui.screens.relations.RelationScreen
import com.mobile.anchor.app.ui.theme.AnchorTheme

/**
 * 好友Fragment
 * 使用Compose实现UI
 */
class RelationFragment : Fragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                AnchorTheme {
                    RelationFragmentContent()
                }
            }
        }
    }

    @androidx.compose.runtime.Composable
    private fun RelationFragmentContent() {
        val navController = rememberNavController()

        NavHost(
            navController = navController,
            startDestination = "relation_main"
        ) {
            // 好友主页面
            composable("relation_main") {
                RelationScreen()
            }

            // 用户详情页面
//            composable(
//                route = Screen.UserDetail.route,
//                arguments = listOf(
//                    navArgument("userId") { type = NavType.StringType }
//                )
//            ) { backStackEntry ->
//                val userId = backStackEntry.arguments?.getString("userId") ?: ""
//                FriendDetailScreen(
//                    userId = userId,
//                    navController = navController
//                )
//            }
        }
    }

    companion object {
        fun newInstance(): RelationFragment {
            return RelationFragment()
        }
    }
}
