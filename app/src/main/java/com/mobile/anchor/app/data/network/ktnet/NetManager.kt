package com.mobile.anchor.app.data.network.ktnet

import android.annotation.SuppressLint
import com.bdc.android.library.ktnet.factory.ApiResultCallAdapterFactory
import com.bdc.android.library.ktnet.interception.LogInterceptor
import com.bdc.android.library.utils.Logger
import com.mobile.anchor.app.BuildConfig
import com.mobile.anchor.app.data.network.DetailedLogInterceptor
import com.mobile.anchor.app.data.network.ktnet.interception.HeadInterceptor
import com.squareup.moshi.Moshi
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.moshi.MoshiConverterFactory
import java.security.SecureRandom
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.HostnameVerifier
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSession
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.X509TrustManager

/**
 *
 * @Author： Lxf
 * @Time： 2022/2/17 2:50 下午
 * @description： 网络单例 retrofit + okhttp + moshi
 */
object NetManager {
    var TIME_OUT = 60
    private var debug = BuildConfig.DEBUG
    private val okHttpClient: OkHttpClient by lazy { getClient() }
    private val moshi: Moshi by lazy { getMyMoshi() }
    var baseURL = BuildConfig.HOST

    fun <S> getService(serviceClass: Class<S>, baseUrl: String): S {
        return Retrofit.Builder()
            .client(okHttpClient)
            .addCallAdapterFactory(ApiResultCallAdapterFactory())
            .addConverterFactory(MoshiConverterFactory.create(moshi))
            .baseUrl(baseUrl)
            .build()
            .create(serviceClass)
    }

    private fun getClient(): OkHttpClient {
        val builder = OkHttpClient.Builder().run {
            connectTimeout(TIME_OUT.toLong(), TimeUnit.SECONDS)
            readTimeout(TIME_OUT.toLong(), TimeUnit.SECONDS)
            writeTimeout(TIME_OUT.toLong(), TimeUnit.SECONDS)

            if (debug) {
//                addInterceptor(LogInterceptor())
            }
            addInterceptor(HeadInterceptor())

            // 添加详细日志拦截器（总是启用，用于调试和监控）
            addInterceptor(DetailedLogInterceptor())

            sslSocketFactory(createSSLSocketFactory(), TrustAllCerts())
            hostnameVerifier(TrustAllNameVerifier())
        }
        return builder.build()
    }

    private fun getMyMoshi(): Moshi {
        return Moshi.Builder().add(MoshiApiResponseTypeAdapterFactory()).build()
    }

    private fun createSSLSocketFactory(): SSLSocketFactory {
        lateinit var ssfFactory: SSLSocketFactory
        try {
            val sslFactory = SSLContext.getInstance("TLS")
            sslFactory.init(null, arrayOf(TrustAllCerts()), SecureRandom());
            ssfFactory = sslFactory.socketFactory
        } catch (e: Exception) {
            Logger.e("SSL错误：${e.message}")
        }
        return ssfFactory
    }
}

class TrustAllNameVerifier : HostnameVerifier {
    @SuppressLint("BadHostnameVerifier")
    override fun verify(hostname: String?, session: SSLSession?): Boolean = true
}

@SuppressLint("CustomX509TrustManager")
class TrustAllCerts : X509TrustManager {
    @SuppressLint("TrustAllX509TrustManager")
    override fun checkClientTrusted(chain: Array<out X509Certificate>?, authType: String?) {
    }

    @SuppressLint("TrustAllX509TrustManager")
    override fun checkServerTrusted(chain: Array<out X509Certificate>?, authType: String?) {
    }

    override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
}