# 网络框架完善说明

## 🎯 完善内容

根据后台要求，已完善以下网络框架功能：

### 1. **固定鉴权信息** 🔐
- **AuthManager**: 统一管理API鉴权信息
- **自动添加请求头**: Authorization、API-Key、Client-ID、时间戳、签名等
- **令牌管理**: 访问令牌、刷新令牌的存储和管理
- **签名机制**: 自动生成请求签名（可根据实际需求调整算法）

### 2. **固定Query参数** 📋
- **ApiParamsBuilder**: 管理所有API请求的通用参数
- **自动添加参数**: appID、cursor、deviceID、deviceModel、lang、osType、osVersion、packageName、size、uid、version
- **设备信息自动获取**: 设备ID、设备型号、系统版本等
- **用户信息管理**: UID、语言设置等

### 3. **网络配置管理** ⚙️
- **NetworkConfig**: 统一管理网络配置
- **多环境支持**: 开发、测试、预发布、生产环境
- **超时配置**: 连接、读取、写入超时时间
- **重试机制**: 重试次数和延迟配置
- **缓存管理**: 缓存开关和大小配置

## 🏗️ 架构设计

```
NetworkClient (网络客户端)
├── NetworkConfig (配置管理)
├── AuthManager (鉴权管理)
├── ApiParamsBuilder (通用参数管理)
├── OkHttp拦截器
│   ├── AuthInterceptor (鉴权拦截器)
│   ├── CommonParamsInterceptor (通用参数拦截器)
│   └── LoggingInterceptor (日志拦截器)
└── Retrofit (API服务)
```

## 📝 使用方法

### 1. 初始化配置

```kotlin
// 在Application中初始化
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 获取网络配置管理器
        val networkConfig = NetworkConfig.getInstance(this)
        
        // 设置环境（可选）
        networkConfig.setEnvironment(NetworkConfig.ENV_DEV)
        
        // 获取鉴权管理器
        val authManager = AuthManager.getInstance(this)
        
        // 设置API密钥
        authManager.setApiKey("your_api_key_here")
        
        // 获取通用参数管理器
        val apiParamsBuilder = ApiParamsBuilder
        
        // 设置用户ID（登录后）
        apiParamsBuilder.updateUID("user_123")
    }
}
```

### 2. API接口定义

```kotlin
interface ApiService {
    // 通用参数会自动添加，只需定义业务参数
    @GET("user/list")
    suspend fun getUsers(): Response<UsersResponse>
    
    @POST("user/create")
    suspend fun createUser(@Body user: CreateUserRequest): Response<UserResponse>
}
```

### 3. 网络请求

```kotlin
class UserRepository(context: Context) {
    private val networkClient = NetworkClient.getInstance(context)
    private val apiService = networkClient.apiService
    
    fun getUsers(): Flow<ApiResult<UsersResponse>> = flow {
        emit(ApiResult.Loading)
        try {
            // 通用参数和鉴权信息会自动添加
            val response = apiService.getUsers()
            emit(handleResponse(response))
        } catch (e: Exception) {
            emit(ApiResult.Error(e, handleException(e)))
        }
    }.flowOn(Dispatchers.IO)
}
```

### 4. 实际请求示例

当调用 `apiService.getUsers()` 时，实际发送的请求会是：

```
GET https://api.example.com/user/list?appID=anchor_app&cursor=&deviceID=uuid&deviceModel=Samsung%20Galaxy&lang=zh&osType=android&osVersion=13&packageName=com.mobile.anchor.app&size=20&uid=user_123&version=1.0.0

Headers:
Authorization: Bearer access_token_here
X-API-Key: your_api_key_here
X-Client-ID: anchor_client
X-Timestamp: 1703123456789
X-Signature: generated_signature
Content-Type: application/json
Accept: application/json
User-Agent: Anchor/1.0.0 (Android 13; Samsung Galaxy)
```

## 🔧 配置说明

### 环境配置
```kotlin
val networkConfig = NetworkConfig.getInstance(context)

// 设置环境
networkConfig.setEnvironment(NetworkConfig.ENV_PROD)

// 获取当前环境的API地址
val baseUrl = networkConfig.getBaseUrl()
```

### 鉴权配置
```kotlin
val authManager = AuthManager.getInstance(context)

// 设置API密钥
authManager.setApiKey("your_api_key")

// 保存登录令牌
authManager.saveTokens("access_token", "refresh_token")

// 检查登录状态
val isLoggedIn = authManager.isLoggedIn()
```

### 通用参数配置
```kotlin
val apiParamsBuilder = ApiParamsBuilder

// 更新用户ID
apiParamsBuilder.updateUID("new_user_id")

// 更新语言
apiParamsBuilder.updateLanguage("en")

// 获取当前设备ID
val deviceId = apiParamsBuilder.getCurrentDeviceID()
```

## 📋 固定参数说明

每个API请求都会自动添加以下参数：

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| appID | String | 应用ID | "anchor_app" |
| cursor | String | 分页游标 | "next_page_token" |
| deviceID | String | 设备唯一标识 | "uuid-generated" |
| deviceModel | String | 设备型号 | "Samsung Galaxy S21" |
| lang | String | 语言设置 | "zh" / "en" |
| osType | String | 操作系统类型 | "android" |
| osVersion | String | 系统版本 | "13" |
| packageName | String | 应用包名 | "com.mobile.anchor.app" |
| size | Int | 分页大小 | 20 |
| uid | String | 用户ID | "user_123" |
| version | String | 应用版本 | "1.0.0" |

## 🔒 鉴权请求头说明

每个API请求都会自动添加以下请求头：

| 请求头 | 说明 | 示例值 |
|--------|------|--------|
| Authorization | 访问令牌 | "Bearer access_token" |
| X-API-Key | API密钥 | "your_api_key" |
| X-Client-ID | 客户端ID | "anchor_client" |
| X-Timestamp | 时间戳 | "1703123456789" |
| X-Signature | 请求签名 | "generated_signature" |
| Content-Type | 内容类型 | "application/json" |
| Accept | 接受类型 | "application/json" |
| User-Agent | 用户代理 | "Anchor/1.0.0 (Android 13; Samsung Galaxy)" |

## 🚀 优势特点

1. **自动化**: 通用参数和鉴权信息自动添加，无需手动处理
2. **统一管理**: 集中管理网络配置、鉴权信息和通用参数
3. **多环境支持**: 轻松切换开发、测试、生产环境
4. **安全性**: 自动签名机制，保证请求安全
5. **可配置**: 超时时间、重试机制等都可配置
6. **易维护**: 清晰的架构设计，便于维护和扩展

## 📝 注意事项

1. **API密钥安全**: 生产环境中应该从服务器获取API密钥，不要硬编码
2. **签名算法**: 示例中使用简单哈希，实际项目中应使用HMAC-SHA256等安全算法
3. **令牌刷新**: 可以添加自动刷新令牌的机制
4. **错误处理**: 根据实际业务需求完善错误处理逻辑
5. **日志安全**: 生产环境中注意不要记录敏感信息

这个完善的网络框架已经满足了后台的所有要求，可以直接在项目中使用。
