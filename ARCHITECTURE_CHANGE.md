# 架构重构说明

## 重构概述

项目已成功从 **纯Compose导航架构** 重构为 **混合架构**：
- **主要页面**（首页、好友、消息、我的）使用 **MainActivity + Fragment架构**
- **二级页面**（设置、用户详情等）仍使用 **Compose Screen + Navigation**

## 架构变更

### 之前的架构
```
MainActivity
└── MainFragment (Compose)
    ├── NavigationGraph (Compose Navigation)
    ├── BottomNavigationBar (Compose)
    └── Screen Composables
        ├── HomeScreen
        ├── FriendScreen
        ├── MessageScreen
        └── ProfileScreen
```

### 现在的架构
```
MainActivity (AppCompatActivity)
├── BottomNavigationView (Material Design)
└── Fragment Container
    ├── HomeFragment (Compose UI + 内部导航)
    │   ├── HomeScreen (主界面)
    │   └── SettingsScreen (二级页面)
    ├── FriendFragment (Compose UI)
    ├── MessageFragment (Compose UI)
    └── ProfileFragment (Compose UI + 内部导航)
        ├── ProfileScreen (主界面)
        └── SettingsScreen (二级页面)
```

## 主要变更

### 1. MainActivity
- 继承从 `ComponentActivity` 改为 `AppCompatActivity`
- 添加了Fragment管理逻辑
- 使用传统的BottomNavigationView替代Compose导航

### 2. Fragment架构
- 创建了4个新的Fragment：
  - `HomeFragment.kt`
  - `FriendFragment.kt`
  - `MessageFragment.kt`
  - `ProfileFragment.kt`
- 每个Fragment内部使用ComposeView来渲染对应的Screen

### 3. 删除的文件
- `MainFragment.kt` - 不再需要
- `NavigationGraph.kt` - 主导航使用Fragment切换替代
- `BottomNavigationBar.kt` - 使用Material Design组件替代

### 4. 新增的文件
- `bottom_navigation_menu.xml` - 底部导航菜单定义
- 4个Fragment类文件
- `Screen.kt` - 保留用于二级页面路由定义

### 5. 依赖变更
- 添加了AppCompat和Material Design依赖
- 保留了Compose相关依赖用于Fragment内的UI

## 优势

1. **更好的内存管理** - Fragment可以独立管理生命周期
2. **混合导航模式** - 主页面使用Fragment切换，二级页面使用Compose导航
3. **更好的状态保存** - Fragment自动处理状态保存和恢复
4. **兼容性更好** - 与传统Android开发模式兼容
5. **性能优化** - Fragment懒加载，只有显示时才创建UI
6. **保持现代化** - 二级页面仍使用Compose Screen，便于复杂UI开发

## 使用方式

### 主页面导航
应用启动后会默认显示HomeFragment，用户可以通过底部导航栏切换到不同的Fragment。

### 二级页面导航
在HomeFragment和ProfileFragment中，可以导航到设置页面等二级页面：
- 从HomeScreen点击设置按钮 → 导航到SettingsScreen
- 从ProfileScreen点击设置按钮 → 导航到SettingsScreen
- 在SettingsScreen中点击返回 → 返回到对应的主页面

## 注意事项

- **Fragment间通信**：通过MainActivity提供的方法（如`switchToProfileTab()`）
- **数据传递**：建议使用ViewModel或其他状态管理方案
- **导航一致性**：主页面使用Fragment切换，二级页面使用Compose导航
- **Screen.kt保留**：用于定义二级页面的路由和图标资源
