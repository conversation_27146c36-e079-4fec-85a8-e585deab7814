# Anchor 项目框架结构图

## 📁 完整项目结构

```
Anchor/
├── 📄 README.md                           # 项目说明文档
├── 📄 NETWORK_FRAMEWORK.md                # 网络框架说明文档
├── 📄 PROJECT_STRUCTURE.md                # 项目结构说明文档（本文件）
├── 📄 build.gradle.kts                    # 项目级构建配置
├── 📄 settings.gradle.kts                 # 项目设置
├── 📄 gradle.properties                   # Gradle 属性配置
├── 📁 gradle/
│   └── 📄 libs.versions.toml              # 依赖版本管理
│
└── 📁 app/                                # 主应用模块
    ├── 📄 build.gradle.kts                # 应用级构建配置
    ├── 📄 proguard-rules.pro              # 混淆规则
    │
    └── 📁 src/main/
        ├── 📄 AndroidManifest.xml         # 应用清单文件
        │
        ├── 📁 java/com/github/anchor/    # 主要源码目录
        │   ├── 📄 MainActivity.kt         # 主活动
        │   │
        │   ├── 📁 config/                 # 配置管理
        │   │   └── 📄 AppConfig.kt        # 应用配置常量
        │   │
        │   ├── 📁 data/                   # 数据层
        │   │   └── 📁 network/            # 网络相关
        │   │       ├── 📄 ApiResult.kt    # 网络请求结果封装
        │   │       ├── 📄 ApiService.kt   # API 接口定义
        │   │       ├── 📄 AuthManager.kt  # 鉴权管理器
        │   │       ├── 📄 CommonParams.kt # 通用参数管理
        │   │       ├── 📄 NetworkClient.kt # 网络客户端
        │   │       ├── 📄 NetworkConfig.kt # 网络配置管理
        │   │       └── 📄 NetworkRepository.kt # 网络数据仓库
        │   │
        │   ├── 📁 navigation/             # 导航相关
        │   │   ├── 📄 NavigationGraph.kt  # 导航图配置
        │   │   └── 📄 Screen.kt           # 页面路由定义
        │   │
        │   ├── 📁 ui/                     # UI层
        │   │   ├── 📁 components/         # 通用UI组件
        │   │   │   ├── 📄 AsyncImageComponent.kt # 异步图片组件
        │   │   │   └── 📄 BottomNavigationBar.kt # 底部导航栏
        │   │   │
        │   │   ├── 📁 screens/            # 页面
        │   │   │   ├── 📁 home/           # 首页
        │   │   │   │   ├── 📄 HomeScreen.kt # 首页界面
        │   │   │   │   └── 📄 HomeViewModel.kt # 首页ViewModel
        │   │   │   │
        │   │   │   ├── 📁 friend/         # 好友页
        │   │   │   │   └── 📄 FriendScreen.kt # 好友界面
        │   │   │   │
        │   │   │   ├── 📁 message/        # 消息页
        │   │   │   │   └── 📄 MessageScreen.kt # 消息界面
        │   │   │   │
        │   │   │   ├── 📁 profile/        # 个人资料页
        │   │   │   │   └── 📄 ProfileScreen.kt # 个人资料界面
        │   │   │   │
        │   │   │   └── 📁 settings/       # 设置页
        │   │   │       └── 📄 SettingsScreen.kt # 设置界面
        │   │   │
        │   │   └── 📁 theme/              # 主题相关
        │   │       ├── 📄 Color.kt        # 颜色定义
        │   │       ├── 📄 Theme.kt        # 主题配置
        │   │       └── 📄 Type.kt         # 字体配置
        │   │
        │   └── 📁 utils/                  # 工具类
        │       ├── 📄 DateTimeUtils.kt    # 日期时间工具
        │       ├── 📄 LogX.kt         # 日志工具
        │       ├── 📄 NetworkUtils.kt     # 网络状态工具
        │       ├── 📄 PermissionUtils.kt  # 权限管理工具
        │       ├── 📄 DataStoreManager.kt  # DataStore数据存储管理器
        │       └── 📄 StringUtils.kt      # 字符串工具
        │
        └── 📁 res/                        # 资源文件
            ├── 📁 drawable/               # 矢量图资源
            │   ├── 📄 ic_launcher_background.xml
            │   └── 📄 ic_launcher_foreground.xml
            │
            ├── 📁 mipmap-*/               # 应用图标资源
            │   ├── 📄 ic_launcher.webp
            │   ├── 📄 ic_launcher_round.webp
            │   ├── 📄 ic_tab_home_active.png      # 首页激活图标
            │   ├── 📄 ic_tab_home_inactive.png    # 首页未激活图标
            │   ├── 📄 ic_tab_friend_active.png    # 好友激活图标
            │   ├── 📄 ic_tab_friend_inactive.png  # 好友未激活图标
            │   ├── 📄 ic_tab_message_active.png   # 消息激活图标
            │   ├── 📄 ic_tab_message_inactive.png # 消息未激活图标
            │   ├── 📄 ic_tab_profile_active.png   # 个人激活图标
            │   └── 📄 ic_tab_profile_inactive.png # 个人未激活图标
            │
            ├── 📁 values/                 # 值资源
            │   ├── 📄 colors.xml          # 颜色资源
            │   ├── 📄 strings.xml         # 字符串资源
            │   └── 📄 themes.xml          # 主题资源
            │
            └── 📁 xml/                    # XML配置
                ├── 📄 backup_rules.xml
                └── 📄 data_extraction_rules.xml
```

## 🏗️ 架构层次说明

### 1. **配置层 (Config Layer)**
```
📁 config/
└── AppConfig.kt                    # 应用全局配置常量
```
- 统一管理应用配置常量
- 网络配置、缓存配置、主题配置等

### 2. **数据层 (Data Layer)**
```
📁 data/network/
├── ApiResult.kt                    # 网络请求结果封装
├── ApiService.kt                   # API接口定义 + 数据模型
├── AuthManager.kt                  # 鉴权管理器
├── CommonParams.kt                 # 通用参数管理器
├── NetworkClient.kt                # 网络客户端（Retrofit + OkHttp）
├── NetworkConfig.kt                # 网络配置管理器
└── NetworkRepository.kt            # 网络数据仓库
```
- **网络框架**: 完整的网络请求解决方案
- **自动化**: 鉴权信息和通用参数自动添加
- **配置管理**: 多环境支持，超时重试配置

### 3. **导航层 (Navigation Layer)**
```
📁 navigation/
├── NavigationGraph.kt              # 导航图配置
└── Screen.kt                       # 页面路由定义
```
- **类型安全**: 强类型路由定义
- **底部导航**: 四个主要页面的导航管理

### 4. **UI层 (UI Layer)**
```
📁 ui/
├── 📁 components/                  # 通用UI组件
│   ├── AsyncImageComponent.kt      # 图片加载组件
│   └── BottomNavigationBar.kt      # 底部导航栏
│
├── 📁 screens/                     # 页面模块
│   ├── home/     (首页)
│   ├── friend/   (好友)
│   ├── message/  (消息)
│   ├── profile/  (个人资料)
│   └── settings/ (设置)
│
└── 📁 theme/                       # 主题系统
    ├── Color.kt                    # 颜色定义（紫色主题 #9F2AF8）
    ├── Theme.kt                    # 主题配置（深色背景 #101321）
    └── Type.kt                     # 字体配置
```
- **Jetpack Compose**: 现代化UI框架
- **MVVM架构**: ViewModel + StateFlow
- **组件化**: 可复用的UI组件
- **主题系统**: 统一的颜色和字体管理

### 5. **工具层 (Utils Layer)**
```
📁 utils/
├── DateTimeUtils.kt                # 日期时间处理
├── LogX.kt                     # 日志管理
├── NetworkUtils.kt                 # 网络状态检测
├── PermissionUtils.kt              # 权限管理
├── DataStoreManager.kt             # 数据存储管理器
└── StringUtils.kt                  # 字符串处理
```
- **工具集合**: 常用功能的工具类
- **线程安全**: 单例模式，线程安全设计
- **易用性**: 简洁的API设计

## 🎯 核心特性

### ✅ 网络框架
- **自动鉴权**: 每个请求自动添加鉴权信息
- **通用参数**: 自动添加 appID、deviceID、uid 等固定参数
- **多环境**: 开发、测试、生产环境支持
- **错误处理**: 统一的错误处理和重试机制

### ✅ UI框架
- **现代化**: Jetpack Compose + Material3
- **响应式**: StateFlow + Flow 数据流
- **导航**: Navigation Compose 类型安全导航
- **主题**: 紫色主题 + 深色背景

### ✅ 工具集
- **日志**: 统一日志管理，支持不同级别
- **存储**: SharedPreferences 封装
- **权限**: 权限请求和管理
- **网络**: 网络状态监听
- **时间**: 日期时间格式化和计算
- **字符串**: 验证、格式化、隐藏敏感信息

### ✅ 架构设计
- **分层清晰**: Config → Data → Navigation → UI → Utils
- **职责分离**: 每层都有明确的职责
- **易于维护**: 模块化设计，便于扩展
- **无依赖注入**: 使用单例模式，符合您的偏好

## 📱 页面结构

### 底部导航 (4个主要页面)
1. **🏠 首页 (Home)** - 展示用户列表，网络状态
2. **👥 好友 (Friend)** - 好友列表，在线状态
3. **💬 消息 (Message)** - 消息列表，未读提醒
4. **👤 我的 (Profile)** - 个人信息，设置入口

### 其他页面
- **⚙️ 设置页** - 应用设置，账户管理
- **ℹ️ 关于页** - 版本信息，用户协议

这个项目框架结构完整、清晰，采用现代化的Android开发技术栈，具有良好的可维护性和扩展性。
